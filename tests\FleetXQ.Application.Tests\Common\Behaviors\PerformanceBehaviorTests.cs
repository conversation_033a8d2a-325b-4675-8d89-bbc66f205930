using FleetXQ.Application.Common.Behaviors;
using FluentAssertions;
using MediatR;
using Microsoft.Extensions.Logging;
using Moq;

namespace FleetXQ.Application.Tests.Common.Behaviors;

public class PerformanceBehaviorTests
{
    private readonly Mock<ILogger<PerformanceBehavior<TestRequest, TestResponse>>> _mockLogger;
    private readonly Mock<RequestHandlerDelegate<TestResponse>> _mockNext;

    public PerformanceBehaviorTests()
    {
        _mockLogger = new Mock<ILogger<PerformanceBehavior<TestRequest, TestResponse>>>();
        _mockNext = new Mock<RequestHandlerDelegate<TestResponse>>();
    }

    [Fact]
    public async Task Handle_WithFastRequest_ShouldLogInformation()
    {
        // Arrange
        var behavior = new PerformanceBehavior<TestRequest, TestResponse>(_mockLogger.Object, 1000); // 1 second threshold
        var request = new TestRequest { Value = "test" };
        var expectedResponse = new TestResponse { Result = "success" };

        _mockNext.Setup(x => x()).ReturnsAsync(expectedResponse);

        // Act
        var result = await behavior.Handle(request, _mockNext.Object, CancellationToken.None);

        // Assert
        result.Should().Be(expectedResponse);

        // Verify information level logging for fast requests
        VerifyLoggerCalled(LogLevel.Information, Times.Once);
        VerifyLoggerCalled(LogLevel.Warning, Times.Never);

        _mockNext.Verify(x => x(), Times.Once);
    }

    [Fact]
    public async Task Handle_WithSlowRequest_ShouldLogWarning()
    {
        // Arrange
        var behavior = new PerformanceBehavior<TestRequest, TestResponse>(_mockLogger.Object, 50); // 50ms threshold
        var request = new TestRequest { Value = "test" };
        var expectedResponse = new TestResponse { Result = "success" };

        _mockNext.Setup(x => x()).Returns(async () =>
        {
            await Task.Delay(100); // Simulate slow request (100ms > 50ms threshold)
            return expectedResponse;
        });

        // Act
        var result = await behavior.Handle(request, _mockNext.Object, CancellationToken.None);

        // Assert
        result.Should().Be(expectedResponse);

        // Verify warning level logging for slow requests
        VerifyLoggerCalled(LogLevel.Warning, Times.AtLeastOnce);

        _mockNext.Verify(x => x(), Times.Once);
    }

    [Fact]
    public async Task Handle_WithSlowRequest_ShouldLogSlowRequestDetails()
    {
        // Arrange
        var behavior = new PerformanceBehavior<TestRequest, TestResponse>(_mockLogger.Object, 50);
        var request = new TestRequest { Value = "test" };
        var expectedResponse = new TestResponse { Result = "success" };

        _mockNext.Setup(x => x()).Returns(async () =>
        {
            await Task.Delay(100);
            return expectedResponse;
        });

        // Act
        var result = await behavior.Handle(request, _mockNext.Object, CancellationToken.None);

        // Assert
        result.Should().Be(expectedResponse);

        // Verify slow request detection warning
        VerifyLoggerCalled(LogLevel.Warning, Times.AtLeastOnce, "Slow request detected");

        // Verify system state logging
        VerifyLoggerCalled(LogLevel.Warning, Times.AtLeastOnce, "system state");
    }

    [Fact]
    public async Task Handle_WithException_ShouldLogPerformanceMetrics()
    {
        // Arrange
        var behavior = new PerformanceBehavior<TestRequest, TestResponse>(_mockLogger.Object);
        var request = new TestRequest { Value = "test" };
        var exception = new InvalidOperationException("Test exception");

        _mockNext.Setup(x => x()).ThrowsAsync(exception);

        // Act & Assert
        var thrownException = await Assert.ThrowsAsync<InvalidOperationException>(() =>
            behavior.Handle(request, _mockNext.Object, CancellationToken.None));

        thrownException.Should().Be(exception);

        // Verify performance logging even for failed requests
        VerifyLoggerCalled(LogLevel.Information, Times.Once, "failed");

        _mockNext.Verify(x => x(), Times.Once);
    }

    [Fact]
    public async Task Handle_ShouldMeasureMemoryUsage()
    {
        // Arrange
        var behavior = new PerformanceBehavior<TestRequest, TestResponse>(_mockLogger.Object);
        var request = new TestRequest { Value = "test" };
        var expectedResponse = new TestResponse { Result = "success" };

        _mockNext.Setup(x => x()).ReturnsAsync(expectedResponse);

        // Act
        var result = await behavior.Handle(request, _mockNext.Object, CancellationToken.None);

        // Assert
        result.Should().Be(expectedResponse);

        // Verify memory usage is logged
        VerifyLoggerCalled(LogLevel.Information, Times.Once, "Memory:");
    }

    [Fact]
    public async Task Handle_WithHighMemoryUsage_ShouldLogWarning()
    {
        // Arrange
        var behavior = new PerformanceBehavior<TestRequest, TestResponse>(_mockLogger.Object);
        var request = new TestRequest { Value = "test" };
        var expectedResponse = new TestResponse { Result = "success" };

        _mockNext.Setup(x => x()).Returns(async () =>
        {
            // Simulate high memory usage by creating large objects
            var largeArray = new byte[15 * 1024 * 1024]; // 15MB
            await Task.Delay(10);
            return expectedResponse;
        });

        // Act
        var result = await behavior.Handle(request, _mockNext.Object, CancellationToken.None);

        // Assert
        result.Should().Be(expectedResponse);

        // Note: This test might not always trigger the high memory warning
        // depending on GC behavior, but it demonstrates the concept
    }

    [Fact]
    public async Task Handle_WithDefaultThreshold_ShouldUse500msThreshold()
    {
        // Arrange
        var behavior = new PerformanceBehavior<TestRequest, TestResponse>(_mockLogger.Object); // Default threshold
        var request = new TestRequest { Value = "test" };
        var expectedResponse = new TestResponse { Result = "success" };

        _mockNext.Setup(x => x()).Returns(async () =>
        {
            await Task.Delay(600); // Exceed default 500ms threshold
            return expectedResponse;
        });

        // Act
        var result = await behavior.Handle(request, _mockNext.Object, CancellationToken.None);

        // Assert
        result.Should().Be(expectedResponse);

        // Verify slow request warning with default threshold
        VerifyLoggerCalled(LogLevel.Warning, Times.AtLeastOnce, "Slow request detected");
        VerifyLoggerCalled(LogLevel.Warning, Times.AtLeastOnce, "500");
    }

    [Fact]
    public async Task Handle_ShouldLogElapsedTime()
    {
        // Arrange
        var behavior = new PerformanceBehavior<TestRequest, TestResponse>(_mockLogger.Object);
        var request = new TestRequest { Value = "test" };
        var expectedResponse = new TestResponse { Result = "success" };

        _mockNext.Setup(x => x()).Returns(async () =>
        {
            await Task.Delay(50);
            return expectedResponse;
        });

        // Act
        var result = await behavior.Handle(request, _mockNext.Object, CancellationToken.None);

        // Assert
        result.Should().Be(expectedResponse);

        // Verify elapsed time is logged
        VerifyLoggerCalled(LogLevel.Information, Times.Once, "ms");
    }

    [Fact]
    public async Task Handle_WithCancellationToken_ShouldPassTokenToNext()
    {
        // Arrange
        var behavior = new PerformanceBehavior<TestRequest, TestResponse>(_mockLogger.Object);
        var request = new TestRequest { Value = "test" };
        var expectedResponse = new TestResponse { Result = "success" };
        var cancellationToken = new CancellationTokenSource().Token;

        _mockNext.Setup(x => x()).ReturnsAsync(expectedResponse);

        // Act
        var result = await behavior.Handle(request, _mockNext.Object, cancellationToken);

        // Assert
        result.Should().Be(expectedResponse);
        _mockNext.Verify(x => x(), Times.Once);
    }

    [Theory]
    [InlineData(100)]
    [InlineData(500)]
    [InlineData(1000)]
    public async Task Handle_WithDifferentThresholds_ShouldRespectThreshold(long thresholdMs)
    {
        // Arrange
        var behavior = new PerformanceBehavior<TestRequest, TestResponse>(_mockLogger.Object, thresholdMs);
        var request = new TestRequest { Value = "test" };
        var expectedResponse = new TestResponse { Result = "success" };

        var delayMs = (int)(thresholdMs + 50); // Exceed threshold by 50ms
        _mockNext.Setup(x => x()).Returns(async () =>
        {
            await Task.Delay(delayMs);
            return expectedResponse;
        });

        // Act
        var result = await behavior.Handle(request, _mockNext.Object, CancellationToken.None);

        // Assert
        result.Should().Be(expectedResponse);

        // Verify slow request warning with the specific threshold
        VerifyLoggerCalled(LogLevel.Warning, Times.AtLeastOnce, "Slow request detected");
        VerifyLoggerCalled(LogLevel.Warning, Times.AtLeastOnce, thresholdMs.ToString());
    }

    [Fact]
    public async Task Handle_ShouldLogRequestName()
    {
        // Arrange
        var behavior = new PerformanceBehavior<TestRequest, TestResponse>(_mockLogger.Object);
        var request = new TestRequest { Value = "test" };
        var expectedResponse = new TestResponse { Result = "success" };

        _mockNext.Setup(x => x()).ReturnsAsync(expectedResponse);

        // Act
        var result = await behavior.Handle(request, _mockNext.Object, CancellationToken.None);

        // Assert
        result.Should().Be(expectedResponse);

        // Verify request name is logged
        VerifyLoggerCalled(LogLevel.Information, Times.Once, "TestRequest");
    }

    private void VerifyLoggerCalled(LogLevel logLevel, Times times, string? messageContains = null)
    {
        _mockLogger.Verify(
            x => x.Log(
                logLevel,
                It.IsAny<EventId>(),
                messageContains != null 
                    ? It.Is<It.IsAnyType>((v, t) => v.ToString()!.Contains(messageContains))
                    : It.IsAny<It.IsAnyType>(),
                It.IsAny<Exception>(),
                It.IsAny<Func<It.IsAnyType, Exception?, string>>()),
            times);
    }

    // Test helper classes
    public class TestRequest : IRequest<TestResponse>
    {
        public string Value { get; set; } = string.Empty;
    }

    public class TestResponse
    {
        public string Result { get; set; } = string.Empty;
    }
}
