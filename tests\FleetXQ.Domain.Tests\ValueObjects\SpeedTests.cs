using FleetXQ.Domain.ValueObjects;
using Xunit;

namespace FleetXQ.Domain.Tests.ValueObjects;

public class SpeedTests
{
    [Theory]
    [InlineData(0)]
    [InlineData(50)]
    [InlineData(120.5)]
    public void Constructor_WithValidSpeed_ShouldCreateSpeed(decimal kmh)
    {
        // Act
        var speed = new Speed(kmh);

        // Assert
        Assert.Equal(kmh, speed.KilometersPerHour);
    }

    [Fact]
    public void Constructor_WithNegativeSpeed_ShouldThrowArgumentOutOfRangeException()
    {
        // Act & Assert
        Assert.Throws<ArgumentOutOfRangeException>(() => new Speed(-1));
    }

    [Fact]
    public void MilesPerHour_ShouldConvertCorrectly()
    {
        // Arrange
        var speed = new Speed(100); // 100 km/h
        var expectedMph = 100 * 0.621371m;

        // Act
        var mph = speed.MilesPerHour;

        // Assert
        Assert.Equal(expectedMph, mph, 2);
    }

    [Fact]
    public void MetersPerSecond_ShouldConvertCorrectly()
    {
        // Arrange
        var speed = new Speed(36); // 36 km/h = 10 m/s
        var expectedMps = 10m;

        // Act
        var mps = speed.MetersPerSecond;

        // Assert
        Assert.Equal(expectedMps, mps, 2);
    }

    [Fact]
    public void Knots_ShouldConvertCorrectly()
    {
        // Arrange
        var speed = new Speed(100); // 100 km/h
        var expectedKnots = 100 * 0.539957m;

        // Act
        var knots = speed.Knots;

        // Assert
        Assert.Equal(expectedKnots, knots, 2);
    }

    [Theory]
    [InlineData(0, true)]
    [InlineData(0.1, false)]
    [InlineData(50, false)]
    public void IsStationary_ShouldReturnCorrectValue(decimal kmh, bool expectedIsStationary)
    {
        // Arrange
        var speed = new Speed(kmh);

        // Act & Assert
        Assert.Equal(expectedIsStationary, speed.IsStationary);
    }

    [Theory]
    [InlineData(50, false)]
    [InlineData(80, false)]
    [InlineData(81, true)]
    [InlineData(120, true)]
    public void IsHigh_ShouldReturnCorrectValue(decimal kmh, bool expectedIsHigh)
    {
        // Arrange
        var speed = new Speed(kmh);

        // Act & Assert
        Assert.Equal(expectedIsHigh, speed.IsHigh);
    }

    [Fact]
    public void FromMilesPerHour_WithValidValue_ShouldCreateSpeed()
    {
        // Arrange
        var mph = 62.1371m; // Approximately 100 km/h

        // Act
        var speed = Speed.FromMilesPerHour(mph);

        // Assert
        Assert.Equal(100m, speed.KilometersPerHour, 1);
    }

    [Fact]
    public void FromMilesPerHour_WithNegativeValue_ShouldThrowArgumentOutOfRangeException()
    {
        // Act & Assert
        Assert.Throws<ArgumentOutOfRangeException>(() => Speed.FromMilesPerHour(-1));
    }

    [Fact]
    public void FromMetersPerSecond_WithValidValue_ShouldCreateSpeed()
    {
        // Arrange
        var mps = 10m; // 10 m/s = 36 km/h

        // Act
        var speed = Speed.FromMetersPerSecond(mps);

        // Assert
        Assert.Equal(36m, speed.KilometersPerHour);
    }

    [Fact]
    public void FromKnots_WithValidValue_ShouldCreateSpeed()
    {
        // Arrange
        var knots = 53.9957m; // Approximately 100 km/h

        // Act
        var speed = Speed.FromKnots(knots);

        // Assert
        Assert.Equal(100m, speed.KilometersPerHour, 1);
    }

    [Fact]
    public void Zero_ShouldReturnZeroSpeed()
    {
        // Act
        var speed = Speed.Zero;

        // Assert
        Assert.Equal(0, speed.KilometersPerHour);
        Assert.True(speed.IsStationary);
    }

    [Theory]
    [InlineData(0, SpeedCategory.Stationary)]
    [InlineData(25, SpeedCategory.Low)]
    [InlineData(45, SpeedCategory.Medium)]
    [InlineData(80, SpeedCategory.High)]
    [InlineData(120, SpeedCategory.VeryHigh)]
    public void GetCategory_ShouldReturnCorrectCategory(decimal kmh, SpeedCategory expectedCategory)
    {
        // Arrange
        var speed = new Speed(kmh);

        // Act
        var category = speed.GetCategory();

        // Assert
        Assert.Equal(expectedCategory, category);
    }

    [Fact]
    public void ExceedsLimit_WithSpeedOverLimit_ShouldReturnTrue()
    {
        // Arrange
        var speed = new Speed(100);
        var speedLimit = new Speed(80);

        // Act
        var result = speed.ExceedsLimit(speedLimit);

        // Assert
        Assert.True(result);
    }

    [Fact]
    public void ExceedsLimit_WithSpeedUnderLimit_ShouldReturnFalse()
    {
        // Arrange
        var speed = new Speed(70);
        var speedLimit = new Speed(80);

        // Act
        var result = speed.ExceedsLimit(speedLimit);

        // Assert
        Assert.False(result);
    }

    [Fact]
    public void PercentageOverLimit_WithSpeedOverLimit_ShouldCalculateCorrectly()
    {
        // Arrange
        var speed = new Speed(100);
        var speedLimit = new Speed(80);
        var expectedPercentage = 25m; // 20/80 * 100 = 25%

        // Act
        var percentage = speed.PercentageOverLimit(speedLimit);

        // Assert
        Assert.Equal(expectedPercentage, percentage);
    }

    [Fact]
    public void PercentageOverLimit_WithSpeedUnderLimit_ShouldReturnZero()
    {
        // Arrange
        var speed = new Speed(70);
        var speedLimit = new Speed(80);

        // Act
        var percentage = speed.PercentageOverLimit(speedLimit);

        // Assert
        Assert.Equal(0, percentage);
    }

    [Fact]
    public void AdditionOperator_ShouldAddSpeeds()
    {
        // Arrange
        var speed1 = new Speed(50);
        var speed2 = new Speed(30);

        // Act
        var result = speed1 + speed2;

        // Assert
        Assert.Equal(80, result.KilometersPerHour);
    }

    [Fact]
    public void SubtractionOperator_ShouldSubtractSpeeds()
    {
        // Arrange
        var speed1 = new Speed(80);
        var speed2 = new Speed(30);

        // Act
        var result = speed1 - speed2;

        // Assert
        Assert.Equal(50, result.KilometersPerHour);
    }

    [Fact]
    public void SubtractionOperator_WithResultBelowZero_ShouldReturnZero()
    {
        // Arrange
        var speed1 = new Speed(30);
        var speed2 = new Speed(50);

        // Act
        var result = speed1 - speed2;

        // Assert
        Assert.Equal(0, result.KilometersPerHour);
    }

    [Fact]
    public void ComparisonOperators_ShouldWorkCorrectly()
    {
        // Arrange
        var speed1 = new Speed(50);
        var speed2 = new Speed(80);

        // Act & Assert
        Assert.True(speed2 > speed1);
        Assert.True(speed1 < speed2);
        Assert.True(speed2 >= speed1);
        Assert.True(speed1 <= speed2);
        Assert.False(speed1 > speed2);
        Assert.False(speed2 < speed1);
    }

    [Fact]
    public void ImplicitConversion_FromDecimal_ShouldWork()
    {
        // Arrange
        decimal kmh = 75.5m;

        // Act
        Speed speed = kmh;

        // Assert
        Assert.Equal(kmh, speed.KilometersPerHour);
    }

    [Fact]
    public void ImplicitConversion_ToDecimal_ShouldWork()
    {
        // Arrange
        var speed = new Speed(75.5m);

        // Act
        decimal kmh = speed;

        // Assert
        Assert.Equal(75.5m, kmh);
    }

    [Fact]
    public void ToString_ShouldReturnFormattedString()
    {
        // Arrange
        var speed = new Speed(75.5m);
        var expected = "75.5 km/h";

        // Act
        var result = speed.ToString();

        // Assert
        Assert.Equal(expected, result);
    }
}
