namespace FleetXQ.Domain.Enums;

/// <summary>
/// Represents the role of a user in the system
/// </summary>
public enum UserRole
{
    /// <summary>
    /// Regular user with basic permissions
    /// </summary>
    User = 1,

    /// <summary>
    /// Driver with vehicle operation permissions
    /// </summary>
    Driver = 2,

    /// <summary>
    /// Manager with fleet management permissions
    /// </summary>
    Manager = 3,

    /// <summary>
    /// Administrator with full system access
    /// </summary>
    Admin = 4
}
