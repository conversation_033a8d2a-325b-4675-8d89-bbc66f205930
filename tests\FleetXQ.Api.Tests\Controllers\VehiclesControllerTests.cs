using FleetXQ.Api.Controllers;
using FleetXQ.Api.Models;
using FleetXQ.Api.Tests.Common;
using FleetXQ.Application.Features.Vehicles.Commands.CreateVehicle;
using FleetXQ.Application.Features.Vehicles.Commands.UpdateVehicleStatus;
using FleetXQ.Application.Features.Vehicles.Queries.GetVehicleById;
using FleetXQ.Application.Features.Vehicles.Queries.GetVehicleList;
using FleetXQ.Domain.Enums;

namespace FleetXQ.Api.Tests.Controllers;

/// <summary>
/// Unit tests for VehiclesController
/// </summary>
public class VehiclesControllerTests : TestBase
{
    private readonly VehiclesController _controller;

    public VehiclesControllerTests()
    {
        _controller = CreateAuthenticatedController<VehiclesController>(Guid.NewGuid(), "testuser", "Manager");
    }

    [Fact]
    public async Task GetVehicles_WithValidParameters_ShouldReturnPaginatedList()
    {
        // Arrange
        var expectedResult = GetVehicleListResult.Success(
            new List<object> { new { Id = Guid.NewGuid(), Name = "Test Vehicle" } },
            10, // totalCount
            1,  // pageNumber
            10  // pageSize
        );

        MockMediator.Setup(x => x.Send(It.IsAny<GetVehicleListQuery>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(expectedResult);

        // Act
        var result = await _controller.GetVehicles(
            status: "Active",
            vehicleType: "Truck",
            pageNumber: 1,
            pageSize: 10
        );

        // Assert
        var response = AssertPaginatedSuccessResult(result);
        response.Data.Should().NotBeNull();
        response.TotalRecords.Should().Be(10);
        response.PageNumber.Should().Be(1);
        response.PageSize.Should().Be(10);

        MockMediator.Verify(x => x.Send(It.Is<GetVehicleListQuery>(q => 
            q.PageNumber == 1 && q.PageSize == 10), It.IsAny<CancellationToken>()), Times.Once);
    }

    [Fact]
    public async Task GetVehicles_WithInvalidPageNumber_ShouldReturnBadRequest()
    {
        // Act
        var result = await _controller.GetVehicles(pageNumber: 0);

        // Assert
        var response = AssertBadRequestResult(result);
        response.Message.Should().Contain("Page number must be greater than 0");

        MockMediator.Verify(x => x.Send(It.IsAny<GetVehicleListQuery>(), It.IsAny<CancellationToken>()), Times.Never);
    }

    [Fact]
    public async Task GetVehicles_WithInvalidPageSize_ShouldReturnBadRequest()
    {
        // Act
        var result = await _controller.GetVehicles(pageSize: 101);

        // Assert
        var response = AssertBadRequestResult(result);
        response.Message.Should().Contain("Page size must be between 1 and 100");

        MockMediator.Verify(x => x.Send(It.IsAny<GetVehicleListQuery>(), It.IsAny<CancellationToken>()), Times.Never);
    }

    [Fact]
    public async Task GetVehicle_WithExistingId_ShouldReturnVehicle()
    {
        // Arrange
        var vehicleId = Guid.NewGuid();
        var expectedResult = GetVehicleByIdResult.Success(new { Id = vehicleId, Name = "Test Vehicle" });

        MockMediator.Setup(x => x.Send(It.IsAny<GetVehicleByIdQuery>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(expectedResult);

        // Act
        var result = await _controller.GetVehicle(vehicleId);

        // Assert
        var response = AssertSuccessResult(result);
        response.Data.Should().NotBeNull();

        MockMediator.Verify(x => x.Send(It.Is<GetVehicleByIdQuery>(q => 
            q.VehicleId == vehicleId), It.IsAny<CancellationToken>()), Times.Once);
    }

    [Fact]
    public async Task GetVehicle_WithNonExistentId_ShouldReturnNotFound()
    {
        // Arrange
        var vehicleId = Guid.NewGuid();
        var expectedResult = GetVehicleByIdResult.NotFound();

        MockMediator.Setup(x => x.Send(It.IsAny<GetVehicleByIdQuery>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(expectedResult);

        // Act
        var result = await _controller.GetVehicle(vehicleId);

        // Assert
        var response = AssertNotFoundResult(result);
        response.Message.Should().Contain("Vehicle not found");

        MockMediator.Verify(x => x.Send(It.IsAny<GetVehicleByIdQuery>(), It.IsAny<CancellationToken>()), Times.Once);
    }

    [Fact]
    public async Task CreateVehicle_WithValidData_ShouldReturnCreated()
    {
        // Arrange
        var command = new CreateVehicleCommand
        {
            VehicleName = "Test Vehicle",
            LicensePlate = "ABC-123",
            VehicleType = "Truck",
            FuelType = "Diesel"
        };

        var vehicleId = Guid.NewGuid();
        var expectedResult = CreateVehicleResult.Successful(vehicleId);

        MockMediator.Setup(x => x.Send(It.IsAny<CreateVehicleCommand>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(expectedResult);

        // Act
        var result = await _controller.CreateVehicle(command);

        // Assert
        var response = AssertCreatedResult(result);
        response.Data.Should().NotBeNull();

        MockMediator.Verify(x => x.Send(It.IsAny<CreateVehicleCommand>(), It.IsAny<CancellationToken>()), Times.Once);
    }

    [Fact]
    public async Task CreateVehicle_WithDuplicateLicensePlate_ShouldReturnConflict()
    {
        // Arrange
        var command = new CreateVehicleCommand
        {
            VehicleName = "Test Vehicle",
            LicensePlate = "ABC-123",
            VehicleType = "Truck",
            FuelType = "Diesel"
        };

        var expectedResult = CreateVehicleResult.Failed("Vehicle with license plate ABC-123 already exists");

        MockMediator.Setup(x => x.Send(It.IsAny<CreateVehicleCommand>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(expectedResult);

        // Act
        var result = await _controller.CreateVehicle(command);

        // Assert
        result.Should().NotBeNull();
        var conflictResult = result.Result.Should().BeOfType<ConflictObjectResult>().Subject;
        var apiResponse = conflictResult.Value.Should().BeOfType<ApiResponse<object>>().Subject;
        
        apiResponse.Success.Should().BeFalse();
        apiResponse.Message.Should().Contain("license plate");

        MockMediator.Verify(x => x.Send(It.IsAny<CreateVehicleCommand>(), It.IsAny<CancellationToken>()), Times.Once);
    }

    [Fact]
    public async Task CreateVehicle_WithInvalidData_ShouldReturnBadRequest()
    {
        // Arrange
        var command = new CreateVehicleCommand
        {
            VehicleName = "Test Vehicle",
            LicensePlate = "ABC-123",
            VehicleType = "Truck",
            FuelType = "Diesel"
        };

        var expectedResult = CreateVehicleResult.Failed("Invalid vehicle data");

        MockMediator.Setup(x => x.Send(It.IsAny<CreateVehicleCommand>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(expectedResult);

        // Act
        var result = await _controller.CreateVehicle(command);

        // Assert
        var response = AssertBadRequestResult(result);
        response.Message.Should().Contain("Invalid vehicle data");

        MockMediator.Verify(x => x.Send(It.IsAny<CreateVehicleCommand>(), It.IsAny<CancellationToken>()), Times.Once);
    }

    [Fact]
    public async Task UpdateVehicleStatus_WithValidData_ShouldReturnSuccess()
    {
        // Arrange
        var vehicleId = Guid.NewGuid();
        var request = new UpdateVehicleStatusRequest
        {
            Status = VehicleStatus.Maintenance,
            Reason = "Scheduled maintenance",
            Context = "Monthly service"
        };

        var expectedResult = UpdateVehicleStatusResult.Successful(
            vehicleId, 
            VehicleStatus.Active, 
            VehicleStatus.Maintenance
        );

        MockMediator.Setup(x => x.Send(It.IsAny<UpdateVehicleStatusCommand>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(expectedResult);

        // Act
        var result = await _controller.UpdateVehicleStatus(vehicleId, request);

        // Assert
        var response = AssertSuccessResult(result);
        response.Data.Should().NotBeNull();

        MockMediator.Verify(x => x.Send(It.Is<UpdateVehicleStatusCommand>(c => 
            c.VehicleId == vehicleId && c.Status == VehicleStatus.Maintenance), 
            It.IsAny<CancellationToken>()), Times.Once);
    }

    [Fact]
    public async Task UpdateVehicleStatus_WithNonExistentVehicle_ShouldReturnNotFound()
    {
        // Arrange
        var vehicleId = Guid.NewGuid();
        var request = new UpdateVehicleStatusRequest
        {
            Status = VehicleStatus.Maintenance,
            Reason = "Scheduled maintenance"
        };

        var expectedResult = UpdateVehicleStatusResult.Failed(vehicleId, "Vehicle not found");

        MockMediator.Setup(x => x.Send(It.IsAny<UpdateVehicleStatusCommand>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(expectedResult);

        // Act
        var result = await _controller.UpdateVehicleStatus(vehicleId, request);

        // Assert
        var response = AssertNotFoundResult(result);
        response.Message.Should().Contain("Vehicle not found");

        MockMediator.Verify(x => x.Send(It.IsAny<UpdateVehicleStatusCommand>(), It.IsAny<CancellationToken>()), Times.Once);
    }

    [Fact]
    public async Task UpdateVehicleStatus_WithInvalidStatus_ShouldReturnBadRequest()
    {
        // Arrange
        var vehicleId = Guid.NewGuid();
        var request = new UpdateVehicleStatusRequest
        {
            Status = VehicleStatus.Active,
            Reason = "Invalid transition"
        };

        var expectedResult = UpdateVehicleStatusResult.Failed(vehicleId, "Invalid status transition");

        MockMediator.Setup(x => x.Send(It.IsAny<UpdateVehicleStatusCommand>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(expectedResult);

        // Act
        var result = await _controller.UpdateVehicleStatus(vehicleId, request);

        // Assert
        var response = AssertBadRequestResult(result);
        response.Message.Should().Contain("Invalid status transition");

        MockMediator.Verify(x => x.Send(It.IsAny<UpdateVehicleStatusCommand>(), It.IsAny<CancellationToken>()), Times.Once);
    }
}
