using FleetXQ.Domain.ValueObjects;
using Xunit;

namespace FleetXQ.Domain.Tests.ValueObjects;

public class FuelLevelTests
{
    [Theory]
    [InlineData(0)]
    [InlineData(25.5)]
    [InlineData(50)]
    [InlineData(100)]
    public void Constructor_WithValidPercentage_ShouldCreateFuelLevel(decimal percentage)
    {
        // Act
        var fuelLevel = new FuelLevel(percentage);

        // Assert
        Assert.Equal(percentage, fuelLevel.Percentage);
    }

    [Theory]
    [InlineData(-1)]
    [InlineData(101)]
    [InlineData(-0.1)]
    [InlineData(100.1)]
    public void Constructor_WithInvalidPercentage_ShouldThrowArgumentOutOfRangeException(decimal percentage)
    {
        // Act & Assert
        Assert.Throws<ArgumentOutOfRangeException>(() => new FuelLevel(percentage));
    }

    [Theory]
    [InlineData(0, true)]
    [InlineData(5, true)]
    [InlineData(15, true)]
    [InlineData(20, false)]
    [InlineData(50, false)]
    public void IsLow_ShouldReturnCorrectValue(decimal percentage, bool expectedIsLow)
    {
        // Arrange
        var fuelLevel = new FuelLevel(percentage);

        // Act & Assert
        Assert.Equal(expectedIsLow, fuelLevel.IsLow);
    }

    [Theory]
    [InlineData(0, true)]
    [InlineData(5, true)]
    [InlineData(9.9, true)]
    [InlineData(10, false)]
    [InlineData(50, false)]
    public void IsCritical_ShouldReturnCorrectValue(decimal percentage, bool expectedIsCritical)
    {
        // Arrange
        var fuelLevel = new FuelLevel(percentage);

        // Act & Assert
        Assert.Equal(expectedIsCritical, fuelLevel.IsCritical);
    }

    [Theory]
    [InlineData(0, true)]
    [InlineData(0.1, false)]
    [InlineData(50, false)]
    public void IsEmpty_ShouldReturnCorrectValue(decimal percentage, bool expectedIsEmpty)
    {
        // Arrange
        var fuelLevel = new FuelLevel(percentage);

        // Act & Assert
        Assert.Equal(expectedIsEmpty, fuelLevel.IsEmpty);
    }

    [Theory]
    [InlineData(100, true)]
    [InlineData(99.9, false)]
    [InlineData(50, false)]
    public void IsFull_ShouldReturnCorrectValue(decimal percentage, bool expectedIsFull)
    {
        // Arrange
        var fuelLevel = new FuelLevel(percentage);

        // Act & Assert
        Assert.Equal(expectedIsFull, fuelLevel.IsFull);
    }

    [Fact]
    public void FromDouble_WithValidValue_ShouldCreateFuelLevel()
    {
        // Arrange
        var percentage = 75.5;

        // Act
        var fuelLevel = FuelLevel.FromDouble(percentage);

        // Assert
        Assert.Equal((decimal)percentage, fuelLevel.Percentage);
    }

    [Fact]
    public void Empty_ShouldReturnZeroPercentFuelLevel()
    {
        // Act
        var fuelLevel = FuelLevel.Empty;

        // Assert
        Assert.Equal(0, fuelLevel.Percentage);
        Assert.True(fuelLevel.IsEmpty);
    }

    [Fact]
    public void Full_ShouldReturnHundredPercentFuelLevel()
    {
        // Act
        var fuelLevel = FuelLevel.Full;

        // Assert
        Assert.Equal(100, fuelLevel.Percentage);
        Assert.True(fuelLevel.IsFull);
    }

    [Theory]
    [InlineData(0, FuelLevelCategory.Empty)]
    [InlineData(5, FuelLevelCategory.Critical)]
    [InlineData(15, FuelLevelCategory.Low)]
    [InlineData(50, FuelLevelCategory.Normal)]
    [InlineData(85, FuelLevelCategory.High)]
    public void GetCategory_ShouldReturnCorrectCategory(decimal percentage, FuelLevelCategory expectedCategory)
    {
        // Arrange
        var fuelLevel = new FuelLevel(percentage);

        // Act
        var category = fuelLevel.GetCategory();

        // Assert
        Assert.Equal(expectedCategory, category);
    }

    [Fact]
    public void ToLiters_WithValidTankCapacity_ShouldCalculateCorrectLiters()
    {
        // Arrange
        var fuelLevel = new FuelLevel(50);
        var tankCapacity = 60m;
        var expectedLiters = 30m;

        // Act
        var liters = fuelLevel.ToLiters(tankCapacity);

        // Assert
        Assert.Equal(expectedLiters, liters);
    }

    [Fact]
    public void ToLiters_WithNegativeTankCapacity_ShouldThrowArgumentOutOfRangeException()
    {
        // Arrange
        var fuelLevel = new FuelLevel(50);

        // Act & Assert
        Assert.Throws<ArgumentOutOfRangeException>(() => fuelLevel.ToLiters(-10));
    }

    [Fact]
    public void FromLiters_WithValidValues_ShouldCreateCorrectFuelLevel()
    {
        // Arrange
        var liters = 30m;
        var tankCapacity = 60m;
        var expectedPercentage = 50m;

        // Act
        var fuelLevel = FuelLevel.FromLiters(liters, tankCapacity);

        // Assert
        Assert.Equal(expectedPercentage, fuelLevel.Percentage);
    }

    [Theory]
    [InlineData(-1, 60)]
    [InlineData(70, 60)]
    public void FromLiters_WithInvalidLiters_ShouldThrowArgumentOutOfRangeException(decimal liters, decimal tankCapacity)
    {
        // Act & Assert
        Assert.Throws<ArgumentOutOfRangeException>(() => FuelLevel.FromLiters(liters, tankCapacity));
    }

    [Fact]
    public void FromLiters_WithZeroTankCapacity_ShouldThrowArgumentOutOfRangeException()
    {
        // Act & Assert
        Assert.Throws<ArgumentOutOfRangeException>(() => FuelLevel.FromLiters(30, 0));
    }

    [Fact]
    public void ImplicitConversion_FromDecimal_ShouldWork()
    {
        // Arrange
        decimal percentage = 75.5m;

        // Act
        FuelLevel fuelLevel = percentage;

        // Assert
        Assert.Equal(percentage, fuelLevel.Percentage);
    }

    [Fact]
    public void ImplicitConversion_ToDecimal_ShouldWork()
    {
        // Arrange
        var fuelLevel = new FuelLevel(75.5m);

        // Act
        decimal percentage = fuelLevel;

        // Assert
        Assert.Equal(75.5m, percentage);
    }

    [Fact]
    public void Equals_WithSamePercentage_ShouldReturnTrue()
    {
        // Arrange
        var fuelLevel1 = new FuelLevel(50);
        var fuelLevel2 = new FuelLevel(50);

        // Act & Assert
        Assert.Equal(fuelLevel1, fuelLevel2);
        Assert.True(fuelLevel1 == fuelLevel2);
        Assert.False(fuelLevel1 != fuelLevel2);
    }

    [Fact]
    public void Equals_WithDifferentPercentage_ShouldReturnFalse()
    {
        // Arrange
        var fuelLevel1 = new FuelLevel(50);
        var fuelLevel2 = new FuelLevel(75);

        // Act & Assert
        Assert.NotEqual(fuelLevel1, fuelLevel2);
        Assert.False(fuelLevel1 == fuelLevel2);
        Assert.True(fuelLevel1 != fuelLevel2);
    }

    [Fact]
    public void ToString_ShouldReturnFormattedString()
    {
        // Arrange
        var fuelLevel = new FuelLevel(75.5m);
        var expected = "75.5%";

        // Act
        var result = fuelLevel.ToString();

        // Assert
        Assert.Equal(expected, result);
    }
}
