using FleetXQ.Domain.Entities;
using FleetXQ.Domain.Enums;
using FleetXQ.Domain.ValueObjects;

namespace FleetXQ.Domain.Interfaces;

/// <summary>
/// Repository interface for Trip aggregate root
/// </summary>
public interface ITripRepository : IRepository<Trip>
{
    /// <summary>
    /// Gets trips by vehicle ID
    /// </summary>
    /// <param name="vehicleId">The vehicle ID</param>
    /// <param name="cancellationToken">The cancellation token</param>
    /// <returns>A collection of trips for the specified vehicle</returns>
    Task<IEnumerable<Trip>> GetByVehicleIdAsync(Guid vehicleId, CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets trips by driver ID
    /// </summary>
    /// <param name="driverId">The driver ID</param>
    /// <param name="cancellationToken">The cancellation token</param>
    /// <returns>A collection of trips for the specified driver</returns>
    Task<IEnumerable<Trip>> GetByDriverIdAsync(Guid driverId, CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets trips by status
    /// </summary>
    /// <param name="status">The trip status</param>
    /// <param name="cancellationToken">The cancellation token</param>
    /// <returns>A collection of trips with the specified status</returns>
    Task<IEnumerable<Trip>> GetByStatusAsync(TripStatus status, CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets active trips
    /// </summary>
    /// <param name="cancellationToken">The cancellation token</param>
    /// <returns>A collection of active trips</returns>
    Task<IEnumerable<Trip>> GetActiveTripsAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets completed trips
    /// </summary>
    /// <param name="cancellationToken">The cancellation token</param>
    /// <returns>A collection of completed trips</returns>
    Task<IEnumerable<Trip>> GetCompletedTripsAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets trips within a date range
    /// </summary>
    /// <param name="fromDate">The start date</param>
    /// <param name="toDate">The end date</param>
    /// <param name="cancellationToken">The cancellation token</param>
    /// <returns>A collection of trips within the date range</returns>
    Task<IEnumerable<Trip>> GetTripsBetweenAsync(DateTime fromDate, DateTime toDate, 
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets trips starting from a specific location radius
    /// </summary>
    /// <param name="centerLocation">The center location</param>
    /// <param name="radiusKm">The radius in kilometers</param>
    /// <param name="cancellationToken">The cancellation token</param>
    /// <returns>A collection of trips starting within the specified radius</returns>
    Task<IEnumerable<Trip>> GetTripsStartingInRadiusAsync(Location centerLocation, double radiusKm, 
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets trips ending in a specific location radius
    /// </summary>
    /// <param name="centerLocation">The center location</param>
    /// <param name="radiusKm">The radius in kilometers</param>
    /// <param name="cancellationToken">The cancellation token</param>
    /// <returns>A collection of trips ending within the specified radius</returns>
    Task<IEnumerable<Trip>> GetTripsEndingInRadiusAsync(Location centerLocation, double radiusKm, 
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets trips with distance above threshold
    /// </summary>
    /// <param name="distanceThreshold">The distance threshold in kilometers</param>
    /// <param name="cancellationToken">The cancellation token</param>
    /// <returns>A collection of long trips</returns>
    Task<IEnumerable<Trip>> GetLongTripsAsync(decimal distanceThreshold, CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets trips with duration above threshold
    /// </summary>
    /// <param name="durationThresholdMinutes">The duration threshold in minutes</param>
    /// <param name="cancellationToken">The cancellation token</param>
    /// <returns>A collection of long duration trips</returns>
    Task<IEnumerable<Trip>> GetLongDurationTripsAsync(int durationThresholdMinutes, 
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets trips with high fuel consumption
    /// </summary>
    /// <param name="fuelConsumptionThreshold">The fuel consumption threshold in liters</param>
    /// <param name="cancellationToken">The cancellation token</param>
    /// <returns>A collection of trips with high fuel consumption</returns>
    Task<IEnumerable<Trip>> GetHighFuelConsumptionTripsAsync(decimal fuelConsumptionThreshold, 
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets trips with harsh events above threshold
    /// </summary>
    /// <param name="harshEventsThreshold">The harsh events threshold</param>
    /// <param name="cancellationToken">The cancellation token</param>
    /// <returns>A collection of trips with many harsh events</returns>
    Task<IEnumerable<Trip>> GetTripsWithHarshEventsAsync(int harshEventsThreshold = 1, 
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets trips with high idle time percentage
    /// </summary>
    /// <param name="idleTimePercentageThreshold">The idle time percentage threshold</param>
    /// <param name="cancellationToken">The cancellation token</param>
    /// <returns>A collection of trips with high idle time</returns>
    Task<IEnumerable<Trip>> GetHighIdleTimeTripsAsync(double idleTimePercentageThreshold = 20, 
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets the current active trip for a vehicle
    /// </summary>
    /// <param name="vehicleId">The vehicle ID</param>
    /// <param name="cancellationToken">The cancellation token</param>
    /// <returns>The active trip for the vehicle, or null if none</returns>
    Task<Trip?> GetCurrentTripForVehicleAsync(Guid vehicleId, CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets the current active trip for a driver
    /// </summary>
    /// <param name="driverId">The driver ID</param>
    /// <param name="cancellationToken">The cancellation token</param>
    /// <returns>The active trip for the driver, or null if none</returns>
    Task<Trip?> GetCurrentTripForDriverAsync(Guid driverId, CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets trip statistics for a vehicle
    /// </summary>
    /// <param name="vehicleId">The vehicle ID</param>
    /// <param name="fromDate">The start date</param>
    /// <param name="toDate">The end date</param>
    /// <param name="cancellationToken">The cancellation token</param>
    /// <returns>Trip statistics for the vehicle</returns>
    Task<TripStatistics> GetVehicleTripStatisticsAsync(Guid vehicleId, DateTime? fromDate = null, 
        DateTime? toDate = null, CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets trip statistics for a driver
    /// </summary>
    /// <param name="driverId">The driver ID</param>
    /// <param name="fromDate">The start date</param>
    /// <param name="toDate">The end date</param>
    /// <param name="cancellationToken">The cancellation token</param>
    /// <returns>Trip statistics for the driver</returns>
    Task<TripStatistics> GetDriverTripStatisticsAsync(Guid driverId, DateTime? fromDate = null, 
        DateTime? toDate = null, CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets overall fleet trip statistics
    /// </summary>
    /// <param name="fromDate">The start date</param>
    /// <param name="toDate">The end date</param>
    /// <param name="cancellationToken">The cancellation token</param>
    /// <returns>Overall fleet trip statistics</returns>
    Task<TripStatistics> GetFleetTripStatisticsAsync(DateTime? fromDate = null, DateTime? toDate = null, 
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Searches trips by multiple criteria
    /// </summary>
    /// <param name="searchCriteria">The search criteria</param>
    /// <param name="cancellationToken">The cancellation token</param>
    /// <returns>A collection of trips matching the criteria</returns>
    Task<IEnumerable<Trip>> SearchTripsAsync(TripSearchCriteria searchCriteria, 
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets trip analytics for reporting
    /// </summary>
    /// <param name="fromDate">The start date</param>
    /// <param name="toDate">The end date</param>
    /// <param name="groupBy">The grouping period (day, week, month)</param>
    /// <param name="cancellationToken">The cancellation token</param>
    /// <returns>Trip analytics data</returns>
    Task<IEnumerable<TripAnalyticsData>> GetTripAnalyticsAsync(DateTime fromDate, DateTime toDate, 
        string groupBy = "day", CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets top performing trips by score
    /// </summary>
    /// <param name="limit">The maximum number of trips to return</param>
    /// <param name="fromDate">The start date</param>
    /// <param name="toDate">The end date</param>
    /// <param name="cancellationToken">The cancellation token</param>
    /// <returns>A collection of top performing trips</returns>
    Task<IEnumerable<Trip>> GetTopPerformingTripsAsync(int limit = 10, DateTime? fromDate = null, 
        DateTime? toDate = null, CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets poorly performing trips by score
    /// </summary>
    /// <param name="limit">The maximum number of trips to return</param>
    /// <param name="fromDate">The start date</param>
    /// <param name="toDate">The end date</param>
    /// <param name="cancellationToken">The cancellation token</param>
    /// <returns>A collection of poorly performing trips</returns>
    Task<IEnumerable<Trip>> GetPoorlyPerformingTripsAsync(int limit = 10, DateTime? fromDate = null, 
        DateTime? toDate = null, CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets recent trips for dashboard display
    /// </summary>
    /// <param name="limit">The maximum number of trips to return</param>
    /// <param name="cancellationToken">The cancellation token</param>
    /// <returns>A collection of recent trips</returns>
    Task<IEnumerable<Trip>> GetRecentTripsAsync(int limit = 10, CancellationToken cancellationToken = default);
}

/// <summary>
/// Represents search criteria for trips
/// </summary>
public class TripSearchCriteria
{
    /// <summary>
    /// Gets or sets the vehicle ID filter
    /// </summary>
    public Guid? VehicleId { get; set; }

    /// <summary>
    /// Gets or sets the driver ID filter
    /// </summary>
    public Guid? DriverId { get; set; }

    /// <summary>
    /// Gets or sets the trip status filter
    /// </summary>
    public TripStatus? Status { get; set; }

    /// <summary>
    /// Gets or sets the start date from filter
    /// </summary>
    public DateTime? StartDateFrom { get; set; }

    /// <summary>
    /// Gets or sets the start date to filter
    /// </summary>
    public DateTime? StartDateTo { get; set; }

    /// <summary>
    /// Gets or sets the end date from filter
    /// </summary>
    public DateTime? EndDateFrom { get; set; }

    /// <summary>
    /// Gets or sets the end date to filter
    /// </summary>
    public DateTime? EndDateTo { get; set; }

    /// <summary>
    /// Gets or sets the minimum distance filter
    /// </summary>
    public decimal? MinDistance { get; set; }

    /// <summary>
    /// Gets or sets the maximum distance filter
    /// </summary>
    public decimal? MaxDistance { get; set; }

    /// <summary>
    /// Gets or sets the minimum duration filter in minutes
    /// </summary>
    public int? MinDurationMinutes { get; set; }

    /// <summary>
    /// Gets or sets the maximum duration filter in minutes
    /// </summary>
    public int? MaxDurationMinutes { get; set; }

    /// <summary>
    /// Gets or sets the minimum fuel consumed filter
    /// </summary>
    public decimal? MinFuelConsumed { get; set; }

    /// <summary>
    /// Gets or sets the maximum fuel consumed filter
    /// </summary>
    public decimal? MaxFuelConsumed { get; set; }

    /// <summary>
    /// Gets or sets the minimum harsh events filter
    /// </summary>
    public int? MinHarshEvents { get; set; }

    /// <summary>
    /// Gets or sets the maximum harsh events filter
    /// </summary>
    public int? MaxHarshEvents { get; set; }

    /// <summary>
    /// Gets or sets the purpose filter
    /// </summary>
    public string? Purpose { get; set; }

    /// <summary>
    /// Gets or sets the start location filter
    /// </summary>
    public Location? StartLocation { get; set; }

    /// <summary>
    /// Gets or sets the end location filter
    /// </summary>
    public Location? EndLocation { get; set; }

    /// <summary>
    /// Gets or sets the radius for location filters in kilometers
    /// </summary>
    public double? LocationRadiusKm { get; set; }
}

/// <summary>
/// Represents trip statistics
/// </summary>
public class TripStatistics
{
    /// <summary>
    /// Gets or sets the total number of trips
    /// </summary>
    public int TotalTrips { get; set; }

    /// <summary>
    /// Gets or sets the total distance traveled
    /// </summary>
    public decimal TotalDistance { get; set; }

    /// <summary>
    /// Gets or sets the total duration in minutes
    /// </summary>
    public int TotalDurationMinutes { get; set; }

    /// <summary>
    /// Gets or sets the total fuel consumed
    /// </summary>
    public decimal TotalFuelConsumed { get; set; }

    /// <summary>
    /// Gets or sets the total idle time in minutes
    /// </summary>
    public int TotalIdleTimeMinutes { get; set; }

    /// <summary>
    /// Gets or sets the total harsh events
    /// </summary>
    public int TotalHarshEvents { get; set; }

    /// <summary>
    /// Gets or sets the average distance per trip
    /// </summary>
    public decimal AverageDistance { get; set; }

    /// <summary>
    /// Gets or sets the average duration per trip in minutes
    /// </summary>
    public double AverageDurationMinutes { get; set; }

    /// <summary>
    /// Gets or sets the average fuel consumption per trip
    /// </summary>
    public decimal AverageFuelConsumption { get; set; }

    /// <summary>
    /// Gets or sets the average speed across all trips
    /// </summary>
    public decimal AverageSpeed { get; set; }

    /// <summary>
    /// Gets or sets the average trip score
    /// </summary>
    public double AverageTripScore { get; set; }

    /// <summary>
    /// Gets or sets the fuel efficiency in liters per 100km
    /// </summary>
    public decimal FuelEfficiency { get; set; }
}

/// <summary>
/// Represents trip analytics data for a specific time period
/// </summary>
public class TripAnalyticsData
{
    /// <summary>
    /// Gets or sets the period (date or date range)
    /// </summary>
    public string Period { get; set; } = string.Empty;

    /// <summary>
    /// Gets or sets the trip statistics for the period
    /// </summary>
    public TripStatistics Statistics { get; set; } = new();

    /// <summary>
    /// Gets or sets the number of active trips
    /// </summary>
    public int ActiveTrips { get; set; }

    /// <summary>
    /// Gets or sets the number of completed trips
    /// </summary>
    public int CompletedTrips { get; set; }

    /// <summary>
    /// Gets or sets the number of cancelled trips
    /// </summary>
    public int CancelledTrips { get; set; }
}
