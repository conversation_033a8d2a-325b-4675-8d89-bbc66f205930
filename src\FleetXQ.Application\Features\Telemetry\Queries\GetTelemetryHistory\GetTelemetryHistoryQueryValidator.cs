using FluentValidation;

namespace FleetXQ.Application.Features.Telemetry.Queries.GetTelemetryHistory;

/// <summary>
/// Validator for GetTelemetryHistoryQuery
/// </summary>
public sealed class GetTelemetryHistoryQueryValidator : AbstractValidator<GetTelemetryHistoryQuery>
{
    /// <summary>
    /// Initializes a new instance of the <see cref="GetTelemetryHistoryQueryValidator"/> class
    /// </summary>
    public GetTelemetryHistoryQueryValidator()
    {
        RuleFor(x => x.VehicleId)
            .NotEmpty()
            .WithMessage("Vehicle ID is required");

        RuleFor(x => x.StartDate)
            .NotEmpty()
            .WithMessage("Start date is required")
            .LessThan(x => x.EndDate)
            .WithMessage("Start date must be before end date");

        RuleFor(x => x.EndDate)
            .NotEmpty()
            .WithMessage("End date is required")
            .LessThanOrEqualTo(DateTime.UtcNow)
            .WithMessage("End date cannot be in the future");

        RuleFor(x => x.EndDate.Subtract(x.StartDate).TotalDays)
            .LessThanOrEqualTo(90)
            .WithMessage("Date range cannot exceed 90 days");

        RuleFor(x => x.PageNumber)
            .GreaterThan(0)
            .WithMessage("Page number must be greater than 0");

        RuleFor(x => x.PageSize)
            .GreaterThan(0)
            .WithMessage("Page size must be greater than 0")
            .LessThanOrEqualTo(1000)
            .WithMessage("Page size cannot exceed 1000");

        RuleFor(x => x.IntervalMinutes)
            .GreaterThan(0)
            .WithMessage("Interval minutes must be greater than 0")
            .LessThanOrEqualTo(1440) // 24 hours
            .WithMessage("Interval minutes cannot exceed 1440 (24 hours)")
            .When(x => x.IntervalMinutes.HasValue);
    }
}
