using AutoMapper;
using FleetXQ.Application.Features.Authentication.DTOs;
using FleetXQ.Domain.Enums;
using FleetXQ.Domain.Interfaces;
using MediatR;
using Microsoft.Extensions.Logging;

namespace FleetXQ.Application.Features.Authentication.Queries.GetUserProfile;

/// <summary>
/// Handler for GetUserProfileQuery
/// </summary>
public sealed class GetUserProfileQueryHandler : IRequestHandler<GetUserProfileQuery, GetUserProfileResult>
{
    private readonly IUserRepository _userRepository;
    private readonly IMapper _mapper;
    private readonly ILogger<GetUserProfileQueryHandler> _logger;

    /// <summary>
    /// Initializes a new instance of the <see cref="GetUserProfileQueryHandler"/> class
    /// </summary>
    /// <param name="userRepository">The user repository</param>
    /// <param name="mapper">The AutoMapper instance</param>
    /// <param name="logger">The logger</param>
    public GetUserProfileQueryHandler(
        IUserRepository userRepository,
        IMapper mapper,
        ILogger<GetUserProfileQueryHandler> logger)
    {
        _userRepository = userRepository ?? throw new ArgumentNullException(nameof(userRepository));
        _mapper = mapper ?? throw new ArgumentNullException(nameof(mapper));
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
    }

    /// <summary>
    /// Handles the GetUserProfileQuery
    /// </summary>
    /// <param name="request">The query request</param>
    /// <param name="cancellationToken">The cancellation token</param>
    /// <returns>The query result</returns>
    public async Task<GetUserProfileResult> Handle(GetUserProfileQuery request, CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogInformation("Getting user profile for user {UserId} requested by {RequestingUserId}", 
                request.UserId, request.RequestingUserId);

            // Get the requesting user for authorization checks
            var requestingUser = await _userRepository.GetByIdAsync(request.RequestingUserId, cancellationToken);
            if (requestingUser == null)
            {
                _logger.LogWarning("Requesting user not found: {RequestingUserId}", request.RequestingUserId);
                return GetUserProfileResult.CreateAccessDenied();
            }

            // Check if the requesting user can access the profile
            if (!CanAccessProfile(request.UserId, request.RequestingUserId, requestingUser.Role))
            {
                _logger.LogWarning("Access denied for user {RequestingUserId} to access profile {UserId}", 
                    request.RequestingUserId, request.UserId);
                return GetUserProfileResult.CreateAccessDenied();
            }

            // Get the target user
            var user = await _userRepository.GetByIdAsync(request.UserId, cancellationToken);
            if (user == null)
            {
                _logger.LogInformation("User not found: {UserId}", request.UserId);
                return GetUserProfileResult.NotFound();
            }

            // Create the profile DTO
            var profile = CreateUserProfileDto(user, request.IncludeSensitiveInfo, requestingUser.Role);

            _logger.LogInformation("Successfully retrieved user profile for user {UserId}", request.UserId);

            return GetUserProfileResult.CreateSuccess(profile);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting user profile for user {UserId}", request.UserId);
            return GetUserProfileResult.Failed($"An error occurred while retrieving the user profile: {ex.Message}");
        }
    }

    /// <summary>
    /// Checks if the requesting user can access the target user's profile
    /// </summary>
    /// <param name="targetUserId">The target user ID</param>
    /// <param name="requestingUserId">The requesting user ID</param>
    /// <param name="requestingUserRole">The requesting user's role</param>
    /// <returns>True if access is allowed</returns>
    private static bool CanAccessProfile(Guid targetUserId, Guid requestingUserId, UserRole requestingUserRole)
    {
        // Users can always access their own profile
        if (targetUserId == requestingUserId)
            return true;

        // Admins can access any profile
        if (requestingUserRole == UserRole.Admin)
            return true;

        // Managers can access profiles of users with lower roles
        if (requestingUserRole == UserRole.Manager)
            return true;

        // Regular users cannot access other users' profiles
        return false;
    }

    /// <summary>
    /// Creates a UserProfileDto from a User entity
    /// </summary>
    /// <param name="user">The user entity</param>
    /// <param name="includeSensitiveInfo">Whether to include sensitive information</param>
    /// <param name="requestingUserRole">The requesting user's role</param>
    /// <returns>The user profile DTO</returns>
    private static UserProfileDto CreateUserProfileDto(Domain.Entities.User user, bool includeSensitiveInfo, UserRole requestingUserRole)
    {
        var profile = new UserProfileDto
        {
            Id = user.Id,
            Username = user.Username,
            Email = user.Email,
            FirstName = user.FirstName,
            LastName = user.LastName,
            FullName = user.FullName,
            Role = user.Role,
            IsActive = user.IsActive,
            IsEmailConfirmed = user.IsEmailConfirmed,
            LastLoginDate = user.LastLoginDate,
            CreatedAt = user.CreatedAt,
            UpdatedAt = user.UpdatedAt,
            Permissions = GetUserPermissions(user.Role)
        };

        // Include sensitive information only if requested and authorized
        if (includeSensitiveInfo && (requestingUserRole == UserRole.Admin || requestingUserRole == UserRole.Manager))
        {
            profile.IsLockedOut = user.IsLockedOut;
            profile.LockoutEnd = user.LockoutEnd;
        }

        return profile;
    }

    /// <summary>
    /// Gets the permissions for a user role
    /// </summary>
    /// <param name="role">The user role</param>
    /// <returns>The list of permissions</returns>
    private static List<string> GetUserPermissions(UserRole role)
    {
        return role switch
        {
            UserRole.Admin => new List<string>
            {
                "vehicles.read", "vehicles.write", "vehicles.delete",
                "drivers.read", "drivers.write", "drivers.delete",
                "users.read", "users.write", "users.delete",
                "alerts.read", "alerts.write", "alerts.delete",
                "reports.read", "reports.write",
                "system.admin"
            },
            UserRole.Manager => new List<string>
            {
                "vehicles.read", "vehicles.write",
                "drivers.read", "drivers.write",
                "users.read",
                "alerts.read", "alerts.write",
                "reports.read", "reports.write"
            },
            UserRole.Driver => new List<string>
            {
                "vehicles.read",
                "profile.read", "profile.write"
            },
            UserRole.User => new List<string>
            {
                "vehicles.read",
                "alerts.read",
                "reports.read",
                "profile.read", "profile.write"
            },
            _ => new List<string>()
        };
    }
}
