using FleetXQ.Api.Models;
using FleetXQ.Application.Features.Telemetry.Commands.ProcessTelemetryData;
using FleetXQ.Application.Features.Telemetry.Queries.GetLatestTelemetry;
using FleetXQ.Application.Features.Telemetry.Queries.GetTelemetryHistory;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Swashbuckle.AspNetCore.Annotations;

namespace FleetXQ.Api.Controllers;

/// <summary>
/// Telemetry data controller for real-time vehicle data ingestion and queries
/// </summary>
[ApiController]
[Route("api/[controller]")]
[Authorize]
[Produces("application/json")]
public class TelemetryController : BaseApiController
{
    /// <summary>
    /// Processes incoming telemetry data from vehicles
    /// </summary>
    /// <param name="request">The telemetry data</param>
    /// <param name="cancellationToken">The cancellation token</param>
    /// <returns>Processing result</returns>
    [HttpPost("process")]
    [Authorize(Roles = "Admin,Manager,System")]
    [SwaggerOperation(
        Summary = "Process telemetry data",
        Description = "Processes incoming real-time telemetry data from vehicles"
    )]
    [SwaggerResponse(200, "Telemetry data processed successfully", typeof(ApiResponse<object>))]
    [SwaggerResponse(400, "Invalid telemetry data", typeof(ApiResponse<object>))]
    [SwaggerResponse(401, "Unauthorized", typeof(ApiResponse<object>))]
    [SwaggerResponse(403, "Forbidden - insufficient permissions", typeof(ApiResponse<object>))]
    [SwaggerResponse(404, "Vehicle not found", typeof(ApiResponse<object>))]
    [SwaggerResponse(500, "Internal server error", typeof(ApiResponse<object>))]
    public async Task<ActionResult<ApiResponse<object>>> ProcessTelemetryData(
        [FromBody] ProcessTelemetryDataCommand request,
        CancellationToken cancellationToken = default)
    {
        var result = await Mediator.Send(request, cancellationToken);

        if (!result.Success)
        {
            if (result.ErrorMessage?.Contains("not found", StringComparison.OrdinalIgnoreCase) == true)
            {
                return NotFound(Error(result.ErrorMessage));
            }

            return BadRequest(Error(result.ErrorMessage ?? "Failed to process telemetry data"));
        }

        return Ok(Success(new
        {
            VehicleId = result.VehicleId,
            ProcessedAt = result.ProcessedAt,
            AlertsTriggered = result.AlertsTriggered
        }, "Telemetry data processed successfully"));
    }

    /// <summary>
    /// Gets the latest telemetry data for a specific vehicle
    /// </summary>
    /// <param name="vehicleId">The vehicle ID</param>
    /// <param name="cancellationToken">The cancellation token</param>
    /// <returns>Latest telemetry data</returns>
    [HttpGet("latest/{vehicleId:guid}")]
    [Authorize(Roles = "Admin,Manager,Driver,User")]
    [SwaggerOperation(
        Summary = "Get latest telemetry",
        Description = "Retrieves the most recent telemetry data for a specific vehicle"
    )]
    [SwaggerResponse(200, "Latest telemetry retrieved successfully", typeof(ApiResponse<object>))]
    [SwaggerResponse(404, "Vehicle not found or no telemetry data", typeof(ApiResponse<object>))]
    [SwaggerResponse(401, "Unauthorized", typeof(ApiResponse<object>))]
    [SwaggerResponse(500, "Internal server error", typeof(ApiResponse<object>))]
    public async Task<ActionResult<ApiResponse<object>>> GetLatestTelemetry(
        [FromRoute] Guid vehicleId,
        CancellationToken cancellationToken = default)
    {
        var query = new GetLatestTelemetryQuery { VehicleId = vehicleId };
        var result = await Mediator.Send(query, cancellationToken);

        if (!result.Success)
        {
            return NotFound(Error(result.ErrorMessage ?? "No telemetry data found for this vehicle"));
        }

        return Ok(Success(result.Telemetry, "Latest telemetry retrieved successfully"));
    }

    /// <summary>
    /// Gets telemetry history for a specific vehicle with optional filtering
    /// </summary>
    /// <param name="vehicleId">The vehicle ID</param>
    /// <param name="startDate">Start date for filtering (optional)</param>
    /// <param name="endDate">End date for filtering (optional)</param>
    /// <param name="pageNumber">Page number (default: 1)</param>
    /// <param name="pageSize">Page size (default: 50, max: 1000)</param>
    /// <param name="cancellationToken">The cancellation token</param>
    /// <returns>Paginated telemetry history</returns>
    [HttpGet("history/{vehicleId:guid}")]
    [Authorize(Roles = "Admin,Manager,Driver,User")]
    [SwaggerOperation(
        Summary = "Get telemetry history",
        Description = "Retrieves historical telemetry data for a specific vehicle with optional date filtering"
    )]
    [SwaggerResponse(200, "Telemetry history retrieved successfully", typeof(PaginatedApiResponse<object>))]
    [SwaggerResponse(400, "Invalid request parameters", typeof(ApiResponse<object>))]
    [SwaggerResponse(404, "Vehicle not found", typeof(ApiResponse<object>))]
    [SwaggerResponse(401, "Unauthorized", typeof(ApiResponse<object>))]
    [SwaggerResponse(500, "Internal server error", typeof(ApiResponse<object>))]
    public async Task<ActionResult<PaginatedApiResponse<object>>> GetTelemetryHistory(
        [FromRoute] Guid vehicleId,
        [FromQuery] DateTime? startDate = null,
        [FromQuery] DateTime? endDate = null,
        [FromQuery] int pageNumber = 1,
        [FromQuery] int pageSize = 50,
        CancellationToken cancellationToken = default)
    {
        // Validate pagination parameters
        if (pageNumber < 1)
            return BadRequest(Error("Page number must be greater than 0"));

        if (pageSize < 1 || pageSize > 1000)
            return BadRequest(Error("Page size must be between 1 and 1000"));

        // Validate date range
        if (startDate.HasValue && endDate.HasValue && startDate > endDate)
            return BadRequest(Error("Start date cannot be after end date"));

        var query = new GetTelemetryHistoryQuery
        {
            VehicleId = vehicleId,
            StartDate = startDate ?? DateTime.UtcNow.AddDays(-30), // Default to last 30 days
            EndDate = endDate ?? DateTime.UtcNow,
            PageNumber = pageNumber,
            PageSize = pageSize
        };

        var result = await Mediator.Send(query, cancellationToken);

        if (!result.Success)
        {
            if (result.ErrorMessage?.Contains("not found", StringComparison.OrdinalIgnoreCase) == true)
            {
                return NotFound(NotFound<object>(result.ErrorMessage));
            }

            return BadRequest(BadRequest<object>(result.ErrorMessage ?? "Failed to retrieve telemetry history"));
        }

        return Ok(PaginatedApiResponse<object>.Success(
            result.History != null ? new[] { result.History } : Enumerable.Empty<object>(),
            result.PageNumber,
            result.PageSize,
            result.TotalCount,
            "Telemetry history retrieved successfully"
        ));
    }

    /// <summary>
    /// Gets the latest telemetry data for multiple vehicles
    /// </summary>
    /// <param name="vehicleIds">List of vehicle IDs</param>
    /// <param name="cancellationToken">The cancellation token</param>
    /// <returns>Latest telemetry data for multiple vehicles</returns>
    [HttpPost("latest/batch")]
    [Authorize(Roles = "Admin,Manager,Driver,User")]
    [SwaggerOperation(
        Summary = "Get latest telemetry for multiple vehicles",
        Description = "Retrieves the most recent telemetry data for multiple vehicles"
    )]
    [SwaggerResponse(200, "Latest telemetry retrieved successfully", typeof(ApiResponse<object>))]
    [SwaggerResponse(400, "Invalid vehicle IDs", typeof(ApiResponse<object>))]
    [SwaggerResponse(401, "Unauthorized", typeof(ApiResponse<object>))]
    [SwaggerResponse(500, "Internal server error", typeof(ApiResponse<object>))]
    public async Task<ActionResult<ApiResponse<object>>> GetLatestTelemetryBatch(
        [FromBody] BatchTelemetryRequest request,
        CancellationToken cancellationToken = default)
    {
        if (request.VehicleIds == null || !request.VehicleIds.Any())
        {
            return BadRequest(Error("Vehicle IDs are required"));
        }

        if (request.VehicleIds.Count() > 100)
        {
            return BadRequest(Error("Maximum 100 vehicle IDs allowed per request"));
        }

        var tasks = request.VehicleIds.Select(async vehicleId =>
        {
            var query = new GetLatestTelemetryQuery { VehicleId = vehicleId };
            var result = await Mediator.Send(query, cancellationToken);
            
            return new
            {
                VehicleId = vehicleId,
                Success = result.Success,
                TelemetryData = result.TelemetryData,
                ErrorMessage = result.ErrorMessage
            };
        });

        var results = await Task.WhenAll(tasks);

        return Ok(Success(results, "Batch telemetry data retrieved successfully"));
    }

    /// <summary>
    /// Gets real-time telemetry statistics for the fleet
    /// </summary>
    /// <param name="cancellationToken">The cancellation token</param>
    /// <returns>Fleet telemetry statistics</returns>
    [HttpGet("stats")]
    [Authorize(Roles = "Admin,Manager")]
    [SwaggerOperation(
        Summary = "Get telemetry statistics",
        Description = "Retrieves real-time telemetry statistics for the entire fleet"
    )]
    [SwaggerResponse(200, "Telemetry statistics retrieved successfully", typeof(ApiResponse<object>))]
    [SwaggerResponse(401, "Unauthorized", typeof(ApiResponse<object>))]
    [SwaggerResponse(403, "Forbidden - insufficient permissions", typeof(ApiResponse<object>))]
    [SwaggerResponse(500, "Internal server error", typeof(ApiResponse<object>))]
    public async Task<ActionResult<ApiResponse<object>>> GetTelemetryStatistics(
        CancellationToken cancellationToken = default)
    {
        // This would typically involve aggregating data from multiple vehicles
        // For now, return a placeholder response
        var stats = new
        {
            TotalVehicles = 0,
            ActiveVehicles = 0,
            AverageSpeed = 0.0,
            AverageFuelLevel = 0.0,
            LastUpdated = DateTime.UtcNow
        };

        return Ok(Success(stats, "Telemetry statistics retrieved successfully"));
    }
}

/// <summary>
/// Request model for batch telemetry queries
/// </summary>
public class BatchTelemetryRequest
{
    /// <summary>
    /// Gets or sets the list of vehicle IDs
    /// </summary>
    public IEnumerable<Guid> VehicleIds { get; set; } = Enumerable.Empty<Guid>();
}
