using FluentValidation;

namespace FleetXQ.Application.Features.Telemetry.Queries.GetLatestTelemetry;

/// <summary>
/// Validator for GetLatestTelemetryQuery
/// </summary>
public sealed class GetLatestTelemetryQueryValidator : AbstractValidator<GetLatestTelemetryQuery>
{
    /// <summary>
    /// Initializes a new instance of the <see cref="GetLatestTelemetryQueryValidator"/> class
    /// </summary>
    public GetLatestTelemetryQueryValidator()
    {
        RuleFor(x => x.MaxAgeHours)
            .GreaterThan(0)
            .WithMessage("Max age hours must be greater than 0")
            .LessThanOrEqualTo(168) // 7 days
            .WithMessage("Max age hours cannot exceed 168 hours (7 days)");
    }
}
