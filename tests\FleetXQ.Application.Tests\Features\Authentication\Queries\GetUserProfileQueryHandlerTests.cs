using FleetXQ.Application.Features.Authentication.Queries.GetUserProfile;
using FleetXQ.Application.Tests.Common;
using FleetXQ.Domain.Entities;
using FleetXQ.Domain.Enums;
using FluentAssertions;
using Moq;

namespace FleetXQ.Application.Tests.Features.Authentication.Queries;

public class GetUserProfileQueryHandlerTests : QueryHandlerTestBase<GetUserProfileQueryHandler>
{
    private readonly GetUserProfileQueryHandler _handler;

    public GetUserProfileQueryHandlerTests()
    {
        _handler = new GetUserProfileQueryHandler(MockUserRepository.Object, Mapper, MockLogger.Object);
    }

    [Fact]
    public async Task Handle_WithUserAccessingOwnProfile_ShouldReturnProfile()
    {
        // Arrange
        var userId = Guid.NewGuid();
        var query = new GetUserProfileQuery(userId, userId);

        var requestingUser = new User("testuser", "<EMAIL>", "Test", "User", UserRole.User);
        var targetUser = requestingUser; // Same user

        MockUserRepository.Setup(x => x.GetByIdAsync(userId, It.IsAny<CancellationToken>()))
            .ReturnsAsync(requestingUser);

        // Act
        var result = await _handler.Handle(query, CreateCancellationToken());

        // Assert
        result.Should().NotBeNull();
        result.Success.Should().BeTrue();
        result.Found.Should().BeTrue();
        result.AccessDenied.Should().BeFalse();
        result.Profile.Should().NotBeNull();
        result.ErrorMessage.Should().BeNull();

        var profile = result.Profile!;
        profile.Id.Should().Be(targetUser.Id);
        profile.Username.Should().Be("testuser");
        profile.Email.Should().Be("<EMAIL>");
        profile.FirstName.Should().Be("Test");
        profile.LastName.Should().Be("User");
        profile.FullName.Should().Be("Test User");
        profile.Role.Should().Be(UserRole.User);
        profile.IsActive.Should().BeTrue();
        profile.Permissions.Should().NotBeEmpty();
        profile.Permissions.Should().Contain("profile.read");
        profile.Permissions.Should().Contain("profile.write");

        MockUserRepository.Verify(x => x.GetByIdAsync(userId, It.IsAny<CancellationToken>()), Times.Exactly(2)); // Once for requesting user, once for target user
        VerifyInformationLogged(Times.AtLeastOnce);
    }

    [Fact]
    public async Task Handle_WithAdminAccessingAnyProfile_ShouldReturnProfile()
    {
        // Arrange
        var adminId = Guid.NewGuid();
        var targetUserId = Guid.NewGuid();
        var query = new GetUserProfileQuery(targetUserId, adminId) { IncludeSensitiveInfo = true };

        var adminUser = new User("admin", "<EMAIL>", "Admin", "User", UserRole.Admin);
        var targetUser = new User("testuser", "<EMAIL>", "Test", "User", UserRole.User);

        MockUserRepository.Setup(x => x.GetByIdAsync(adminId, It.IsAny<CancellationToken>()))
            .ReturnsAsync(adminUser);

        MockUserRepository.Setup(x => x.GetByIdAsync(targetUserId, It.IsAny<CancellationToken>()))
            .ReturnsAsync(targetUser);

        // Act
        var result = await _handler.Handle(query, CreateCancellationToken());

        // Assert
        result.Should().NotBeNull();
        result.Success.Should().BeTrue();
        result.Found.Should().BeTrue();
        result.AccessDenied.Should().BeFalse();
        result.Profile.Should().NotBeNull();

        var profile = result.Profile!;
        profile.Id.Should().Be(targetUser.Id);
        profile.Username.Should().Be("testuser");
        profile.Role.Should().Be(UserRole.User);
        profile.Permissions.Should().NotBeEmpty();
        profile.Permissions.Should().Contain("vehicles.read");
        profile.Permissions.Should().Contain("alerts.read");

        // Admin should see sensitive info
        profile.IsLockedOut.Should().BeFalse();
    }

    [Fact]
    public async Task Handle_WithManagerAccessingUserProfile_ShouldReturnProfile()
    {
        // Arrange
        var managerId = Guid.NewGuid();
        var targetUserId = Guid.NewGuid();
        var query = new GetUserProfileQuery(targetUserId, managerId);

        var managerUser = new User("manager", "<EMAIL>", "Manager", "User", UserRole.Manager);
        var targetUser = new User("testuser", "<EMAIL>", "Test", "User", UserRole.User);

        MockUserRepository.Setup(x => x.GetByIdAsync(managerId, It.IsAny<CancellationToken>()))
            .ReturnsAsync(managerUser);

        MockUserRepository.Setup(x => x.GetByIdAsync(targetUserId, It.IsAny<CancellationToken>()))
            .ReturnsAsync(targetUser);

        // Act
        var result = await _handler.Handle(query, CreateCancellationToken());

        // Assert
        result.Should().NotBeNull();
        result.Success.Should().BeTrue();
        result.Found.Should().BeTrue();
        result.AccessDenied.Should().BeFalse();
        result.Profile.Should().NotBeNull();

        var profile = result.Profile!;
        profile.Role.Should().Be(UserRole.User);
        profile.Permissions.Should().Contain("vehicles.read");
        profile.Permissions.Should().Contain("reports.read");
    }

    [Fact]
    public async Task Handle_WithUserAccessingOtherUserProfile_ShouldReturnAccessDenied()
    {
        // Arrange
        var requestingUserId = Guid.NewGuid();
        var targetUserId = Guid.NewGuid();
        var query = new GetUserProfileQuery(targetUserId, requestingUserId);

        var requestingUser = new User("user1", "<EMAIL>", "User", "One", UserRole.User);

        MockUserRepository.Setup(x => x.GetByIdAsync(requestingUserId, It.IsAny<CancellationToken>()))
            .ReturnsAsync(requestingUser);

        // Act
        var result = await _handler.Handle(query, CreateCancellationToken());

        // Assert
        result.Should().NotBeNull();
        result.Success.Should().BeFalse();
        result.Found.Should().BeFalse();
        result.AccessDenied.Should().BeTrue();
        result.Profile.Should().BeNull();
        result.ErrorMessage.Should().Contain("Access denied");

        MockUserRepository.Verify(x => x.GetByIdAsync(targetUserId, It.IsAny<CancellationToken>()), Times.Never);
        VerifyWarningLogged();
    }

    [Fact]
    public async Task Handle_WithNonExistentRequestingUser_ShouldReturnAccessDenied()
    {
        // Arrange
        var requestingUserId = Guid.NewGuid();
        var targetUserId = Guid.NewGuid();
        var query = new GetUserProfileQuery(targetUserId, requestingUserId);

        MockUserRepository.Setup(x => x.GetByIdAsync(requestingUserId, It.IsAny<CancellationToken>()))
            .ReturnsAsync((User?)null);

        // Act
        var result = await _handler.Handle(query, CreateCancellationToken());

        // Assert
        result.Should().NotBeNull();
        result.Success.Should().BeFalse();
        result.AccessDenied.Should().BeTrue();
        result.Profile.Should().BeNull();
        result.ErrorMessage.Should().Contain("Access denied");

        MockUserRepository.Verify(x => x.GetByIdAsync(targetUserId, It.IsAny<CancellationToken>()), Times.Never);
        VerifyWarningLogged();
    }

    [Fact]
    public async Task Handle_WithNonExistentTargetUser_ShouldReturnNotFound()
    {
        // Arrange
        var requestingUserId = Guid.NewGuid();
        var targetUserId = Guid.NewGuid();
        var query = new GetUserProfileQuery(targetUserId, requestingUserId);

        var requestingUser = new User("admin", "<EMAIL>", "Admin", "User", UserRole.Admin);

        MockUserRepository.Setup(x => x.GetByIdAsync(requestingUserId, It.IsAny<CancellationToken>()))
            .ReturnsAsync(requestingUser);

        MockUserRepository.Setup(x => x.GetByIdAsync(targetUserId, It.IsAny<CancellationToken>()))
            .ReturnsAsync((User?)null);

        // Act
        var result = await _handler.Handle(query, CreateCancellationToken());

        // Assert
        result.Should().NotBeNull();
        result.Success.Should().BeFalse();
        result.Found.Should().BeFalse();
        result.AccessDenied.Should().BeFalse();
        result.Profile.Should().BeNull();
        result.ErrorMessage.Should().Contain("User not found");

        VerifyInformationLogged();
    }

    [Fact]
    public async Task Handle_WithRepositoryException_ShouldReturnFailure()
    {
        // Arrange
        var requestingUserId = Guid.NewGuid();
        var targetUserId = Guid.NewGuid();
        var query = new GetUserProfileQuery(targetUserId, requestingUserId);

        MockUserRepository.Setup(x => x.GetByIdAsync(requestingUserId, It.IsAny<CancellationToken>()))
            .ThrowsAsync(new InvalidOperationException("Database error"));

        // Act
        var result = await _handler.Handle(query, CreateCancellationToken());

        // Assert
        result.Should().NotBeNull();
        result.Success.Should().BeFalse();
        result.Profile.Should().BeNull();
        result.ErrorMessage.Should().Contain("error occurred");

        VerifyErrorLogged();
    }

    [Theory]
    [InlineData(UserRole.Admin, true)]
    [InlineData(UserRole.Manager, true)]
    [InlineData(UserRole.Driver, false)]
    [InlineData(UserRole.User, false)]
    public async Task Handle_WithDifferentRoles_ShouldRespectAccessControl(UserRole requestingUserRole, bool shouldHaveAccess)
    {
        // Arrange
        var requestingUserId = Guid.NewGuid();
        var targetUserId = Guid.NewGuid();
        var query = new GetUserProfileQuery(targetUserId, requestingUserId);

        var requestingUser = new User("user", "<EMAIL>", "User", "Name", requestingUserRole);
        var targetUser = new User("target", "<EMAIL>", "Target", "User", UserRole.User);

        MockUserRepository.Setup(x => x.GetByIdAsync(requestingUserId, It.IsAny<CancellationToken>()))
            .ReturnsAsync(requestingUser);

        if (shouldHaveAccess)
        {
            MockUserRepository.Setup(x => x.GetByIdAsync(targetUserId, It.IsAny<CancellationToken>()))
                .ReturnsAsync(targetUser);
        }

        // Act
        var result = await _handler.Handle(query, CreateCancellationToken());

        // Assert
        result.Should().NotBeNull();
        result.Success.Should().Be(shouldHaveAccess);
        result.AccessDenied.Should().Be(!shouldHaveAccess);

        if (shouldHaveAccess)
        {
            result.Profile.Should().NotBeNull();
            MockUserRepository.Verify(x => x.GetByIdAsync(targetUserId, It.IsAny<CancellationToken>()), Times.Once);
        }
        else
        {
            result.Profile.Should().BeNull();
            MockUserRepository.Verify(x => x.GetByIdAsync(targetUserId, It.IsAny<CancellationToken>()), Times.Never);
        }
    }

    [Theory]
    [InlineData(UserRole.Admin, 7)] // Admin has most permissions
    [InlineData(UserRole.Manager, 6)] // Manager has fewer permissions
    [InlineData(UserRole.Driver, 2)] // Driver has minimal permissions
    [InlineData(UserRole.User, 4)] // User has basic permissions
    public async Task Handle_WithDifferentUserRoles_ShouldReturnCorrectPermissions(UserRole role, int expectedMinPermissions)
    {
        // Arrange
        var userId = Guid.NewGuid();
        var query = new GetUserProfileQuery(userId, userId); // User accessing own profile

        var user = new User("testuser", "<EMAIL>", "Test", "User", role);

        MockUserRepository.Setup(x => x.GetByIdAsync(userId, It.IsAny<CancellationToken>()))
            .ReturnsAsync(user);

        // Act
        var result = await _handler.Handle(query, CreateCancellationToken());

        // Assert
        result.Should().NotBeNull();
        result.Success.Should().BeTrue();
        result.Profile.Should().NotBeNull();
        result.Profile!.Role.Should().Be(role);
        result.Profile.Permissions.Should().HaveCountGreaterOrEqualTo(expectedMinPermissions);

        // Verify role-specific permissions
        switch (role)
        {
            case UserRole.Admin:
                result.Profile.Permissions.Should().Contain("system.admin");
                result.Profile.Permissions.Should().Contain("users.delete");
                break;
            case UserRole.Manager:
                result.Profile.Permissions.Should().Contain("users.read");
                result.Profile.Permissions.Should().Contain("reports.write");
                result.Profile.Permissions.Should().NotContain("system.admin");
                break;
            case UserRole.Driver:
                result.Profile.Permissions.Should().Contain("profile.read");
                result.Profile.Permissions.Should().NotContain("users.read");
                break;
            case UserRole.User:
                result.Profile.Permissions.Should().Contain("vehicles.read");
                result.Profile.Permissions.Should().Contain("alerts.read");
                result.Profile.Permissions.Should().NotContain("users.write");
                break;
        }
    }
}
