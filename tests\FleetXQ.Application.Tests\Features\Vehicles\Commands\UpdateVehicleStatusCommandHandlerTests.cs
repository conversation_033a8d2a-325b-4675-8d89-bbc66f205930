using FleetXQ.Application.Features.Vehicles.Commands.UpdateVehicleStatus;
using FleetXQ.Application.Tests.Common;
using FleetXQ.Domain.Entities;
using FleetXQ.Domain.Enums;
using FluentAssertions;
using Moq;

namespace FleetXQ.Application.Tests.Features.Vehicles.Commands;

public class UpdateVehicleStatusCommandHandlerTests : CommandHandlerTestBase<UpdateVehicleStatusCommandHandler>
{
    private readonly UpdateVehicleStatusCommandHandler _handler;

    public UpdateVehicleStatusCommandHandlerTests()
    {
        _handler = new UpdateVehicleStatusCommandHandler(MockVehicleRepository.Object, MockLogger.Object);
    }

    [Fact]
    public async Task Handle_WithValidCommand_ShouldUpdateStatusSuccessfully()
    {
        // Arrange
        var vehicleId = Guid.NewGuid();
        var command = new UpdateVehicleStatusCommand
        {
            VehicleId = vehicleId,
            Status = VehicleStatus.Maintenance,
            Reason = "Scheduled maintenance",
            ChangedBy = Guid.NewGuid(),
            Context = "Monthly maintenance check"
        };

        var vehicle = new Vehicle("Test Vehicle", "ABC-123", "Car");
        MockVehicleRepository.Setup(x => x.GetByIdAsync(vehicleId, It.IsAny<CancellationToken>()))
            .ReturnsAsync(vehicle);

        MockVehicleRepository.Setup(x => x.UpdateAsync(It.IsAny<Vehicle>(), It.IsAny<CancellationToken>()))
            .Returns(Task.CompletedTask);

        // Act
        var result = await _handler.Handle(command, CreateCancellationToken());

        // Assert
        result.Should().NotBeNull();
        result.Success.Should().BeTrue();
        result.VehicleId.Should().Be(vehicleId);
        result.PreviousStatus.Should().Be(VehicleStatus.Active);
        result.NewStatus.Should().Be(VehicleStatus.Maintenance);
        result.ErrorMessage.Should().BeNull();

        MockVehicleRepository.Verify(x => x.GetByIdAsync(vehicleId, It.IsAny<CancellationToken>()), Times.Once);
        VerifyRepositoryUpdateCalled(MockVehicleRepository);
        VerifyInformationLogged(Times.AtLeastOnce);
    }

    [Fact]
    public async Task Handle_WithNonExistentVehicle_ShouldReturnFailure()
    {
        // Arrange
        var vehicleId = Guid.NewGuid();
        var command = new UpdateVehicleStatusCommand
        {
            VehicleId = vehicleId,
            Status = VehicleStatus.Maintenance,
            Reason = "Scheduled maintenance"
        };

        MockVehicleRepository.Setup(x => x.GetByIdAsync(vehicleId, It.IsAny<CancellationToken>()))
            .ReturnsAsync((Vehicle?)null);

        // Act
        var result = await _handler.Handle(command, CreateCancellationToken());

        // Assert
        result.Should().NotBeNull();
        result.Success.Should().BeFalse();
        result.VehicleId.Should().Be(vehicleId);
        result.ErrorMessage.Should().Contain("not found");

        MockVehicleRepository.Verify(x => x.UpdateAsync(It.IsAny<Vehicle>(), It.IsAny<CancellationToken>()), Times.Never);
        VerifyWarningLogged();
    }

    [Fact]
    public async Task Handle_WithSameStatus_ShouldReturnSuccessWithoutUpdate()
    {
        // Arrange
        var vehicleId = Guid.NewGuid();
        var command = new UpdateVehicleStatusCommand
        {
            VehicleId = vehicleId,
            Status = VehicleStatus.Active,
            Reason = "No change needed"
        };

        var vehicle = new Vehicle("Test Vehicle", "ABC-123", "Car");
        MockVehicleRepository.Setup(x => x.GetByIdAsync(vehicleId, It.IsAny<CancellationToken>()))
            .ReturnsAsync(vehicle);

        // Act
        var result = await _handler.Handle(command, CreateCancellationToken());

        // Assert
        result.Should().NotBeNull();
        result.Success.Should().BeTrue();
        result.VehicleId.Should().Be(vehicleId);
        result.PreviousStatus.Should().Be(VehicleStatus.Active);
        result.NewStatus.Should().Be(VehicleStatus.Active);

        MockVehicleRepository.Verify(x => x.UpdateAsync(It.IsAny<Vehicle>(), It.IsAny<CancellationToken>()), Times.Never);
        VerifyInformationLogged(Times.AtLeastOnce);
    }

    [Fact]
    public async Task Handle_WithInvalidStatusChange_ShouldReturnFailure()
    {
        // Arrange
        var vehicleId = Guid.NewGuid();
        var command = new UpdateVehicleStatusCommand
        {
            VehicleId = vehicleId,
            Status = VehicleStatus.Retired,
            Reason = "Invalid change"
        };

        var vehicle = new Vehicle("Test Vehicle", "ABC-123", "Car");
        MockVehicleRepository.Setup(x => x.GetByIdAsync(vehicleId, It.IsAny<CancellationToken>()))
            .ReturnsAsync(vehicle);

        MockVehicleRepository.Setup(x => x.UpdateAsync(It.IsAny<Vehicle>(), It.IsAny<CancellationToken>()))
            .ThrowsAsync(new ArgumentException("Invalid status change"));

        // Act
        var result = await _handler.Handle(command, CreateCancellationToken());

        // Assert
        result.Should().NotBeNull();
        result.Success.Should().BeFalse();
        result.VehicleId.Should().Be(vehicleId);
        result.ErrorMessage.Should().Contain("Invalid status change");

        VerifyWarningLogged();
    }

    [Fact]
    public async Task Handle_WithRepositoryException_ShouldReturnFailure()
    {
        // Arrange
        var vehicleId = Guid.NewGuid();
        var command = new UpdateVehicleStatusCommand
        {
            VehicleId = vehicleId,
            Status = VehicleStatus.Offline,
            Reason = "System error"
        };

        MockVehicleRepository.Setup(x => x.GetByIdAsync(vehicleId, It.IsAny<CancellationToken>()))
            .ThrowsAsync(new InvalidOperationException("Database error"));

        // Act
        var result = await _handler.Handle(command, CreateCancellationToken());

        // Assert
        result.Should().NotBeNull();
        result.Success.Should().BeFalse();
        result.VehicleId.Should().Be(vehicleId);
        result.ErrorMessage.Should().Contain("error occurred");

        VerifyErrorLogged();
    }

    [Theory]
    [InlineData(VehicleStatus.Active)]
    [InlineData(VehicleStatus.Maintenance)]
    [InlineData(VehicleStatus.Offline)]
    [InlineData(VehicleStatus.Retired)]
    public async Task Handle_WithAllValidStatuses_ShouldUpdateSuccessfully(VehicleStatus newStatus)
    {
        // Arrange
        var vehicleId = Guid.NewGuid();
        var command = new UpdateVehicleStatusCommand
        {
            VehicleId = vehicleId,
            Status = newStatus,
            Reason = $"Changing to {newStatus}",
            ChangedBy = Guid.NewGuid()
        };

        var vehicle = new Vehicle("Test Vehicle", "ABC-123", "Car");
        MockVehicleRepository.Setup(x => x.GetByIdAsync(vehicleId, It.IsAny<CancellationToken>()))
            .ReturnsAsync(vehicle);

        MockVehicleRepository.Setup(x => x.UpdateAsync(It.IsAny<Vehicle>(), It.IsAny<CancellationToken>()))
            .Returns(Task.CompletedTask);

        // Act
        var result = await _handler.Handle(command, CreateCancellationToken());

        // Assert
        result.Should().NotBeNull();
        result.Success.Should().BeTrue();
        result.NewStatus.Should().Be(newStatus);

        if (newStatus != VehicleStatus.Active) // Only update if status actually changes
        {
            VerifyRepositoryUpdateCalled(MockVehicleRepository);
        }
    }
}
