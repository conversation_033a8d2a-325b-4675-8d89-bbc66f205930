# FleetXQ Backend Development Prompts

## Phase 1: Clean Architecture Foundation

### Prompt 1.1: Project Structure Setup
**Objective**: Create the Clean Architecture project structure for FleetXQ backend following the specifications in `docs/backend.md`.

**Context**:
- .NET 8 Web API with Clean Architecture layers
- Database-first EF Core approach
- CQRS with MediatR pattern
- JWT authentication with role-based authorization

**Instructions**:
1. Create solution structure:
   ```
   /src
     /FleetXQ.Api
     /FleetXQ.Application  
     /FleetXQ.Domain
     /FleetXQ.Infrastructure
   /tests
     /FleetXQ.Application.Tests
     /FleetXQ.Infrastructure.Tests
   ```
2. Configure project references following Clean Architecture dependencies
3. Install required NuGet packages per layer:
   - Api: ASP.NET Core, SignalR, Serilog
   - Application: MediatR, FluentValidation, AutoMapper
   - Domain: No external dependencies
   - Infrastructure: EF Core, JWT, external service clients
4. Set up `Directory.Build.props` with consistent settings
5. Configure namespace structure following conventions

**Expected Deliverables**:
- Complete solution structure with proper project references
- NuGet packages installed per layer requirements
- Namespace conventions established
- Build configuration files

**Validation Criteria**:
- Solution builds without errors
- Project dependencies follow Clean Architecture rules
- All required packages installed
- Namespace structure follows conventions

### Prompt 1.2: Domain Layer Implementation
**Objective**: Implement the Domain layer with entities, value objects, and domain events for FleetXQ.

**Context**:
- Domain entities should match database schema from `docs/complete_sql_setup_for_asynchronous_project.sql`
- Include domain events for real-time features
- Implement business rules and validation
- No external dependencies allowed in Domain layer

**Instructions**:
1. Create domain entities for core aggregates:
   - `Vehicle` with telemetry data management
   - `Driver` with assignment and performance tracking
   - `User` with authentication and authorization
   - `Alert` with severity and acknowledgment logic
   - `Trip` with analytics and reporting
2. Implement value objects for:
   - `Location` (latitude, longitude)
   - `FuelLevel` with validation
   - `Speed` with unit conversion
   - `VehicleStatus` enumeration
3. Create domain events:
   - `TelemetryDataReceived`
   - `AlertTriggered`
   - `VehicleStatusChanged`
   - `DriverAssigned`
4. Implement repository interfaces for each aggregate root
5. Add domain services for complex business logic

**Expected Deliverables**:
- Complete domain entity models
- Value objects with proper validation
- Domain events with clear contracts
- Repository interfaces
- Domain services for business logic

**Validation Criteria**:
- Entities encapsulate business rules properly
- Value objects are immutable and validated
- Domain events follow naming conventions
- No external dependencies in Domain layer

### Prompt 1.3: Application Layer with CQRS
**Objective**: Implement the Application layer using CQRS pattern with MediatR for FleetXQ business logic.

**Context**:
- CQRS separates read and write operations
- MediatR handles request/response patterns
- FluentValidation for input validation
- AutoMapper for DTO mapping

**Instructions**:
1. Create CQRS structure for each feature:
   ```
   /Application/Features/
     /Vehicles/
       /Commands/ - CreateVehicle, UpdateVehicleStatus
       /Queries/ - GetVehicleById, GetVehicleList
       /DTOs/ - VehicleDto, VehicleListDto
     /Telemetry/
       /Commands/ - ProcessTelemetryData
       /Queries/ - GetLatestTelemetry, GetTelemetryHistory
     /Authentication/
       /Commands/ - LoginUser, RefreshToken
       /Queries/ - GetUserProfile
   ```
2. Implement command handlers with:
   - Input validation using FluentValidation
   - Business logic orchestration
   - Repository pattern usage
   - Domain event publishing
3. Implement query handlers with:
   - Read-optimized data access
   - DTO projection
   - Caching where appropriate
4. Create AutoMapper profiles for entity-DTO mapping
5. Set up MediatR pipeline behaviors for cross-cutting concerns

**Expected Deliverables**:
- Complete CQRS implementation for all features
- Command and query handlers with validation
- DTO classes with proper mapping
- MediatR pipeline behaviors
- Validation rules for all commands

**Validation Criteria**:
- All commands and queries have corresponding handlers
- Validation works correctly for all inputs
- DTO mapping preserves data integrity
- Pipeline behaviors handle cross-cutting concerns

## Phase 2: API and Real-time Features

### Prompt 2.1: Web API Controllers Implementation
**Objective**: Create RESTful API controllers following Clean Architecture and FleetXQ requirements.

**Context**:
- Controllers should be thin and delegate to MediatR
- JWT authentication with role-based authorization
- Proper HTTP status codes and error handling
- API versioning and documentation

**Instructions**:
1. Create controllers for each domain area:
   - `VehiclesController` - CRUD operations, status updates
   - `TelemetryController` - Real-time data ingestion and queries
   - `DriversController` - Driver management and assignments
   - `AuthController` - Login, logout, token refresh
   - `AlertsController` - Alert management and acknowledgment
   - `DashboardController` - Analytics and reporting
2. Implement proper HTTP methods and status codes:
   - GET for queries (200, 404)
   - POST for creation (201, 400, 409)
   - PUT for updates (200, 404, 400)
   - DELETE for removal (204, 404)
3. Add authorization attributes based on user roles:
   - Admin: Full access to all endpoints
   - Manager: Read/write access to operational data
   - Driver: Limited access to assigned vehicles
   - User: Read-only access to basic information
4. Implement proper error handling and validation responses
5. Add API documentation using Swagger/OpenAPI

**Expected Deliverables**:
- Complete set of API controllers
- Proper HTTP method implementations
- Role-based authorization on all endpoints
- Comprehensive error handling
- Swagger documentation

**Validation Criteria**:
- All endpoints return appropriate HTTP status codes
- Authorization works correctly for each role
- Error responses follow consistent format
- API documentation is complete and accurate

### Prompt 2.2: SignalR Hub Implementation
**Objective**: Implement SignalR hubs for real-time telemetry and alert notifications.

**Context**:
- Real-time vehicle tracking requires SignalR
- Push notifications for alerts and status changes
- Group management for vehicle subscriptions
- Integration with domain events

**Instructions**:
1. Create SignalR hubs:
   - `TelemetryHub` for real-time vehicle data
   - `AlertHub` for alert notifications
   - `DashboardHub` for live dashboard updates
2. Implement hub methods:
   - `SubscribeToVehicle(vehicleId)` - Join vehicle-specific group
   - `UnsubscribeFromVehicle(vehicleId)` - Leave vehicle group
   - `SubscribeToAlerts()` - Join alerts group
   - `GetVehicleStatus(vehicleId)` - Request current status
3. Create SignalR services for domain event handling:
   - Listen to `TelemetryDataReceived` events
   - Push updates to subscribed clients
   - Handle connection management
4. Implement authentication for SignalR connections
5. Add connection state management and error handling

**Expected Deliverables**:
- SignalR hubs with proper method implementations
- Domain event integration for real-time updates
- Authentication and authorization for connections
- Connection state management
- Error handling and logging

**Validation Criteria**:
- Real-time updates work correctly
- Group subscriptions function properly
- Authentication prevents unauthorized access
- Connection errors are handled gracefully

### Prompt 2.3: Authentication and Authorization System
**Objective**: Implement JWT-based authentication with role-based authorization for FleetXQ.

**Context**:
- JWT tokens with user claims and roles
- Password hashing and validation
- Token refresh mechanism
- Role-based access control throughout API

**Instructions**:
1. Create authentication service:
   - User login with email/password
   - Password hashing using BCrypt or similar
   - JWT token generation with claims
   - Token validation and refresh
2. Implement authorization service:
   - Role-based access control
   - Permission checking for specific operations
   - User context management
3. Create JWT middleware:
   - Token validation on each request
   - User context injection
   - Proper error responses for auth failures
4. Set up role-based authorization:
   - Admin: Full system access
   - Manager: Operational data access
   - Driver: Limited to assigned vehicles
   - User: Read-only access
5. Implement security best practices:
   - Token expiration and refresh
   - Secure password requirements
   - Rate limiting for auth endpoints

**Expected Deliverables**:
- Complete authentication service
- JWT token generation and validation
- Role-based authorization system
- Security middleware implementation
- Password security implementation

**Validation Criteria**:
- Authentication works with valid credentials
- Invalid credentials are properly rejected
- JWT tokens contain correct claims
- Role-based access control functions correctly

## Phase 3: Integration and Testing

### Prompt 3.1: Application Layer Unit Testing
**Objective**: Create comprehensive unit tests for Application layer using xUnit and Moq.

**Context**:
- Test command and query handlers in isolation
- Mock repository dependencies
- Validate business logic and error handling
- Achieve minimum 80% code coverage

**Instructions**:
1. Set up test project structure:
   ```
   /tests/FleetXQ.Application.Tests/
     /Features/
       /Vehicles/
         /Commands/ - CreateVehicleCommandHandlerTests
         /Queries/ - GetVehicleQueryHandlerTests
       /Telemetry/
       /Authentication/
   ```
2. Create test base classes:
   - `TestBase` with common mocking setup
   - `CommandHandlerTestBase<THandler>` for command tests
   - `QueryHandlerTestBase<THandler>` for query tests
3. Implement tests for each handler:
   - Happy path scenarios
   - Validation failure cases
   - Business rule violations
   - Repository exceptions
4. Mock all external dependencies:
   - Repository interfaces
   - Logger instances
   - SignalR hub contexts
   - External services
5. Use FluentAssertions for readable test assertions

**Expected Deliverables**:
- Complete unit test suite for all handlers
- Mock configurations for dependencies
- Test base classes for common functionality
- Code coverage report showing 80%+ coverage

**Validation Criteria**:
- All command and query handlers have tests
- Tests cover success and failure scenarios
- Mocks are properly configured
- Tests run quickly and reliably

### Prompt 3.2: Integration Testing Setup
**Objective**: Create integration tests for API endpoints and SignalR functionality.

**Context**:
- Test complete request/response cycles
- Use TestServer for API testing
- Test SignalR connections and messaging
- Use in-memory database for isolation

**Instructions**:
1. Set up integration test infrastructure:
   - `WebApplicationFactory` configuration
   - In-memory database setup
   - Test authentication tokens
   - SignalR test client setup
2. Create API integration tests:
   - Authentication endpoints
   - CRUD operations for all controllers
   - Authorization scenarios
   - Error handling validation
3. Create SignalR integration tests:
   - Connection establishment
   - Group subscription/unsubscription
   - Real-time message delivery
   - Authentication validation
4. Implement test data builders:
   - User accounts with different roles
   - Vehicle and driver test data
   - Telemetry data generators
5. Add performance and load testing scenarios

**Expected Deliverables**:
- Integration test infrastructure
- Complete API endpoint tests
- SignalR functionality tests
- Test data builders and utilities
- Performance test scenarios

**Validation Criteria**:
- All API endpoints tested end-to-end
- SignalR real-time features work correctly
- Authentication and authorization tested
- Tests run in isolation without side effects

### Prompt 3.3: Logging and Monitoring Implementation
**Objective**: Implement comprehensive logging and monitoring using Serilog and application insights.

**Context**:
- Structured logging throughout application
- Performance monitoring and metrics
- Error tracking and alerting
- Audit logging for security events

**Instructions**:
1. Configure Serilog with multiple sinks:
   - Console output for development
   - File logging with rolling policies
   - Application Insights for production
   - Structured logging with context enrichment
2. Implement logging throughout application:
   - Request/response logging middleware
   - Command and query handler logging
   - Authentication and authorization events
   - SignalR connection and message logging
3. Add performance monitoring:
   - Request duration tracking
   - Database query performance
   - SignalR connection metrics
   - Memory and CPU usage monitoring
4. Create custom metrics and dashboards:
   - API endpoint usage statistics
   - Real-time connection counts
   - Error rates and types
   - Business metrics (vehicles tracked, alerts generated)
5. Set up alerting for critical issues:
   - High error rates
   - Performance degradation
   - Authentication failures
   - System resource exhaustion

**Expected Deliverables**:
- Serilog configuration with multiple sinks
- Comprehensive logging throughout application
- Performance monitoring implementation
- Custom metrics and dashboards
- Alerting configuration for critical issues

**Validation Criteria**:
- Logs provide sufficient detail for troubleshooting
- Performance metrics accurately reflect system health
- Alerts trigger appropriately for issues
- Monitoring data helps optimize system performance