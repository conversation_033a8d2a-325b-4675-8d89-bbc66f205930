using FluentValidation;

namespace FleetXQ.Application.Features.Vehicles.Queries.GetVehicleList;

/// <summary>
/// Validator for GetVehicleListQuery
/// </summary>
public sealed class GetVehicleListQueryValidator : AbstractValidator<GetVehicleListQuery>
{
    /// <summary>
    /// Initializes a new instance of the <see cref="GetVehicleListQueryValidator"/> class
    /// </summary>
    public GetVehicleListQueryValidator()
    {
        RuleFor(x => x.PageNumber)
            .GreaterThan(0)
            .WithMessage("Page number must be greater than 0");

        RuleFor(x => x.PageSize)
            .GreaterThan(0)
            .WithMessage("Page size must be greater than 0")
            .LessThanOrEqualTo(100)
            .WithMessage("Page size cannot exceed 100");

        RuleFor(x => x.VehicleType)
            .MaximumLength(50)
            .WithMessage("Vehicle type cannot exceed 50 characters")
            .When(x => !string.IsNullOrWhiteSpace(x.VehicleType));

        RuleFor(x => x.SearchTerm)
            .MaximumLength(100)
            .WithMessage("Search term cannot exceed 100 characters")
            .When(x => !string.IsNullOrWhiteSpace(x.SearchTerm));

        RuleFor(x => x.SortBy)
            .NotEmpty()
            .WithMessage("Sort field is required")
            .Must(BeValidSortField)
            .WithMessage("Invalid sort field. Valid fields are: VehicleName, LicensePlate, VehicleType, Brand, Model, Status, CreatedAt");
    }

    /// <summary>
    /// Validates if the sort field is valid
    /// </summary>
    /// <param name="sortBy">The sort field</param>
    /// <returns>True if valid, false otherwise</returns>
    private static bool BeValidSortField(string sortBy)
    {
        var validSortFields = new[] 
        { 
            "VehicleName", "LicensePlate", "VehicleType", "Brand", "Model", 
            "Status", "CreatedAt", "UpdatedAt", "CurrentMileage" 
        };
        
        return validSortFields.Contains(sortBy, StringComparer.OrdinalIgnoreCase);
    }
}
