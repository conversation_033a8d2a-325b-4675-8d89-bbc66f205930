using FleetXQ.Domain.Interfaces;
using Microsoft.Extensions.Logging;
using Moq;

namespace FleetXQ.Application.Tests.Common;

/// <summary>
/// Base class for query handler tests
/// </summary>
/// <typeparam name="THandler">The query handler type</typeparam>
public abstract class QueryHandlerTestBase<THandler> : TestBase
    where THandler : class
{
    protected readonly Mock<ILogger<THandler>> MockLogger;

    protected QueryHandlerTestBase()
    {
        MockLogger = CreateMockLogger<THandler>();
    }

    /// <summary>
    /// Verifies that the handler logged an information message
    /// </summary>
    /// <param name="times">The expected number of times (default: once)</param>
    protected void VerifyInformationLogged(Times? times = null)
    {
        VerifyLoggerCalled(MockLogger, LogLevel.Information, times);
    }

    /// <summary>
    /// Verifies that the handler logged a warning message
    /// </summary>
    /// <param name="times">The expected number of times (default: once)</param>
    protected void VerifyWarningLogged(Times? times = null)
    {
        VerifyLoggerCalled(MockLogger, LogLevel.Warning, times);
    }

    /// <summary>
    /// Verifies that the handler logged an error message
    /// </summary>
    /// <param name="times">The expected number of times (default: once)</param>
    protected void VerifyErrorLogged(Times? times = null)
    {
        VerifyLoggerCalled(MockLogger, LogLevel.Error, times);
    }

    /// <summary>
    /// Verifies that repository get by id was called
    /// </summary>
    /// <typeparam name="TEntity">The entity type</typeparam>
    /// <param name="mockRepository">The mock repository</param>
    /// <param name="id">The expected entity ID</param>
    /// <param name="times">The expected number of times (default: once)</param>
    protected static void VerifyRepositoryGetByIdCalled<TEntity>(Mock<IRepository<TEntity>> mockRepository, Guid id, Times? times = null)
        where TEntity : class
    {
        mockRepository.Verify(
            x => x.GetByIdAsync(id, It.IsAny<CancellationToken>()),
            times ?? Times.Once);
    }

    /// <summary>
    /// Verifies that repository get all was called
    /// </summary>
    /// <typeparam name="TEntity">The entity type</typeparam>
    /// <param name="mockRepository">The mock repository</param>
    /// <param name="times">The expected number of times (default: once)</param>
    protected static void VerifyRepositoryGetAllCalled<TEntity>(Mock<IRepository<TEntity>> mockRepository, Times? times = null)
        where TEntity : class
    {
        mockRepository.Verify(
            x => x.GetAllAsync(It.IsAny<CancellationToken>()),
            times ?? Times.Once);
    }
}
