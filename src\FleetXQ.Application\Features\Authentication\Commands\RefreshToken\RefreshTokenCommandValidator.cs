using FluentValidation;

namespace FleetXQ.Application.Features.Authentication.Commands.RefreshToken;

/// <summary>
/// Validator for RefreshTokenCommand
/// </summary>
public sealed class RefreshTokenCommandValidator : AbstractValidator<RefreshTokenCommand>
{
    /// <summary>
    /// Initializes a new instance of the <see cref="RefreshTokenCommandValidator"/> class
    /// </summary>
    public RefreshTokenCommandValidator()
    {
        RuleFor(x => x.AccessToken)
            .NotEmpty()
            .WithMessage("Access token is required")
            .MaximumLength(2000)
            .WithMessage("Access token cannot exceed 2000 characters");

        RuleFor(x => x.RefreshToken)
            .NotEmpty()
            .WithMessage("Refresh token is required")
            .MaximumLength(500)
            .WithMessage("Refresh token cannot exceed 500 characters");

        RuleFor(x => x.ClientIpAddress)
            .MaximumLength(45) // IPv6 max length
            .WithMessage("Client IP address cannot exceed 45 characters")
            .When(x => !string.IsNullOrWhiteSpace(x.ClientIpAddress));

        RuleFor(x => x.UserAgent)
            .MaximumLength(500)
            .WithMessage("User agent cannot exceed 500 characters")
            .When(x => !string.IsNullOrWhiteSpace(x.UserAgent));
    }
}
