using FleetXQ.Application.Features.Authentication.Commands.LoginUser;
using FleetXQ.Application.Tests.Common;
using FleetXQ.Domain.Entities;
using FleetXQ.Domain.Enums;
using FluentAssertions;
using Moq;

namespace FleetXQ.Application.Tests.Features.Authentication.Commands;

public class LoginUserCommandHandlerTests : CommandHandlerTestBase<LoginUserCommandHandler>
{
    private readonly LoginUserCommandHandler _handler;

    public LoginUserCommandHandlerTests()
    {
        _handler = new LoginUserCommandHandler(
            MockUserRepository.Object,
            MockPasswordHashingService.Object,
            MockTokenService.Object,
            MockLogger.Object);
    }

    [Fact]
    public async Task Handle_WithValidCredentials_ShouldReturnSuccessfulLogin()
    {
        // Arrange
        var command = new LoginUserCommand
        {
            UsernameOrEmail = "testuser",
            Password = "password123",
            RememberMe = true,
            ClientIpAddress = "***********",
            UserAgent = "Mozilla/5.0"
        };

        var user = new User("testuser", "<EMAIL>", "Test", "User", UserRole.User);
        var hashedPassword = "hashed_password";
        user.GetType().GetProperty("PasswordHash")?.SetValue(user, hashedPassword);

        MockUserRepository.Setup(x => x.GetByUsernameAsync(command.UsernameOrEmail, It.IsAny<CancellationToken>()))
            .ReturnsAsync(user);

        MockPasswordHashingService.Setup(x => x.VerifyPassword(command.Password, hashedPassword))
            .Returns(true);

        MockTokenService.Setup(x => x.GenerateAccessToken(user))
            .Returns("access_token");

        MockTokenService.Setup(x => x.GenerateRefreshToken())
            .Returns("refresh_token");

        MockTokenService.Setup(x => x.GetAccessTokenExpiry())
            .Returns(TimeSpan.FromHours(1));

        MockUserRepository.Setup(x => x.UpdateAsync(It.IsAny<User>(), It.IsAny<CancellationToken>()))
            .Returns(Task.CompletedTask);

        // Act
        var result = await _handler.Handle(command, CreateCancellationToken());

        // Assert
        result.Should().NotBeNull();
        result.Success.Should().BeTrue();
        result.AccessToken.Should().Be("access_token");
        result.RefreshToken.Should().Be("refresh_token");
        result.TokenExpiry.Should().BeCloseTo(DateTime.UtcNow.AddHours(1), TimeSpan.FromMinutes(1));
        result.User.Should().NotBeNull();
        result.User!.Username.Should().Be("testuser");
        result.User.Email.Should().Be("<EMAIL>");
        result.User.FullName.Should().Be("Test User");
        result.User.Role.Should().Be("User");
        result.IsAccountLocked.Should().BeFalse();
        result.ErrorMessage.Should().BeNull();

        MockUserRepository.Verify(x => x.GetByUsernameAsync(command.UsernameOrEmail, It.IsAny<CancellationToken>()), Times.Once);
        MockPasswordHashingService.Verify(x => x.VerifyPassword(command.Password, hashedPassword), Times.Once);
        MockTokenService.Verify(x => x.GenerateAccessToken(user), Times.Once);
        MockTokenService.Verify(x => x.GenerateRefreshToken(), Times.Once);
        VerifyRepositoryUpdateCalled(MockUserRepository);
        VerifyInformationLogged(Times.AtLeastOnce);
    }

    [Fact]
    public async Task Handle_WithEmailAsUsername_ShouldLoginSuccessfully()
    {
        // Arrange
        var command = new LoginUserCommand
        {
            UsernameOrEmail = "<EMAIL>",
            Password = "password123"
        };

        var user = new User("testuser", "<EMAIL>", "Test", "User", UserRole.User);
        var hashedPassword = "hashed_password";
        user.GetType().GetProperty("PasswordHash")?.SetValue(user, hashedPassword);

        MockUserRepository.Setup(x => x.GetByUsernameAsync(command.UsernameOrEmail, It.IsAny<CancellationToken>()))
            .ReturnsAsync((User?)null);

        MockUserRepository.Setup(x => x.GetByEmailAsync(command.UsernameOrEmail, It.IsAny<CancellationToken>()))
            .ReturnsAsync(user);

        MockPasswordHashingService.Setup(x => x.VerifyPassword(command.Password, hashedPassword))
            .Returns(true);

        MockTokenService.Setup(x => x.GenerateAccessToken(user))
            .Returns("access_token");

        MockTokenService.Setup(x => x.GenerateRefreshToken())
            .Returns("refresh_token");

        MockTokenService.Setup(x => x.GetAccessTokenExpiry())
            .Returns(TimeSpan.FromHours(1));

        MockUserRepository.Setup(x => x.UpdateAsync(It.IsAny<User>(), It.IsAny<CancellationToken>()))
            .Returns(Task.CompletedTask);

        // Act
        var result = await _handler.Handle(command, CreateCancellationToken());

        // Assert
        result.Should().NotBeNull();
        result.Success.Should().BeTrue();
        result.AccessToken.Should().Be("access_token");

        MockUserRepository.Verify(x => x.GetByUsernameAsync(command.UsernameOrEmail, It.IsAny<CancellationToken>()), Times.Once);
        MockUserRepository.Verify(x => x.GetByEmailAsync(command.UsernameOrEmail, It.IsAny<CancellationToken>()), Times.Once);
    }

    [Fact]
    public async Task Handle_WithNonExistentUser_ShouldReturnFailure()
    {
        // Arrange
        var command = new LoginUserCommand
        {
            UsernameOrEmail = "nonexistent",
            Password = "password123"
        };

        MockUserRepository.Setup(x => x.GetByUsernameAsync(command.UsernameOrEmail, It.IsAny<CancellationToken>()))
            .ReturnsAsync((User?)null);

        MockUserRepository.Setup(x => x.GetByEmailAsync(command.UsernameOrEmail, It.IsAny<CancellationToken>()))
            .ReturnsAsync((User?)null);

        // Act
        var result = await _handler.Handle(command, CreateCancellationToken());

        // Assert
        result.Should().NotBeNull();
        result.Success.Should().BeFalse();
        result.ErrorMessage.Should().Contain("Invalid username/email or password");
        result.AccessToken.Should().BeNull();
        result.RefreshToken.Should().BeNull();

        MockPasswordHashingService.Verify(x => x.VerifyPassword(It.IsAny<string>(), It.IsAny<string>()), Times.Never);
        MockTokenService.Verify(x => x.GenerateAccessToken(It.IsAny<User>()), Times.Never);
        VerifyWarningLogged();
    }

    [Fact]
    public async Task Handle_WithInvalidPassword_ShouldReturnFailure()
    {
        // Arrange
        var command = new LoginUserCommand
        {
            UsernameOrEmail = "testuser",
            Password = "wrongpassword"
        };

        var user = new User("testuser", "<EMAIL>", "Test", "User", UserRole.User);
        var hashedPassword = "hashed_password";
        user.GetType().GetProperty("PasswordHash")?.SetValue(user, hashedPassword);

        MockUserRepository.Setup(x => x.GetByUsernameAsync(command.UsernameOrEmail, It.IsAny<CancellationToken>()))
            .ReturnsAsync(user);

        MockPasswordHashingService.Setup(x => x.VerifyPassword(command.Password, hashedPassword))
            .Returns(false);

        MockUserRepository.Setup(x => x.UpdateAsync(It.IsAny<User>(), It.IsAny<CancellationToken>()))
            .Returns(Task.CompletedTask);

        // Act
        var result = await _handler.Handle(command, CreateCancellationToken());

        // Assert
        result.Should().NotBeNull();
        result.Success.Should().BeFalse();
        result.ErrorMessage.Should().Contain("Invalid username/email or password");
        result.RemainingAttempts.Should().BeGreaterOrEqualTo(0);
        result.AccessToken.Should().BeNull();
        result.RefreshToken.Should().BeNull();

        MockTokenService.Verify(x => x.GenerateAccessToken(It.IsAny<User>()), Times.Never);
        VerifyRepositoryUpdateCalled(MockUserRepository); // Should update to record failed login
        VerifyWarningLogged();
    }

    [Fact]
    public async Task Handle_WithLockedAccount_ShouldReturnAccountLocked()
    {
        // Arrange
        var command = new LoginUserCommand
        {
            UsernameOrEmail = "testuser",
            Password = "password123"
        };

        var user = new User("testuser", "<EMAIL>", "Test", "User", UserRole.User);
        var lockoutEnd = DateTime.UtcNow.AddMinutes(30);
        
        // Set user as locked out
        user.GetType().GetProperty("IsLockedOut")?.SetValue(user, true);
        user.GetType().GetProperty("LockoutEnd")?.SetValue(user, lockoutEnd);

        MockUserRepository.Setup(x => x.GetByUsernameAsync(command.UsernameOrEmail, It.IsAny<CancellationToken>()))
            .ReturnsAsync(user);

        // Act
        var result = await _handler.Handle(command, CreateCancellationToken());

        // Assert
        result.Should().NotBeNull();
        result.Success.Should().BeFalse();
        result.IsAccountLocked.Should().BeTrue();
        result.LockoutEnd.Should().Be(lockoutEnd);
        result.ErrorMessage.Should().Contain("locked until");
        result.AccessToken.Should().BeNull();

        MockPasswordHashingService.Verify(x => x.VerifyPassword(It.IsAny<string>(), It.IsAny<string>()), Times.Never);
        MockTokenService.Verify(x => x.GenerateAccessToken(It.IsAny<User>()), Times.Never);
        VerifyWarningLogged();
    }

    [Fact]
    public async Task Handle_WithInactiveAccount_ShouldReturnFailure()
    {
        // Arrange
        var command = new LoginUserCommand
        {
            UsernameOrEmail = "testuser",
            Password = "password123"
        };

        var user = new User("testuser", "<EMAIL>", "Test", "User", UserRole.User);
        user.GetType().GetProperty("IsActive")?.SetValue(user, false);

        MockUserRepository.Setup(x => x.GetByUsernameAsync(command.UsernameOrEmail, It.IsAny<CancellationToken>()))
            .ReturnsAsync(user);

        // Act
        var result = await _handler.Handle(command, CreateCancellationToken());

        // Assert
        result.Should().NotBeNull();
        result.Success.Should().BeFalse();
        result.ErrorMessage.Should().Contain("inactive");
        result.AccessToken.Should().BeNull();

        MockPasswordHashingService.Verify(x => x.VerifyPassword(It.IsAny<string>(), It.IsAny<string>()), Times.Never);
        VerifyWarningLogged();
    }

    [Fact]
    public async Task Handle_WithRepositoryException_ShouldReturnFailure()
    {
        // Arrange
        var command = new LoginUserCommand
        {
            UsernameOrEmail = "testuser",
            Password = "password123"
        };

        MockUserRepository.Setup(x => x.GetByUsernameAsync(command.UsernameOrEmail, It.IsAny<CancellationToken>()))
            .ThrowsAsync(new InvalidOperationException("Database error"));

        // Act
        var result = await _handler.Handle(command, CreateCancellationToken());

        // Assert
        result.Should().NotBeNull();
        result.Success.Should().BeFalse();
        result.ErrorMessage.Should().Contain("error occurred during login");
        result.AccessToken.Should().BeNull();

        VerifyErrorLogged();
    }

    [Theory]
    [InlineData(UserRole.Admin)]
    [InlineData(UserRole.Manager)]
    [InlineData(UserRole.Driver)]
    [InlineData(UserRole.User)]
    public async Task Handle_WithDifferentUserRoles_ShouldLoginSuccessfully(UserRole role)
    {
        // Arrange
        var command = new LoginUserCommand
        {
            UsernameOrEmail = "testuser",
            Password = "password123"
        };

        var user = new User("testuser", "<EMAIL>", "Test", "User", role);
        var hashedPassword = "hashed_password";
        user.GetType().GetProperty("PasswordHash")?.SetValue(user, hashedPassword);

        MockUserRepository.Setup(x => x.GetByUsernameAsync(command.UsernameOrEmail, It.IsAny<CancellationToken>()))
            .ReturnsAsync(user);

        MockPasswordHashingService.Setup(x => x.VerifyPassword(command.Password, hashedPassword))
            .Returns(true);

        MockTokenService.Setup(x => x.GenerateAccessToken(user))
            .Returns("access_token");

        MockTokenService.Setup(x => x.GenerateRefreshToken())
            .Returns("refresh_token");

        MockTokenService.Setup(x => x.GetAccessTokenExpiry())
            .Returns(TimeSpan.FromHours(1));

        MockUserRepository.Setup(x => x.UpdateAsync(It.IsAny<User>(), It.IsAny<CancellationToken>()))
            .Returns(Task.CompletedTask);

        // Act
        var result = await _handler.Handle(command, CreateCancellationToken());

        // Assert
        result.Should().NotBeNull();
        result.Success.Should().BeTrue();
        result.User.Should().NotBeNull();
        result.User!.Role.Should().Be(role.ToString());
    }
}
