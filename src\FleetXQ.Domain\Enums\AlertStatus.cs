namespace FleetXQ.Domain.Enums;

/// <summary>
/// Represents the status of an alert
/// </summary>
public enum AlertStatus
{
    /// <summary>
    /// <PERSON><PERSON> is active and requires attention
    /// </summary>
    Active = 1,

    /// <summary>
    /// <PERSON><PERSON> has been acknowledged by a user
    /// </summary>
    Acknowledged = 2,

    /// <summary>
    /// Alert has been resolved
    /// </summary>
    Resolved = 3
}
