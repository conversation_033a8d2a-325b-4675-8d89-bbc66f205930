using MediatR;
using Microsoft.Extensions.Logging;
using System.Diagnostics;

namespace FleetXQ.Application.Common.Behaviors;

/// <summary>
/// Pipeline behavior for performance monitoring and alerting
/// </summary>
/// <typeparam name="TRequest">The request type</typeparam>
/// <typeparam name="TResponse">The response type</typeparam>
public sealed class PerformanceBehavior<TRequest, TResponse> : IPipelineBehavior<TRequest, TResponse>
    where TRequest : notnull
{
    private readonly ILogger<PerformanceBehavior<TRequest, TResponse>> _logger;
    private readonly long _slowRequestThresholdMs;

    /// <summary>
    /// Initializes a new instance of the <see cref="PerformanceBehavior{TRequest, TResponse}"/> class
    /// </summary>
    /// <param name="logger">The logger</param>
    /// <param name="slowRequestThresholdMs">The threshold in milliseconds for slow requests (default: 500ms)</param>
    public PerformanceBehavior(ILogger<PerformanceBehavior<TRequest, TResponse>> logger, long slowRequestThresholdMs = 500)
    {
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        _slowRequestThresholdMs = slowRequestThresholdMs;
    }

    /// <summary>
    /// Handles the request performance monitoring
    /// </summary>
    /// <param name="request">The request</param>
    /// <param name="next">The next handler in the pipeline</param>
    /// <param name="cancellationToken">The cancellation token</param>
    /// <returns>The response</returns>
    public async Task<TResponse> Handle(TRequest request, RequestHandlerDelegate<TResponse> next, CancellationToken cancellationToken)
    {
        var requestName = typeof(TRequest).Name;
        var stopwatch = Stopwatch.StartNew();
        var memoryBefore = GC.GetTotalMemory(false);

        try
        {
            var response = await next();
            
            stopwatch.Stop();
            var memoryAfter = GC.GetTotalMemory(false);
            var memoryUsed = memoryAfter - memoryBefore;

            LogPerformanceMetrics(requestName, stopwatch.ElapsedMilliseconds, memoryUsed, true);

            return response;
        }
        catch (Exception ex)
        {
            stopwatch.Stop();
            var memoryAfter = GC.GetTotalMemory(false);
            var memoryUsed = memoryAfter - memoryBefore;

            LogPerformanceMetrics(requestName, stopwatch.ElapsedMilliseconds, memoryUsed, false);

            // Re-throw the exception
            throw;
        }
    }

    /// <summary>
    /// Logs performance metrics for the request
    /// </summary>
    /// <param name="requestName">The request name</param>
    /// <param name="elapsedMs">The elapsed time in milliseconds</param>
    /// <param name="memoryUsed">The memory used in bytes</param>
    /// <param name="success">Whether the request was successful</param>
    private void LogPerformanceMetrics(string requestName, long elapsedMs, long memoryUsed, bool success)
    {
        var logLevel = elapsedMs > _slowRequestThresholdMs ? LogLevel.Warning : LogLevel.Information;
        var status = success ? "completed" : "failed";
        
        _logger.Log(logLevel, 
            "Performance: {RequestName} {Status} in {ElapsedMs}ms, Memory: {MemoryUsedKB}KB", 
            requestName, status, elapsedMs, memoryUsed / 1024);

        // Log additional details for slow requests
        if (elapsedMs > _slowRequestThresholdMs)
        {
            _logger.LogWarning(
                "Slow request detected: {RequestName} took {ElapsedMs}ms (threshold: {ThresholdMs}ms)", 
                requestName, elapsedMs, _slowRequestThresholdMs);

            // In a production environment, you might want to:
            // 1. Send alerts to monitoring systems
            // 2. Collect additional diagnostic information
            // 3. Trigger performance analysis
            LogSlowRequestDetails(requestName, elapsedMs, memoryUsed);
        }

        // Log memory usage warnings
        if (memoryUsed > 10 * 1024 * 1024) // 10MB threshold
        {
            _logger.LogWarning(
                "High memory usage detected: {RequestName} used {MemoryUsedMB}MB", 
                requestName, memoryUsed / (1024 * 1024));
        }
    }

    /// <summary>
    /// Logs additional details for slow requests
    /// </summary>
    /// <param name="requestName">The request name</param>
    /// <param name="elapsedMs">The elapsed time in milliseconds</param>
    /// <param name="memoryUsed">The memory used in bytes</param>
    private void LogSlowRequestDetails(string requestName, long elapsedMs, long memoryUsed)
    {
        // Get current system information
        var process = Process.GetCurrentProcess();
        var workingSet = process.WorkingSet64;
        var threadCount = process.Threads.Count;
        var gcGen0 = GC.CollectionCount(0);
        var gcGen1 = GC.CollectionCount(1);
        var gcGen2 = GC.CollectionCount(2);

        _logger.LogWarning(
            "Slow request system state - Request: {RequestName}, " +
            "WorkingSet: {WorkingSetMB}MB, Threads: {ThreadCount}, " +
            "GC Gen0: {GcGen0}, GC Gen1: {GcGen1}, GC Gen2: {GcGen2}",
            requestName, workingSet / (1024 * 1024), threadCount, gcGen0, gcGen1, gcGen2);

        // In a real application, you might also want to:
        // 1. Capture stack traces
        // 2. Log database connection pool status
        // 3. Check external service response times
        // 4. Monitor CPU usage
        // 5. Send metrics to APM tools (Application Performance Monitoring)
    }
}

/// <summary>
/// Performance metrics for a request
/// </summary>
public sealed class PerformanceMetrics
{
    /// <summary>
    /// Gets or sets the request name
    /// </summary>
    public string RequestName { get; set; } = string.Empty;

    /// <summary>
    /// Gets or sets the elapsed time in milliseconds
    /// </summary>
    public long ElapsedMilliseconds { get; set; }

    /// <summary>
    /// Gets or sets the memory used in bytes
    /// </summary>
    public long MemoryUsed { get; set; }

    /// <summary>
    /// Gets or sets whether the request was successful
    /// </summary>
    public bool Success { get; set; }

    /// <summary>
    /// Gets or sets the timestamp when the request was processed
    /// </summary>
    public DateTime Timestamp { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// Gets or sets additional performance data
    /// </summary>
    public Dictionary<string, object> AdditionalData { get; set; } = new();
}
