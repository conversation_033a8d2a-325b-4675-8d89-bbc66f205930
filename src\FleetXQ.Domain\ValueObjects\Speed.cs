using FleetXQ.Domain.Common;

namespace FleetXQ.Domain.ValueObjects;

/// <summary>
/// Represents a speed value with unit conversion capabilities
/// </summary>
public sealed class Speed : ValueObject
{
    /// <summary>
    /// Gets the speed value in kilometers per hour
    /// </summary>
    public decimal KilometersPerHour { get; }

    /// <summary>
    /// Gets the speed value in miles per hour
    /// </summary>
    public decimal MilesPerHour => KilometersPerHour * 0.621371m;

    /// <summary>
    /// Gets the speed value in meters per second
    /// </summary>
    public decimal MetersPerSecond => KilometersPerHour / 3.6m;

    /// <summary>
    /// Gets the speed value in knots
    /// </summary>
    public decimal Knots => KilometersPerHour * 0.539957m;

    /// <summary>
    /// Gets a value indicating whether the vehicle is stationary (speed is 0)
    /// </summary>
    public bool IsStationary => KilometersPerHour == 0;

    /// <summary>
    /// Gets a value indicating whether the speed is considered high (above 80 km/h)
    /// </summary>
    public bool IsHigh => KilometersPerHour > 80;

    /// <summary>
    /// Initializes a new instance of the <see cref="Speed"/> class
    /// </summary>
    /// <param name="kilometersPerHour">The speed in kilometers per hour</param>
    /// <exception cref="ArgumentOutOfRangeException">Thrown when speed is negative</exception>
    public Speed(decimal kilometersPerHour)
    {
        if (kilometersPerHour < 0)
        {
            throw new ArgumentOutOfRangeException(nameof(kilometersPerHour), kilometersPerHour, "Speed cannot be negative");
        }

        KilometersPerHour = kilometersPerHour;
    }

    /// <summary>
    /// Creates a Speed from kilometers per hour
    /// </summary>
    /// <param name="kmh">The speed in kilometers per hour</param>
    /// <returns>A new Speed instance</returns>
    public static Speed FromKilometersPerHour(decimal kmh)
    {
        return new Speed(kmh);
    }

    /// <summary>
    /// Creates a Speed from miles per hour
    /// </summary>
    /// <param name="mph">The speed in miles per hour</param>
    /// <returns>A new Speed instance</returns>
    public static Speed FromMilesPerHour(decimal mph)
    {
        if (mph < 0)
        {
            throw new ArgumentOutOfRangeException(nameof(mph), mph, "Speed cannot be negative");
        }

        var kmh = mph / 0.621371m;
        return new Speed(kmh);
    }

    /// <summary>
    /// Creates a Speed from meters per second
    /// </summary>
    /// <param name="mps">The speed in meters per second</param>
    /// <returns>A new Speed instance</returns>
    public static Speed FromMetersPerSecond(decimal mps)
    {
        if (mps < 0)
        {
            throw new ArgumentOutOfRangeException(nameof(mps), mps, "Speed cannot be negative");
        }

        var kmh = mps * 3.6m;
        return new Speed(kmh);
    }

    /// <summary>
    /// Creates a Speed from knots
    /// </summary>
    /// <param name="knots">The speed in knots</param>
    /// <returns>A new Speed instance</returns>
    public static Speed FromKnots(decimal knots)
    {
        if (knots < 0)
        {
            throw new ArgumentOutOfRangeException(nameof(knots), knots, "Speed cannot be negative");
        }

        var kmh = knots / 0.539957m;
        return new Speed(kmh);
    }

    /// <summary>
    /// Creates a Speed from a double value in kilometers per hour
    /// </summary>
    /// <param name="kmh">The speed in kilometers per hour</param>
    /// <returns>A new Speed instance</returns>
    public static Speed FromDouble(double kmh)
    {
        return new Speed((decimal)kmh);
    }

    /// <summary>
    /// Gets a Speed representing zero speed
    /// </summary>
    public static Speed Zero => new(0);

    /// <summary>
    /// Gets the speed category based on the value
    /// </summary>
    /// <returns>The speed category</returns>
    public SpeedCategory GetCategory()
    {
        return KilometersPerHour switch
        {
            0 => SpeedCategory.Stationary,
            <= 30 => SpeedCategory.Low,
            <= 60 => SpeedCategory.Medium,
            <= 100 => SpeedCategory.High,
            _ => SpeedCategory.VeryHigh
        };
    }

    /// <summary>
    /// Determines if the speed exceeds a given limit
    /// </summary>
    /// <param name="speedLimit">The speed limit to check against</param>
    /// <returns>True if speed exceeds the limit, false otherwise</returns>
    public bool ExceedsLimit(Speed speedLimit)
    {
        if (speedLimit == null)
        {
            throw new ArgumentNullException(nameof(speedLimit));
        }

        return KilometersPerHour > speedLimit.KilometersPerHour;
    }

    /// <summary>
    /// Calculates the percentage over a speed limit
    /// </summary>
    /// <param name="speedLimit">The speed limit</param>
    /// <returns>The percentage over the limit (0 if not exceeding)</returns>
    public decimal PercentageOverLimit(Speed speedLimit)
    {
        if (speedLimit == null)
        {
            throw new ArgumentNullException(nameof(speedLimit));
        }

        if (speedLimit.KilometersPerHour == 0)
        {
            throw new ArgumentException("Speed limit cannot be zero", nameof(speedLimit));
        }

        if (KilometersPerHour <= speedLimit.KilometersPerHour)
        {
            return 0;
        }

        return ((KilometersPerHour - speedLimit.KilometersPerHour) / speedLimit.KilometersPerHour) * 100;
    }

    /// <summary>
    /// Gets the atomic values that define the value object's equality
    /// </summary>
    /// <returns>An enumerable of atomic values</returns>
    protected override IEnumerable<object> GetEqualityComponents()
    {
        yield return KilometersPerHour;
    }

    /// <summary>
    /// Returns a string representation of the speed
    /// </summary>
    /// <returns>A string representation of the speed in km/h</returns>
    public override string ToString()
    {
        return $"{KilometersPerHour:F1} km/h";
    }

    /// <summary>
    /// Implicit conversion from decimal to Speed (assumes km/h)
    /// </summary>
    /// <param name="kmh">The speed in kilometers per hour</param>
    /// <returns>A new Speed instance</returns>
    public static implicit operator Speed(decimal kmh)
    {
        return new Speed(kmh);
    }

    /// <summary>
    /// Implicit conversion from Speed to decimal (returns km/h)
    /// </summary>
    /// <param name="speed">The Speed instance</param>
    /// <returns>The speed value in kilometers per hour</returns>
    public static implicit operator decimal(Speed speed)
    {
        return speed.KilometersPerHour;
    }

    /// <summary>
    /// Addition operator for Speed values
    /// </summary>
    /// <param name="left">The first speed</param>
    /// <param name="right">The second speed</param>
    /// <returns>A new Speed representing the sum</returns>
    public static Speed operator +(Speed left, Speed right)
    {
        if (left == null) throw new ArgumentNullException(nameof(left));
        if (right == null) throw new ArgumentNullException(nameof(right));

        return new Speed(left.KilometersPerHour + right.KilometersPerHour);
    }

    /// <summary>
    /// Subtraction operator for Speed values
    /// </summary>
    /// <param name="left">The first speed</param>
    /// <param name="right">The second speed</param>
    /// <returns>A new Speed representing the difference</returns>
    public static Speed operator -(Speed left, Speed right)
    {
        if (left == null) throw new ArgumentNullException(nameof(left));
        if (right == null) throw new ArgumentNullException(nameof(right));

        var result = left.KilometersPerHour - right.KilometersPerHour;
        return new Speed(Math.Max(0, result)); // Speed cannot be negative
    }

    /// <summary>
    /// Greater than operator for Speed values
    /// </summary>
    /// <param name="left">The first speed</param>
    /// <param name="right">The second speed</param>
    /// <returns>True if left is greater than right</returns>
    public static bool operator >(Speed left, Speed right)
    {
        if (left == null) throw new ArgumentNullException(nameof(left));
        if (right == null) throw new ArgumentNullException(nameof(right));

        return left.KilometersPerHour > right.KilometersPerHour;
    }

    /// <summary>
    /// Less than operator for Speed values
    /// </summary>
    /// <param name="left">The first speed</param>
    /// <param name="right">The second speed</param>
    /// <returns>True if left is less than right</returns>
    public static bool operator <(Speed left, Speed right)
    {
        if (left == null) throw new ArgumentNullException(nameof(left));
        if (right == null) throw new ArgumentNullException(nameof(right));

        return left.KilometersPerHour < right.KilometersPerHour;
    }

    /// <summary>
    /// Greater than or equal operator for Speed values
    /// </summary>
    /// <param name="left">The first speed</param>
    /// <param name="right">The second speed</param>
    /// <returns>True if left is greater than or equal to right</returns>
    public static bool operator >=(Speed left, Speed right)
    {
        return !(left < right);
    }

    /// <summary>
    /// Less than or equal operator for Speed values
    /// </summary>
    /// <param name="left">The first speed</param>
    /// <param name="right">The second speed</param>
    /// <returns>True if left is less than or equal to right</returns>
    public static bool operator <=(Speed left, Speed right)
    {
        return !(left > right);
    }
}

/// <summary>
/// Represents the category of speed
/// </summary>
public enum SpeedCategory
{
    /// <summary>
    /// Vehicle is not moving (0 km/h)
    /// </summary>
    Stationary,

    /// <summary>
    /// Low speed (1-30 km/h)
    /// </summary>
    Low,

    /// <summary>
    /// Medium speed (31-60 km/h)
    /// </summary>
    Medium,

    /// <summary>
    /// High speed (61-100 km/h)
    /// </summary>
    High,

    /// <summary>
    /// Very high speed (above 100 km/h)
    /// </summary>
    VeryHigh
}
