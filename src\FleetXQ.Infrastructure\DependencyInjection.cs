using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Configuration;

namespace FleetXQ.Infrastructure;

/// <summary>
/// Extension methods for configuring infrastructure services
/// </summary>
public static class DependencyInjection
{
    /// <summary>
    /// Adds infrastructure services to the dependency injection container
    /// </summary>
    /// <param name="services">The service collection</param>
    /// <param name="configuration">The configuration</param>
    /// <returns>The service collection</returns>
    public static IServiceCollection AddInfrastructure(this IServiceCollection services, IConfiguration configuration)
    {
        // Database configuration will be added here
        // Authentication configuration will be added here
        // Repository registrations will be added here
        // External service integrations will be added here

        return services;
    }
}
