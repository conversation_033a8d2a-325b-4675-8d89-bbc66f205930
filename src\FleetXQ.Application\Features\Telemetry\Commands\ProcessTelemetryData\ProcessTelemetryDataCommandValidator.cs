using FluentValidation;

namespace FleetXQ.Application.Features.Telemetry.Commands.ProcessTelemetryData;

/// <summary>
/// Validator for ProcessTelemetryDataCommand
/// </summary>
public sealed class ProcessTelemetryDataCommandValidator : AbstractValidator<ProcessTelemetryDataCommand>
{
    /// <summary>
    /// Initializes a new instance of the <see cref="ProcessTelemetryDataCommandValidator"/> class
    /// </summary>
    public ProcessTelemetryDataCommandValidator()
    {
        RuleFor(x => x.VehicleId)
            .NotEmpty()
            .WithMessage("Vehicle ID is required");

        RuleFor(x => x.Latitude)
            .InclusiveBetween(-90, 90)
            .WithMessage("Latitude must be between -90 and 90 degrees");

        RuleFor(x => x.Longitude)
            .InclusiveBetween(-180, 180)
            .WithMessage("Longitude must be between -180 and 180 degrees");

        RuleFor(x => x.SpeedKmh)
            .GreaterThanOrEqualTo(0)
            .WithMessage("Speed cannot be negative")
            .LessThanOrEqualTo(500)
            .WithMessage("Speed cannot exceed 500 km/h");

        RuleFor(x => x.FuelLevelPercentage)
            .InclusiveBetween(0, 100)
            .WithMessage("Fuel level percentage must be between 0 and 100")
            .When(x => x.FuelLevelPercentage.HasValue);

        RuleFor(x => x.Mileage)
            .GreaterThanOrEqualTo(0)
            .WithMessage("Mileage cannot be negative")
            .LessThanOrEqualTo(10000000)
            .WithMessage("Mileage cannot exceed 10,000,000")
            .When(x => x.Mileage.HasValue);

        RuleFor(x => x.Timestamp)
            .NotEmpty()
            .WithMessage("Timestamp is required")
            .LessThanOrEqualTo(DateTime.UtcNow.AddMinutes(5))
            .WithMessage("Timestamp cannot be more than 5 minutes in the future")
            .GreaterThan(DateTime.UtcNow.AddDays(-30))
            .WithMessage("Timestamp cannot be more than 30 days in the past");

        RuleFor(x => x.EngineTemperature)
            .InclusiveBetween(-50, 200)
            .WithMessage("Engine temperature must be between -50°C and 200°C")
            .When(x => x.EngineTemperature.HasValue);

        RuleFor(x => x.RPM)
            .InclusiveBetween(0, 10000)
            .WithMessage("RPM must be between 0 and 10,000")
            .When(x => x.RPM.HasValue);

        RuleFor(x => x.AdditionalData)
            .MaximumLength(5000)
            .WithMessage("Additional data cannot exceed 5000 characters")
            .Must(BeValidJson)
            .WithMessage("Additional data must be valid JSON")
            .When(x => !string.IsNullOrWhiteSpace(x.AdditionalData));
    }

    /// <summary>
    /// Validates if the string is valid JSON
    /// </summary>
    /// <param name="json">The JSON string to validate</param>
    /// <returns>True if valid JSON, false otherwise</returns>
    private static bool BeValidJson(string? json)
    {
        if (string.IsNullOrWhiteSpace(json))
            return true;

        try
        {
            System.Text.Json.JsonDocument.Parse(json);
            return true;
        }
        catch
        {
            return false;
        }
    }
}
