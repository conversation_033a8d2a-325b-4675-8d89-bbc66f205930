using FluentValidation;

namespace FleetXQ.Application.Features.Vehicles.Queries.GetVehicleById;

/// <summary>
/// Validator for GetVehicleByIdQuery
/// </summary>
public sealed class GetVehicleByIdQueryValidator : AbstractValidator<GetVehicleByIdQuery>
{
    /// <summary>
    /// Initializes a new instance of the <see cref="GetVehicleByIdQueryValidator"/> class
    /// </summary>
    public GetVehicleByIdQueryValidator()
    {
        RuleFor(x => x.VehicleId)
            .NotEmpty()
            .WithMessage("Vehicle ID is required");
    }
}
