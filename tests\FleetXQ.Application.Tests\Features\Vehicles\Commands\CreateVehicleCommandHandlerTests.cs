using FleetXQ.Application.Features.Vehicles.Commands.CreateVehicle;
using FleetXQ.Application.Tests.Common;
using FleetXQ.Domain.Entities;
using FluentAssertions;
using Moq;

namespace FleetXQ.Application.Tests.Features.Vehicles.Commands;

public class CreateVehicleCommandHandlerTests : CommandHandlerTestBase<CreateVehicleCommandHandler>
{
    private readonly CreateVehicleCommandHandler _handler;

    public CreateVehicleCommandHandlerTests()
    {
        _handler = new CreateVehicleCommandHandler(MockVehicleRepository.Object, MockLogger.Object);
    }

    [Fact]
    public async Task Handle_WithValidCommand_ShouldCreateVehicleSuccessfully()
    {
        // Arrange
        var command = new CreateVehicleCommand
        {
            VehicleName = "Test Vehicle",
            LicensePlate = "ABC-123",
            VehicleType = "Car",
            FuelType = "Gasoline",
            Brand = "Toyota",
            Model = "Camry",
            Year = 2023,
            Color = "Blue",
            FuelTankCapacity = 60m
        };

        MockVehicleRepository.Setup(x => x.GetByLicensePlateAsync(command.LicensePlate, It.IsAny<CancellationToken>()))
            .ReturnsAsync((Vehicle?)null);

        MockVehicleRepository.Setup(x => x.AddAsync(It.IsAny<Vehicle>(), It.IsAny<CancellationToken>()))
            .Returns(Task.CompletedTask);

        // Act
        var result = await _handler.Handle(command, CreateCancellationToken());

        // Assert
        result.Should().NotBeNull();
        result.Success.Should().BeTrue();
        result.VehicleId.Should().NotBeEmpty();
        result.ErrorMessage.Should().BeNull();

        MockVehicleRepository.Verify(x => x.GetByLicensePlateAsync(command.LicensePlate, It.IsAny<CancellationToken>()), Times.Once);
        VerifyRepositoryAddCalled(MockVehicleRepository);
        VerifyInformationLogged(Times.AtLeastOnce);
    }

    [Fact]
    public async Task Handle_WithDuplicateLicensePlate_ShouldReturnFailure()
    {
        // Arrange
        var command = new CreateVehicleCommand
        {
            VehicleName = "Test Vehicle",
            LicensePlate = "ABC-123",
            VehicleType = "Car"
        };

        var existingVehicle = new Vehicle("Existing Vehicle", "ABC-123", "Car");
        MockVehicleRepository.Setup(x => x.GetByLicensePlateAsync(command.LicensePlate, It.IsAny<CancellationToken>()))
            .ReturnsAsync(existingVehicle);

        // Act
        var result = await _handler.Handle(command, CreateCancellationToken());

        // Assert
        result.Should().NotBeNull();
        result.Success.Should().BeFalse();
        result.ErrorMessage.Should().Contain("already exists");
        result.VehicleId.Should().BeEmpty();

        MockVehicleRepository.Verify(x => x.AddAsync(It.IsAny<Vehicle>(), It.IsAny<CancellationToken>()), Times.Never);
        VerifyWarningLogged();
    }

    [Fact]
    public async Task Handle_WithDuplicateVIN_ShouldReturnFailure()
    {
        // Arrange
        var command = new CreateVehicleCommand
        {
            VehicleName = "Test Vehicle",
            LicensePlate = "ABC-123",
            VehicleType = "Car",
            VIN = "1HGBH41JXMN109186"
        };

        MockVehicleRepository.Setup(x => x.GetByLicensePlateAsync(command.LicensePlate, It.IsAny<CancellationToken>()))
            .ReturnsAsync((Vehicle?)null);

        var existingVehicle = new Vehicle("Existing Vehicle", "XYZ-789", "Car");
        MockVehicleRepository.Setup(x => x.GetByVinAsync(command.VIN, It.IsAny<CancellationToken>()))
            .ReturnsAsync(existingVehicle);

        // Act
        var result = await _handler.Handle(command, CreateCancellationToken());

        // Assert
        result.Should().NotBeNull();
        result.Success.Should().BeFalse();
        result.ErrorMessage.Should().Contain("VIN").And.Contain("already exists");
        result.VehicleId.Should().BeEmpty();

        MockVehicleRepository.Verify(x => x.AddAsync(It.IsAny<Vehicle>(), It.IsAny<CancellationToken>()), Times.Never);
        VerifyWarningLogged();
    }

    [Fact]
    public async Task Handle_WithRepositoryException_ShouldReturnFailure()
    {
        // Arrange
        var command = new CreateVehicleCommand
        {
            VehicleName = "Test Vehicle",
            LicensePlate = "ABC-123",
            VehicleType = "Car"
        };

        MockVehicleRepository.Setup(x => x.GetByLicensePlateAsync(command.LicensePlate, It.IsAny<CancellationToken>()))
            .ThrowsAsync(new InvalidOperationException("Database error"));

        // Act
        var result = await _handler.Handle(command, CreateCancellationToken());

        // Assert
        result.Should().NotBeNull();
        result.Success.Should().BeFalse();
        result.ErrorMessage.Should().Contain("error occurred");
        result.VehicleId.Should().BeEmpty();

        VerifyErrorLogged();
    }

    [Fact]
    public async Task Handle_WithMinimalValidData_ShouldCreateVehicleSuccessfully()
    {
        // Arrange
        var command = new CreateVehicleCommand
        {
            VehicleName = "Minimal Vehicle",
            LicensePlate = "MIN-001",
            VehicleType = "Truck"
        };

        MockVehicleRepository.Setup(x => x.GetByLicensePlateAsync(command.LicensePlate, It.IsAny<CancellationToken>()))
            .ReturnsAsync((Vehicle?)null);

        MockVehicleRepository.Setup(x => x.AddAsync(It.IsAny<Vehicle>(), It.IsAny<CancellationToken>()))
            .Returns(Task.CompletedTask);

        // Act
        var result = await _handler.Handle(command, CreateCancellationToken());

        // Assert
        result.Should().NotBeNull();
        result.Success.Should().BeTrue();
        result.VehicleId.Should().NotBeEmpty();

        VerifyRepositoryAddCalled(MockVehicleRepository);
        VerifyInformationLogged(Times.AtLeastOnce);
    }
}
