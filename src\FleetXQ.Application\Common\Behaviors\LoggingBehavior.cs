using MediatR;
using Microsoft.Extensions.Logging;
using System.Diagnostics;
using System.Text.Json;

namespace FleetXQ.Application.Common.Behaviors;

/// <summary>
/// Pipeline behavior for request/response logging
/// </summary>
/// <typeparam name="TRequest">The request type</typeparam>
/// <typeparam name="TResponse">The response type</typeparam>
public sealed class LoggingBehavior<TRequest, TResponse> : IPipelineBehavior<TRequest, TResponse>
    where TRequest : notnull
{
    private readonly ILogger<LoggingBehavior<TRequest, TResponse>> _logger;

    /// <summary>
    /// Initializes a new instance of the <see cref="LoggingBehavior{TRequest, TResponse}"/> class
    /// </summary>
    /// <param name="logger">The logger</param>
    public LoggingBehavior(ILogger<LoggingBehavior<TRequest, TResponse>> logger)
    {
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
    }

    /// <summary>
    /// Handles the request logging
    /// </summary>
    /// <param name="request">The request</param>
    /// <param name="next">The next handler in the pipeline</param>
    /// <param name="cancellationToken">The cancellation token</param>
    /// <returns>The response</returns>
    public async Task<TResponse> Handle(TRequest request, RequestHandlerDelegate<TResponse> next, CancellationToken cancellationToken)
    {
        var requestName = typeof(TRequest).Name;
        var requestId = Guid.NewGuid();
        var stopwatch = Stopwatch.StartNew();

        _logger.LogInformation("Starting request {RequestName} with ID {RequestId}", requestName, requestId);

        // Log request details (be careful with sensitive data)
        if (_logger.IsEnabled(LogLevel.Debug))
        {
            var requestJson = SerializeRequest(request);
            _logger.LogDebug("Request {RequestName} ({RequestId}) details: {RequestData}", 
                requestName, requestId, requestJson);
        }

        try
        {
            var response = await next();
            
            stopwatch.Stop();
            
            _logger.LogInformation("Completed request {RequestName} with ID {RequestId} in {ElapsedMilliseconds}ms", 
                requestName, requestId, stopwatch.ElapsedMilliseconds);

            // Log response details for debug (be careful with sensitive data)
            if (_logger.IsEnabled(LogLevel.Debug))
            {
                var responseJson = SerializeResponse(response);
                _logger.LogDebug("Response for {RequestName} ({RequestId}): {ResponseData}", 
                    requestName, requestId, responseJson);
            }

            return response;
        }
        catch (Exception ex)
        {
            stopwatch.Stop();
            
            _logger.LogError(ex, "Request {RequestName} with ID {RequestId} failed after {ElapsedMilliseconds}ms", 
                requestName, requestId, stopwatch.ElapsedMilliseconds);
            
            throw;
        }
    }

    /// <summary>
    /// Serializes the request for logging (with sensitive data filtering)
    /// </summary>
    /// <param name="request">The request to serialize</param>
    /// <returns>The serialized request</returns>
    private static string SerializeRequest(TRequest request)
    {
        try
        {
            // Create a copy of the request with sensitive data masked
            var requestCopy = MaskSensitiveData(request);
            return JsonSerializer.Serialize(requestCopy, new JsonSerializerOptions 
            { 
                WriteIndented = false,
                PropertyNamingPolicy = JsonNamingPolicy.CamelCase
            });
        }
        catch (Exception)
        {
            return $"[Unable to serialize {typeof(TRequest).Name}]";
        }
    }

    /// <summary>
    /// Serializes the response for logging (with sensitive data filtering)
    /// </summary>
    /// <param name="response">The response to serialize</param>
    /// <returns>The serialized response</returns>
    private static string SerializeResponse(TResponse response)
    {
        try
        {
            // Create a copy of the response with sensitive data masked
            var responseCopy = MaskSensitiveData(response);
            return JsonSerializer.Serialize(responseCopy, new JsonSerializerOptions 
            { 
                WriteIndented = false,
                PropertyNamingPolicy = JsonNamingPolicy.CamelCase
            });
        }
        catch (Exception)
        {
            return $"[Unable to serialize {typeof(TResponse).Name}]";
        }
    }

    /// <summary>
    /// Masks sensitive data in objects for logging
    /// </summary>
    /// <param name="obj">The object to mask</param>
    /// <returns>The object with sensitive data masked</returns>
    private static object MaskSensitiveData(object obj)
    {
        if (obj == null) return obj;

        var type = obj.GetType();
        var properties = type.GetProperties();
        var maskedObj = Activator.CreateInstance(type);

        if (maskedObj == null) return obj;

        foreach (var property in properties)
        {
            if (!property.CanRead || !property.CanWrite) continue;

            var value = property.GetValue(obj);
            
            // Mask sensitive properties
            if (IsSensitiveProperty(property.Name))
            {
                property.SetValue(maskedObj, "[MASKED]");
            }
            else
            {
                property.SetValue(maskedObj, value);
            }
        }

        return maskedObj;
    }

    /// <summary>
    /// Determines if a property contains sensitive data
    /// </summary>
    /// <param name="propertyName">The property name</param>
    /// <returns>True if the property is sensitive</returns>
    private static bool IsSensitiveProperty(string propertyName)
    {
        var sensitiveProperties = new[]
        {
            "password", "token", "secret", "key", "hash", "pin", "ssn", "creditcard"
        };

        return sensitiveProperties.Any(sensitive => 
            propertyName.Contains(sensitive, StringComparison.OrdinalIgnoreCase));
    }
}
