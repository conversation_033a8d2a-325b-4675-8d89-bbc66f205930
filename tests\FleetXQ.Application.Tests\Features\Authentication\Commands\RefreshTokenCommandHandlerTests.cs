using FleetXQ.Application.Features.Authentication.Commands.RefreshToken;
using FleetXQ.Application.Tests.Common;
using FleetXQ.Domain.Entities;
using FleetXQ.Domain.Enums;
using FluentAssertions;
using Moq;

namespace FleetXQ.Application.Tests.Features.Authentication.Commands;

public class RefreshTokenCommandHandlerTests : CommandHandlerTestBase<RefreshTokenCommandHandler>
{
    private readonly RefreshTokenCommandHandler _handler;

    public RefreshTokenCommandHandlerTests()
    {
        _handler = new RefreshTokenCommandHandler(
            MockUserRepository.Object,
            MockTokenService.Object,
            MockLogger.Object);
    }

    [Fact]
    public async Task Handle_WithValidTokens_ShouldReturnNewTokens()
    {
        // Arrange
        var userId = Guid.NewGuid();
        var command = new RefreshTokenCommand
        {
            AccessToken = "expired_access_token",
            RefreshToken = "valid_refresh_token",
            ClientIpAddress = "***********",
            UserAgent = "Mozilla/5.0"
        };

        var user = new User("testuser", "<EMAIL>", "Test", "User", UserRole.User);

        MockTokenService.Setup(x => x.GetUserIdFromToken(command.AccessToken))
            .Returns(userId);

        MockUserRepository.Setup(x => x.GetByIdAsync(userId, It.IsAny<CancellationToken>()))
            .ReturnsAsync(user);

        MockTokenService.Setup(x => x.GenerateAccessToken(user))
            .Returns("new_access_token");

        MockTokenService.Setup(x => x.GenerateRefreshToken())
            .Returns("new_refresh_token");

        MockTokenService.Setup(x => x.GetAccessTokenExpiry())
            .Returns(TimeSpan.FromHours(1));

        // Act
        var result = await _handler.Handle(command, CreateCancellationToken());

        // Assert
        result.Should().NotBeNull();
        result.Success.Should().BeTrue();
        result.AccessToken.Should().Be("new_access_token");
        result.RefreshToken.Should().Be("new_refresh_token");
        result.TokenExpiry.Should().BeCloseTo(DateTime.UtcNow.AddHours(1), TimeSpan.FromMinutes(1));
        result.User.Should().NotBeNull();
        result.User!.Id.Should().Be(user.Id);
        result.User.Username.Should().Be("testuser");
        result.User.Role.Should().Be("User");
        result.ErrorMessage.Should().BeNull();

        MockTokenService.Verify(x => x.GetUserIdFromToken(command.AccessToken), Times.Once);
        MockUserRepository.Verify(x => x.GetByIdAsync(userId, It.IsAny<CancellationToken>()), Times.Once);
        MockTokenService.Verify(x => x.GenerateAccessToken(user), Times.Once);
        MockTokenService.Verify(x => x.GenerateRefreshToken(), Times.Once);
        VerifyInformationLogged(Times.AtLeastOnce);
    }

    [Fact]
    public async Task Handle_WithInvalidAccessToken_ShouldReturnFailure()
    {
        // Arrange
        var command = new RefreshTokenCommand
        {
            AccessToken = "invalid_access_token",
            RefreshToken = "valid_refresh_token"
        };

        MockTokenService.Setup(x => x.GetUserIdFromToken(command.AccessToken))
            .Returns((Guid?)null);

        // Act
        var result = await _handler.Handle(command, CreateCancellationToken());

        // Assert
        result.Should().NotBeNull();
        result.Success.Should().BeFalse();
        result.ErrorMessage.Should().Contain("Invalid access token");
        result.AccessToken.Should().BeNull();
        result.RefreshToken.Should().BeNull();
        result.User.Should().BeNull();

        MockUserRepository.Verify(x => x.GetByIdAsync(It.IsAny<Guid>(), It.IsAny<CancellationToken>()), Times.Never);
        MockTokenService.Verify(x => x.GenerateAccessToken(It.IsAny<User>()), Times.Never);
        VerifyWarningLogged();
    }

    [Fact]
    public async Task Handle_WithNonExistentUser_ShouldReturnFailure()
    {
        // Arrange
        var userId = Guid.NewGuid();
        var command = new RefreshTokenCommand
        {
            AccessToken = "valid_access_token",
            RefreshToken = "valid_refresh_token"
        };

        MockTokenService.Setup(x => x.GetUserIdFromToken(command.AccessToken))
            .Returns(userId);

        MockUserRepository.Setup(x => x.GetByIdAsync(userId, It.IsAny<CancellationToken>()))
            .ReturnsAsync((User?)null);

        // Act
        var result = await _handler.Handle(command, CreateCancellationToken());

        // Assert
        result.Should().NotBeNull();
        result.Success.Should().BeFalse();
        result.ErrorMessage.Should().Contain("User not found");
        result.AccessToken.Should().BeNull();
        result.RefreshToken.Should().BeNull();

        MockTokenService.Verify(x => x.GenerateAccessToken(It.IsAny<User>()), Times.Never);
        VerifyWarningLogged();
    }

    [Fact]
    public async Task Handle_WithInactiveUser_ShouldReturnFailure()
    {
        // Arrange
        var userId = Guid.NewGuid();
        var command = new RefreshTokenCommand
        {
            AccessToken = "valid_access_token",
            RefreshToken = "valid_refresh_token"
        };

        var user = new User("testuser", "<EMAIL>", "Test", "User", UserRole.User);
        user.GetType().GetProperty("IsActive")?.SetValue(user, false);

        MockTokenService.Setup(x => x.GetUserIdFromToken(command.AccessToken))
            .Returns(userId);

        MockUserRepository.Setup(x => x.GetByIdAsync(userId, It.IsAny<CancellationToken>()))
            .ReturnsAsync(user);

        // Act
        var result = await _handler.Handle(command, CreateCancellationToken());

        // Assert
        result.Should().NotBeNull();
        result.Success.Should().BeFalse();
        result.ErrorMessage.Should().Contain("Account is inactive");
        result.AccessToken.Should().BeNull();

        MockTokenService.Verify(x => x.GenerateAccessToken(It.IsAny<User>()), Times.Never);
        VerifyWarningLogged();
    }

    [Fact]
    public async Task Handle_WithLockedUser_ShouldReturnFailure()
    {
        // Arrange
        var userId = Guid.NewGuid();
        var command = new RefreshTokenCommand
        {
            AccessToken = "valid_access_token",
            RefreshToken = "valid_refresh_token"
        };

        var user = new User("testuser", "<EMAIL>", "Test", "User", UserRole.User);
        user.GetType().GetProperty("IsLockedOut")?.SetValue(user, true);
        user.GetType().GetProperty("LockoutEnd")?.SetValue(user, DateTime.UtcNow.AddMinutes(30));

        MockTokenService.Setup(x => x.GetUserIdFromToken(command.AccessToken))
            .Returns(userId);

        MockUserRepository.Setup(x => x.GetByIdAsync(userId, It.IsAny<CancellationToken>()))
            .ReturnsAsync(user);

        // Act
        var result = await _handler.Handle(command, CreateCancellationToken());

        // Assert
        result.Should().NotBeNull();
        result.Success.Should().BeFalse();
        result.ErrorMessage.Should().Contain("Account is locked");
        result.AccessToken.Should().BeNull();

        MockTokenService.Verify(x => x.GenerateAccessToken(It.IsAny<User>()), Times.Never);
        VerifyWarningLogged();
    }

    [Fact]
    public async Task Handle_WithEmptyRefreshToken_ShouldReturnFailure()
    {
        // Arrange
        var userId = Guid.NewGuid();
        var command = new RefreshTokenCommand
        {
            AccessToken = "valid_access_token",
            RefreshToken = ""
        };

        var user = new User("testuser", "<EMAIL>", "Test", "User", UserRole.User);

        MockTokenService.Setup(x => x.GetUserIdFromToken(command.AccessToken))
            .Returns(userId);

        MockUserRepository.Setup(x => x.GetByIdAsync(userId, It.IsAny<CancellationToken>()))
            .ReturnsAsync(user);

        // Act
        var result = await _handler.Handle(command, CreateCancellationToken());

        // Assert
        result.Should().NotBeNull();
        result.Success.Should().BeFalse();
        result.ErrorMessage.Should().Contain("Invalid refresh token");
        result.AccessToken.Should().BeNull();

        MockTokenService.Verify(x => x.GenerateAccessToken(It.IsAny<User>()), Times.Never);
        VerifyWarningLogged();
    }

    [Fact]
    public async Task Handle_WithRepositoryException_ShouldReturnFailure()
    {
        // Arrange
        var userId = Guid.NewGuid();
        var command = new RefreshTokenCommand
        {
            AccessToken = "valid_access_token",
            RefreshToken = "valid_refresh_token"
        };

        MockTokenService.Setup(x => x.GetUserIdFromToken(command.AccessToken))
            .Returns(userId);

        MockUserRepository.Setup(x => x.GetByIdAsync(userId, It.IsAny<CancellationToken>()))
            .ThrowsAsync(new InvalidOperationException("Database error"));

        // Act
        var result = await _handler.Handle(command, CreateCancellationToken());

        // Assert
        result.Should().NotBeNull();
        result.Success.Should().BeFalse();
        result.ErrorMessage.Should().Contain("error occurred during token refresh");
        result.AccessToken.Should().BeNull();

        VerifyErrorLogged();
    }

    [Theory]
    [InlineData(UserRole.Admin)]
    [InlineData(UserRole.Manager)]
    [InlineData(UserRole.Driver)]
    [InlineData(UserRole.User)]
    public async Task Handle_WithDifferentUserRoles_ShouldRefreshTokensSuccessfully(UserRole role)
    {
        // Arrange
        var userId = Guid.NewGuid();
        var command = new RefreshTokenCommand
        {
            AccessToken = "valid_access_token",
            RefreshToken = "valid_refresh_token"
        };

        var user = new User("testuser", "<EMAIL>", "Test", "User", role);

        MockTokenService.Setup(x => x.GetUserIdFromToken(command.AccessToken))
            .Returns(userId);

        MockUserRepository.Setup(x => x.GetByIdAsync(userId, It.IsAny<CancellationToken>()))
            .ReturnsAsync(user);

        MockTokenService.Setup(x => x.GenerateAccessToken(user))
            .Returns("new_access_token");

        MockTokenService.Setup(x => x.GenerateRefreshToken())
            .Returns("new_refresh_token");

        MockTokenService.Setup(x => x.GetAccessTokenExpiry())
            .Returns(TimeSpan.FromHours(1));

        // Act
        var result = await _handler.Handle(command, CreateCancellationToken());

        // Assert
        result.Should().NotBeNull();
        result.Success.Should().BeTrue();
        result.User.Should().NotBeNull();
        result.User!.Role.Should().Be(role.ToString());
    }

    [Fact]
    public async Task Handle_WithValidRequest_ShouldGenerateNewTokensWithCorrectExpiry()
    {
        // Arrange
        var userId = Guid.NewGuid();
        var command = new RefreshTokenCommand
        {
            AccessToken = "valid_access_token",
            RefreshToken = "valid_refresh_token"
        };

        var user = new User("testuser", "<EMAIL>", "Test", "User", UserRole.User);
        var tokenExpiry = TimeSpan.FromMinutes(30);

        MockTokenService.Setup(x => x.GetUserIdFromToken(command.AccessToken))
            .Returns(userId);

        MockUserRepository.Setup(x => x.GetByIdAsync(userId, It.IsAny<CancellationToken>()))
            .ReturnsAsync(user);

        MockTokenService.Setup(x => x.GenerateAccessToken(user))
            .Returns("new_access_token");

        MockTokenService.Setup(x => x.GenerateRefreshToken())
            .Returns("new_refresh_token");

        MockTokenService.Setup(x => x.GetAccessTokenExpiry())
            .Returns(tokenExpiry);

        // Act
        var result = await _handler.Handle(command, CreateCancellationToken());

        // Assert
        result.Should().NotBeNull();
        result.Success.Should().BeTrue();
        result.TokenExpiry.Should().BeCloseTo(DateTime.UtcNow.Add(tokenExpiry), TimeSpan.FromMinutes(1));

        MockTokenService.Verify(x => x.GetAccessTokenExpiry(), Times.Once);
    }
}
