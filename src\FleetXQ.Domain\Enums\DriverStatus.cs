namespace FleetXQ.Domain.Enums;

/// <summary>
/// Represents the status of a driver
/// </summary>
public enum DriverStatus
{
    /// <summary>
    /// Driver is active and available
    /// </summary>
    Active = 1,

    /// <summary>
    /// Driver is inactive
    /// </summary>
    Inactive = 2,

    /// <summary>
    /// Driver is on leave
    /// </summary>
    OnLeave = 3,

    /// <summary>
    /// Driver has been terminated
    /// </summary>
    Terminated = 4
}
