using FleetXQ.Domain.Common;
using FleetXQ.Domain.Enums;
using FleetXQ.Domain.ValueObjects;

namespace FleetXQ.Domain.Events;

/// <summary>
/// Domain event raised when an alert is triggered in the system
/// </summary>
public sealed class AlertTriggeredEvent : BaseDomainEvent
{
    /// <summary>
    /// Gets the alert ID
    /// </summary>
    public Guid AlertId { get; }

    /// <summary>
    /// Gets the alert type
    /// </summary>
    public AlertType AlertType { get; }

    /// <summary>
    /// Gets the alert severity
    /// </summary>
    public AlertSeverity Severity { get; }

    /// <summary>
    /// Gets the alert message
    /// </summary>
    public string Message { get; }

    /// <summary>
    /// Gets the vehicle ID associated with the alert
    /// </summary>
    public Guid? VehicleId { get; }

    /// <summary>
    /// Gets the driver ID associated with the alert
    /// </summary>
    public Guid? DriverId { get; }

    /// <summary>
    /// Gets the location where the alert was triggered
    /// </summary>
    public Location? Location { get; }

    /// <summary>
    /// Gets additional metadata about the alert
    /// </summary>
    public Dictionary<string, object> Metadata { get; }

    /// <summary>
    /// Gets a value indicating whether the alert requires immediate attention
    /// </summary>
    public bool RequiresImmediateAttention => Severity == AlertSeverity.Critical;

    /// <summary>
    /// Initializes a new instance of the <see cref="AlertTriggeredEvent"/> class
    /// </summary>
    /// <param name="alertId">The alert ID</param>
    /// <param name="alertType">The alert type</param>
    /// <param name="severity">The alert severity</param>
    /// <param name="message">The alert message</param>
    /// <param name="vehicleId">The vehicle ID</param>
    /// <param name="driverId">The driver ID</param>
    /// <param name="location">The location</param>
    /// <param name="metadata">Additional metadata</param>
    public AlertTriggeredEvent(Guid alertId, AlertType alertType, AlertSeverity severity, string message,
        Guid? vehicleId = null, Guid? driverId = null, Location? location = null, 
        Dictionary<string, object>? metadata = null)
    {
        if (alertId == Guid.Empty)
            throw new ArgumentException("Alert ID cannot be empty", nameof(alertId));
        
        if (string.IsNullOrWhiteSpace(message))
            throw new ArgumentException("Alert message cannot be empty", nameof(message));

        AlertId = alertId;
        AlertType = alertType;
        Severity = severity;
        Message = message;
        VehicleId = vehicleId;
        DriverId = driverId;
        Location = location;
        Metadata = metadata ?? new Dictionary<string, object>();
    }

    /// <summary>
    /// Creates an alert triggered event for a speed violation
    /// </summary>
    /// <param name="alertId">The alert ID</param>
    /// <param name="vehicleId">The vehicle ID</param>
    /// <param name="driverId">The driver ID</param>
    /// <param name="currentSpeed">The current speed</param>
    /// <param name="speedLimit">The speed limit</param>
    /// <param name="location">The location</param>
    /// <returns>A new AlertTriggeredEvent</returns>
    public static AlertTriggeredEvent ForSpeedViolation(Guid alertId, Guid vehicleId, Guid? driverId,
        Speed currentSpeed, Speed speedLimit, Location location)
    {
        var percentage = currentSpeed.PercentageOverLimit(speedLimit);
        var severity = percentage switch
        {
            > 50 => AlertSeverity.Critical,
            > 25 => AlertSeverity.High,
            > 10 => AlertSeverity.Medium,
            _ => AlertSeverity.Low
        };

        var message = $"Speed violation: {currentSpeed} in {speedLimit} zone ({percentage:F1}% over limit)";
        var metadata = new Dictionary<string, object>
        {
            ["currentSpeed"] = currentSpeed.KilometersPerHour,
            ["speedLimit"] = speedLimit.KilometersPerHour,
            ["percentageOver"] = percentage
        };

        return new AlertTriggeredEvent(alertId, AlertType.Speed, severity, message, vehicleId, driverId, location, metadata);
    }

    /// <summary>
    /// Creates an alert triggered event for low fuel
    /// </summary>
    /// <param name="alertId">The alert ID</param>
    /// <param name="vehicleId">The vehicle ID</param>
    /// <param name="fuelLevel">The current fuel level</param>
    /// <param name="location">The location</param>
    /// <returns>A new AlertTriggeredEvent</returns>
    public static AlertTriggeredEvent ForLowFuel(Guid alertId, Guid vehicleId, FuelLevel fuelLevel, Location? location = null)
    {
        var severity = fuelLevel.GetCategory() switch
        {
            FuelLevelCategory.Empty => AlertSeverity.Critical,
            FuelLevelCategory.Critical => AlertSeverity.Critical,
            FuelLevelCategory.Low => AlertSeverity.High,
            _ => AlertSeverity.Medium
        };

        var message = $"Low fuel alert: {fuelLevel} ({fuelLevel.GetCategory()})";
        var metadata = new Dictionary<string, object>
        {
            ["fuelPercentage"] = fuelLevel.Percentage,
            ["fuelCategory"] = fuelLevel.GetCategory().ToString()
        };

        return new AlertTriggeredEvent(alertId, AlertType.Fuel, severity, message, vehicleId, null, location, metadata);
    }

    /// <summary>
    /// Creates an alert triggered event for maintenance due
    /// </summary>
    /// <param name="alertId">The alert ID</param>
    /// <param name="vehicleId">The vehicle ID</param>
    /// <param name="maintenanceType">The maintenance type</param>
    /// <param name="dueDate">The due date</param>
    /// <returns>A new AlertTriggeredEvent</returns>
    public static AlertTriggeredEvent ForMaintenanceDue(Guid alertId, Guid vehicleId, string maintenanceType, DateTime dueDate)
    {
        var daysUntilDue = (dueDate - DateTime.UtcNow).TotalDays;
        var severity = daysUntilDue switch
        {
            < 0 => AlertSeverity.Critical,
            < 1 => AlertSeverity.High,
            < 7 => AlertSeverity.Medium,
            _ => AlertSeverity.Low
        };

        var message = daysUntilDue < 0
            ? $"Maintenance overdue: {maintenanceType} (overdue by {Math.Abs(daysUntilDue):F0} days)"
            : $"Maintenance due: {maintenanceType} (due in {daysUntilDue:F0} days)";

        var metadata = new Dictionary<string, object>
        {
            ["maintenanceType"] = maintenanceType,
            ["dueDate"] = dueDate,
            ["daysUntilDue"] = daysUntilDue
        };

        return new AlertTriggeredEvent(alertId, AlertType.Maintenance, severity, message, vehicleId, null, null, metadata);
    }

    /// <summary>
    /// Creates an alert triggered event for harsh driving
    /// </summary>
    /// <param name="alertId">The alert ID</param>
    /// <param name="vehicleId">The vehicle ID</param>
    /// <param name="driverId">The driver ID</param>
    /// <param name="eventType">The harsh driving event type</param>
    /// <param name="location">The location</param>
    /// <returns>A new AlertTriggeredEvent</returns>
    public static AlertTriggeredEvent ForHarshDriving(Guid alertId, Guid vehicleId, Guid driverId, 
        string eventType, Location location)
    {
        var message = $"Harsh driving detected: {eventType}";
        var metadata = new Dictionary<string, object>
        {
            ["eventType"] = eventType
        };

        return new AlertTriggeredEvent(alertId, AlertType.HarshDriving, AlertSeverity.Medium, message, 
            vehicleId, driverId, location, metadata);
    }
}
