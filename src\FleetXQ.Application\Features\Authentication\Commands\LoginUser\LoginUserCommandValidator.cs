using FluentValidation;

namespace FleetXQ.Application.Features.Authentication.Commands.LoginUser;

/// <summary>
/// Validator for LoginUserCommand
/// </summary>
public sealed class LoginUserCommandValidator : AbstractValidator<LoginUserCommand>
{
    /// <summary>
    /// Initializes a new instance of the <see cref="LoginUserCommandValidator"/> class
    /// </summary>
    public LoginUserCommandValidator()
    {
        RuleFor(x => x.UsernameOrEmail)
            .NotEmpty()
            .WithMessage("Username or email is required")
            .MaximumLength(100)
            .WithMessage("Username or email cannot exceed 100 characters");

        RuleFor(x => x.Password)
            .NotEmpty()
            .WithMessage("Password is required")
            .MinimumLength(1)
            .WithMessage("Password is required")
            .MaximumLength(500)
            .WithMessage("Password cannot exceed 500 characters");

        RuleFor(x => x.ClientIpAddress)
            .MaximumLength(45) // IPv6 max length
            .WithMessage("Client IP address cannot exceed 45 characters")
            .When(x => !string.IsNullOrWhiteSpace(x.ClientIpAddress));

        RuleFor(x => x.UserAgent)
            .MaximumLength(500)
            .WithMessage("User agent cannot exceed 500 characters")
            .When(x => !string.IsNullOrWhiteSpace(x.UserAgent));
    }
}
