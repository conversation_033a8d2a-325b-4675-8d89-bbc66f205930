using FleetXQ.Application.Features.Telemetry.Commands.ProcessTelemetryData;
using FleetXQ.Application.Tests.Common;
using FleetXQ.Domain.Entities;
using FleetXQ.Domain.Enums;
using FleetXQ.Domain.ValueObjects;
using FluentAssertions;
using Moq;

namespace FleetXQ.Application.Tests.Features.Telemetry.Commands;

public class ProcessTelemetryDataCommandHandlerTests : CommandHandlerTestBase<ProcessTelemetryDataCommandHandler>
{
    private readonly ProcessTelemetryDataCommandHandler _handler;

    public ProcessTelemetryDataCommandHandlerTests()
    {
        _handler = new ProcessTelemetryDataCommandHandler(
            MockVehicleRepository.Object,
            MockAlertRepository.Object,
            MockAlertEvaluationService.Object,
            MockLogger.Object);
    }

    [Fact]
    public async Task Handle_WithValidTelemetryData_ShouldProcessSuccessfully()
    {
        // Arrange
        var vehicleId = Guid.NewGuid();
        var command = new ProcessTelemetryDataCommand
        {
            VehicleId = vehicleId,
            Latitude = 40.7128m,
            Longitude = -74.0060m,
            SpeedKmh = 65m,
            FuelLevelPercentage = 75m,
            Mileage = 50000m,
            Timestamp = DateTime.UtcNow,
            EngineTemperature = 90m,
            RPM = 2500
        };

        var vehicle = new Vehicle("Test Vehicle", "ABC-123", "Car");
        MockVehicleRepository.Setup(x => x.GetByIdAsync(vehicleId, It.IsAny<CancellationToken>()))
            .ReturnsAsync(vehicle);

        MockVehicleRepository.Setup(x => x.UpdateAsync(It.IsAny<Vehicle>(), It.IsAny<CancellationToken>()))
            .Returns(Task.CompletedTask);

        MockAlertEvaluationService.Setup(x => x.EvaluateTelemetryData(
            It.IsAny<Vehicle>(), 
            It.IsAny<Driver?>(), 
            It.IsAny<Location>(), 
            It.IsAny<Speed>(), 
            It.IsAny<FuelLevel?>()))
            .Returns(new List<Alert>());

        // Act
        var result = await _handler.Handle(command, CreateCancellationToken());

        // Assert
        result.Should().NotBeNull();
        result.Success.Should().BeTrue();
        result.VehicleId.Should().Be(vehicleId);
        result.AlertsTriggered.Should().BeFalse();
        result.TriggeredAlerts.Should().BeEmpty();
        result.ErrorMessage.Should().BeNull();

        MockVehicleRepository.Verify(x => x.GetByIdAsync(vehicleId, It.IsAny<CancellationToken>()), Times.Once);
        VerifyRepositoryUpdateCalled(MockVehicleRepository);
        MockAlertEvaluationService.Verify(x => x.EvaluateTelemetryData(
            It.IsAny<Vehicle>(), 
            It.IsAny<Driver?>(), 
            It.IsAny<Location>(), 
            It.IsAny<Speed>(), 
            It.IsAny<FuelLevel?>()), Times.Once);
        VerifyInformationLogged(Times.AtLeastOnce);
    }

    [Fact]
    public async Task Handle_WithNonExistentVehicle_ShouldReturnFailure()
    {
        // Arrange
        var vehicleId = Guid.NewGuid();
        var command = new ProcessTelemetryDataCommand
        {
            VehicleId = vehicleId,
            Latitude = 40.7128m,
            Longitude = -74.0060m,
            SpeedKmh = 65m
        };

        MockVehicleRepository.Setup(x => x.GetByIdAsync(vehicleId, It.IsAny<CancellationToken>()))
            .ReturnsAsync((Vehicle?)null);

        // Act
        var result = await _handler.Handle(command, CreateCancellationToken());

        // Assert
        result.Should().NotBeNull();
        result.Success.Should().BeFalse();
        result.VehicleId.Should().Be(vehicleId);
        result.ErrorMessage.Should().Contain("not found");

        MockVehicleRepository.Verify(x => x.UpdateAsync(It.IsAny<Vehicle>(), It.IsAny<CancellationToken>()), Times.Never);
        MockAlertEvaluationService.Verify(x => x.EvaluateTelemetryData(
            It.IsAny<Vehicle>(), 
            It.IsAny<Driver?>(), 
            It.IsAny<Location>(), 
            It.IsAny<Speed>(), 
            It.IsAny<FuelLevel?>()), Times.Never);
        VerifyWarningLogged();
    }

    [Fact]
    public async Task Handle_WithAlertsTriggered_ShouldCreateAlerts()
    {
        // Arrange
        var vehicleId = Guid.NewGuid();
        var command = new ProcessTelemetryDataCommand
        {
            VehicleId = vehicleId,
            Latitude = 40.7128m,
            Longitude = -74.0060m,
            SpeedKmh = 120m, // High speed to trigger alert
            FuelLevelPercentage = 5m // Low fuel to trigger alert
        };

        var vehicle = new Vehicle("Test Vehicle", "ABC-123", "Car");
        MockVehicleRepository.Setup(x => x.GetByIdAsync(vehicleId, It.IsAny<CancellationToken>()))
            .ReturnsAsync(vehicle);

        MockVehicleRepository.Setup(x => x.UpdateAsync(It.IsAny<Vehicle>(), It.IsAny<CancellationToken>()))
            .Returns(Task.CompletedTask);

        var alerts = new List<Alert>
        {
            new(AlertType.Speeding, AlertSeverity.High, vehicleId, null, "High speed detected"),
            new(AlertType.LowFuel, AlertSeverity.Medium, vehicleId, null, "Low fuel level")
        };

        MockAlertEvaluationService.Setup(x => x.EvaluateTelemetryData(
            It.IsAny<Vehicle>(), 
            It.IsAny<Driver?>(), 
            It.IsAny<Location>(), 
            It.IsAny<Speed>(), 
            It.IsAny<FuelLevel?>()))
            .Returns(alerts);

        MockAlertRepository.Setup(x => x.AddAsync(It.IsAny<Alert>(), It.IsAny<CancellationToken>()))
            .Returns(Task.CompletedTask);

        // Act
        var result = await _handler.Handle(command, CreateCancellationToken());

        // Assert
        result.Should().NotBeNull();
        result.Success.Should().BeTrue();
        result.VehicleId.Should().Be(vehicleId);
        result.AlertsTriggered.Should().BeTrue();
        result.TriggeredAlerts.Should().HaveCount(2);
        result.TriggeredAlerts.Should().Contain("Speeding");
        result.TriggeredAlerts.Should().Contain("LowFuel");

        MockAlertRepository.Verify(x => x.AddAsync(It.IsAny<Alert>(), It.IsAny<CancellationToken>()), Times.Exactly(2));
        VerifyInformationLogged(Times.AtLeastOnce);
    }

    [Fact]
    public async Task Handle_WithMinimalTelemetryData_ShouldProcessSuccessfully()
    {
        // Arrange
        var vehicleId = Guid.NewGuid();
        var command = new ProcessTelemetryDataCommand
        {
            VehicleId = vehicleId,
            Latitude = 0m,
            Longitude = 0m,
            SpeedKmh = 0m
        };

        var vehicle = new Vehicle("Test Vehicle", "ABC-123", "Car");
        MockVehicleRepository.Setup(x => x.GetByIdAsync(vehicleId, It.IsAny<CancellationToken>()))
            .ReturnsAsync(vehicle);

        MockVehicleRepository.Setup(x => x.UpdateAsync(It.IsAny<Vehicle>(), It.IsAny<CancellationToken>()))
            .Returns(Task.CompletedTask);

        MockAlertEvaluationService.Setup(x => x.EvaluateTelemetryData(
            It.IsAny<Vehicle>(), 
            It.IsAny<Driver?>(), 
            It.IsAny<Location>(), 
            It.IsAny<Speed>(), 
            It.IsAny<FuelLevel?>()))
            .Returns(new List<Alert>());

        // Act
        var result = await _handler.Handle(command, CreateCancellationToken());

        // Assert
        result.Should().NotBeNull();
        result.Success.Should().BeTrue();
        result.VehicleId.Should().Be(vehicleId);
        result.AlertsTriggered.Should().BeFalse();

        VerifyRepositoryUpdateCalled(MockVehicleRepository);
    }

    [Fact]
    public async Task Handle_WithRepositoryException_ShouldReturnFailure()
    {
        // Arrange
        var vehicleId = Guid.NewGuid();
        var command = new ProcessTelemetryDataCommand
        {
            VehicleId = vehicleId,
            Latitude = 40.7128m,
            Longitude = -74.0060m,
            SpeedKmh = 65m
        };

        MockVehicleRepository.Setup(x => x.GetByIdAsync(vehicleId, It.IsAny<CancellationToken>()))
            .ThrowsAsync(new InvalidOperationException("Database error"));

        // Act
        var result = await _handler.Handle(command, CreateCancellationToken());

        // Assert
        result.Should().NotBeNull();
        result.Success.Should().BeFalse();
        result.VehicleId.Should().Be(vehicleId);
        result.ErrorMessage.Should().Contain("error occurred");

        VerifyErrorLogged();
    }

    [Fact]
    public async Task Handle_WithInvalidTelemetryData_ShouldReturnFailure()
    {
        // Arrange
        var vehicleId = Guid.NewGuid();
        var command = new ProcessTelemetryDataCommand
        {
            VehicleId = vehicleId,
            Latitude = 40.7128m,
            Longitude = -74.0060m,
            SpeedKmh = 65m
        };

        var vehicle = new Vehicle("Test Vehicle", "ABC-123", "Car");
        MockVehicleRepository.Setup(x => x.GetByIdAsync(vehicleId, It.IsAny<CancellationToken>()))
            .ReturnsAsync(vehicle);

        MockVehicleRepository.Setup(x => x.UpdateAsync(It.IsAny<Vehicle>(), It.IsAny<CancellationToken>()))
            .ThrowsAsync(new ArgumentException("Invalid telemetry data"));

        // Act
        var result = await _handler.Handle(command, CreateCancellationToken());

        // Assert
        result.Should().NotBeNull();
        result.Success.Should().BeFalse();
        result.VehicleId.Should().Be(vehicleId);
        result.ErrorMessage.Should().Contain("Invalid telemetry data");

        VerifyWarningLogged();
    }

    [Fact]
    public async Task Handle_WithAlertCreationFailure_ShouldContinueProcessing()
    {
        // Arrange
        var vehicleId = Guid.NewGuid();
        var command = new ProcessTelemetryDataCommand
        {
            VehicleId = vehicleId,
            Latitude = 40.7128m,
            Longitude = -74.0060m,
            SpeedKmh = 120m // High speed to trigger alert
        };

        var vehicle = new Vehicle("Test Vehicle", "ABC-123", "Car");
        MockVehicleRepository.Setup(x => x.GetByIdAsync(vehicleId, It.IsAny<CancellationToken>()))
            .ReturnsAsync(vehicle);

        MockVehicleRepository.Setup(x => x.UpdateAsync(It.IsAny<Vehicle>(), It.IsAny<CancellationToken>()))
            .Returns(Task.CompletedTask);

        var alerts = new List<Alert>
        {
            new(AlertType.Speeding, AlertSeverity.High, vehicleId, null, "High speed detected")
        };

        MockAlertEvaluationService.Setup(x => x.EvaluateTelemetryData(
            It.IsAny<Vehicle>(), 
            It.IsAny<Driver?>(), 
            It.IsAny<Location>(), 
            It.IsAny<Speed>(), 
            It.IsAny<FuelLevel?>()))
            .Returns(alerts);

        MockAlertRepository.Setup(x => x.AddAsync(It.IsAny<Alert>(), It.IsAny<CancellationToken>()))
            .ThrowsAsync(new InvalidOperationException("Alert creation failed"));

        // Act
        var result = await _handler.Handle(command, CreateCancellationToken());

        // Assert
        result.Should().NotBeNull();
        result.Success.Should().BeTrue(); // Should still succeed even if alert creation fails
        result.VehicleId.Should().Be(vehicleId);
        result.AlertsTriggered.Should().BeFalse(); // No alerts were successfully created

        VerifyRepositoryUpdateCalled(MockVehicleRepository);
        VerifyErrorLogged(); // Should log the alert creation error
    }
}
