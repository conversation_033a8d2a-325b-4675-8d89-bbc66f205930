using FleetXQ.Application.Common.Behaviors;
using FluentAssertions;
using MediatR;
using Microsoft.Extensions.Logging;
using Moq;

namespace FleetXQ.Application.Tests.Common.Behaviors;

public class ExceptionHandlingBehaviorTests
{
    private readonly Mock<ILogger<ExceptionHandlingBehavior<TestRequest, TestResponse>>> _mockLogger;
    private readonly Mock<RequestHandlerDelegate<TestResponse>> _mockNext;
    private readonly ExceptionHandlingBehavior<TestRequest, TestResponse> _behavior;

    public ExceptionHandlingBehaviorTests()
    {
        _mockLogger = new Mock<ILogger<ExceptionHandlingBehavior<TestRequest, TestResponse>>>();
        _mockNext = new Mock<RequestHandlerDelegate<TestResponse>>();
        _behavior = new ExceptionHandlingBehavior<TestRequest, TestResponse>(_mockLogger.Object);
    }

    [Fact]
    public async Task Handle_WithSuccessfulRequest_ShouldCallNext()
    {
        // Arrange
        var request = new TestRequest { Value = "test" };
        var expectedResponse = new TestResponse { Result = "success" };

        _mockNext.Setup(x => x()).ReturnsAsync(expectedResponse);

        // Act
        var result = await _behavior.Handle(request, _mockNext.Object, CancellationToken.None);

        // Assert
        result.Should().Be(expectedResponse);
        _mockNext.Verify(x => x(), Times.Once);

        // No exception logging should occur
        VerifyLoggerCalled(LogLevel.Error, Times.Never);
        VerifyLoggerCalled(LogLevel.Warning, Times.Never);
    }

    [Fact]
    public async Task Handle_WithValidationException_ShouldLogWarningAndRethrow()
    {
        // Arrange
        var request = new TestRequest { Value = "test" };
        var validationErrors = new Dictionary<string, string[]>
        {
            { "Property1", new[] { "Error1", "Error2" } }
        };
        var validationException = new ValidationException(validationErrors);

        _mockNext.Setup(x => x()).ThrowsAsync(validationException);

        // Act & Assert
        var thrownException = await Assert.ThrowsAsync<ValidationException>(() =>
            _behavior.Handle(request, _mockNext.Object, CancellationToken.None));

        thrownException.Should().Be(validationException);

        // Verify warning logging for validation failures
        VerifyLoggerCalled(LogLevel.Warning, Times.Once, "Validation failed");

        _mockNext.Verify(x => x(), Times.Once);
    }

    [Fact]
    public async Task Handle_WithUnauthorizedAccessException_ShouldLogWarningAndRethrow()
    {
        // Arrange
        var request = new TestRequest { Value = "test" };
        var unauthorizedException = new UnauthorizedAccessException("Access denied");

        _mockNext.Setup(x => x()).ThrowsAsync(unauthorizedException);

        // Act & Assert
        var thrownException = await Assert.ThrowsAsync<UnauthorizedAccessException>(() =>
            _behavior.Handle(request, _mockNext.Object, CancellationToken.None));

        thrownException.Should().Be(unauthorizedException);

        // Verify warning logging for unauthorized access
        VerifyLoggerCalled(LogLevel.Warning, Times.Once, "Unauthorized access");

        _mockNext.Verify(x => x(), Times.Once);
    }

    [Fact]
    public async Task Handle_WithArgumentException_ShouldLogWarningAndRethrow()
    {
        // Arrange
        var request = new TestRequest { Value = "test" };
        var argumentException = new ArgumentException("Invalid argument");

        _mockNext.Setup(x => x()).ThrowsAsync(argumentException);

        // Act & Assert
        var thrownException = await Assert.ThrowsAsync<ArgumentException>(() =>
            _behavior.Handle(request, _mockNext.Object, CancellationToken.None));

        thrownException.Should().Be(argumentException);

        // Verify warning logging for invalid arguments
        VerifyLoggerCalled(LogLevel.Warning, Times.Once, "Invalid argument");

        _mockNext.Verify(x => x(), Times.Once);
    }

    [Fact]
    public async Task Handle_WithInvalidOperationException_ShouldLogWarningAndRethrow()
    {
        // Arrange
        var request = new TestRequest { Value = "test" };
        var invalidOperationException = new InvalidOperationException("Invalid operation");

        _mockNext.Setup(x => x()).ThrowsAsync(invalidOperationException);

        // Act & Assert
        var thrownException = await Assert.ThrowsAsync<InvalidOperationException>(() =>
            _behavior.Handle(request, _mockNext.Object, CancellationToken.None));

        thrownException.Should().Be(invalidOperationException);

        // Verify warning logging for invalid operations
        VerifyLoggerCalled(LogLevel.Warning, Times.Once, "Invalid operation");

        _mockNext.Verify(x => x(), Times.Once);
    }

    [Fact]
    public async Task Handle_WithOperationCanceledException_ShouldLogInformationAndRethrow()
    {
        // Arrange
        var request = new TestRequest { Value = "test" };
        var cancellationTokenSource = new CancellationTokenSource();
        cancellationTokenSource.Cancel();
        var cancellationToken = cancellationTokenSource.Token;
        var canceledException = new OperationCanceledException("Operation was cancelled", cancellationToken);

        _mockNext.Setup(x => x()).ThrowsAsync(canceledException);

        // Act & Assert
        var thrownException = await Assert.ThrowsAsync<OperationCanceledException>(() =>
            _behavior.Handle(request, _mockNext.Object, cancellationToken));

        thrownException.Should().Be(canceledException);

        // Verify information logging for cancellation
        VerifyLoggerCalled(LogLevel.Information, Times.Once, "cancelled");

        _mockNext.Verify(x => x(), Times.Once);
    }

    [Fact]
    public async Task Handle_WithUnhandledException_ShouldLogErrorAndThrowApplicationException()
    {
        // Arrange
        var request = new TestRequest { Value = "test" };
        var unhandledException = new InvalidCastException("Unexpected error");

        _mockNext.Setup(x => x()).ThrowsAsync(unhandledException);

        // Act & Assert
        var thrownException = await Assert.ThrowsAsync<ApplicationException>(() =>
            _behavior.Handle(request, _mockNext.Object, CancellationToken.None));

        thrownException.Should().NotBeNull();
        thrownException.Message.Should().Contain("TestRequest");
        thrownException.InnerException.Should().Be(unhandledException);

        // Verify error logging for unhandled exceptions
        VerifyLoggerCalled(LogLevel.Error, Times.Once, "Unhandled exception");

        _mockNext.Verify(x => x(), Times.Once);
    }

    [Fact]
    public async Task Handle_WithCancellationToken_ShouldPassTokenToNext()
    {
        // Arrange
        var request = new TestRequest { Value = "test" };
        var expectedResponse = new TestResponse { Result = "success" };
        var cancellationToken = new CancellationTokenSource().Token;

        _mockNext.Setup(x => x()).ReturnsAsync(expectedResponse);

        // Act
        var result = await _behavior.Handle(request, _mockNext.Object, cancellationToken);

        // Assert
        result.Should().Be(expectedResponse);
        _mockNext.Verify(x => x(), Times.Once);
    }

    [Fact]
    public async Task Handle_ShouldLogRequestName()
    {
        // Arrange
        var request = new TestRequest { Value = "test" };
        var exception = new InvalidOperationException("Test exception");

        _mockNext.Setup(x => x()).ThrowsAsync(exception);

        // Act & Assert
        await Assert.ThrowsAsync<InvalidOperationException>(() =>
            _behavior.Handle(request, _mockNext.Object, CancellationToken.None));

        // Verify request name is included in logs
        VerifyLoggerCalled(LogLevel.Warning, Times.Once, "TestRequest");
    }

    [Fact]
    public void ApplicationException_WithMessage_ShouldCreateCorrectException()
    {
        // Arrange
        var message = "Test error message";

        // Act
        var exception = new ApplicationException(message);

        // Assert
        exception.Should().NotBeNull();
        exception.Message.Should().Be(message);
        exception.ErrorCode.Should().BeNull();
        exception.Details.Should().BeNull();
    }

    [Fact]
    public void ApplicationException_WithMessageAndInnerException_ShouldCreateCorrectException()
    {
        // Arrange
        var message = "Test error message";
        var innerException = new InvalidOperationException("Inner exception");

        // Act
        var exception = new ApplicationException(message, innerException);

        // Assert
        exception.Should().NotBeNull();
        exception.Message.Should().Be(message);
        exception.InnerException.Should().Be(innerException);
        exception.ErrorCode.Should().BeNull();
        exception.Details.Should().BeNull();
    }

    [Fact]
    public void ApplicationException_WithErrorCode_ShouldCreateCorrectException()
    {
        // Arrange
        var message = "Test error message";
        var errorCode = "TEST_ERROR";
        var details = new Dictionary<string, object> { { "key", "value" } };

        // Act
        var exception = new ApplicationException(message, errorCode, details);

        // Assert
        exception.Should().NotBeNull();
        exception.Message.Should().Be(message);
        exception.ErrorCode.Should().Be(errorCode);
        exception.Details.Should().BeEquivalentTo(details);
    }

    [Fact]
    public void ApplicationException_WithErrorCodeAndInnerException_ShouldCreateCorrectException()
    {
        // Arrange
        var message = "Test error message";
        var errorCode = "TEST_ERROR";
        var innerException = new InvalidOperationException("Inner exception");
        var details = new Dictionary<string, object> { { "key", "value" } };

        // Act
        var exception = new ApplicationException(message, errorCode, innerException, details);

        // Assert
        exception.Should().NotBeNull();
        exception.Message.Should().Be(message);
        exception.ErrorCode.Should().Be(errorCode);
        exception.InnerException.Should().Be(innerException);
        exception.Details.Should().BeEquivalentTo(details);
    }

    private void VerifyLoggerCalled(LogLevel logLevel, Times times, string? messageContains = null)
    {
        _mockLogger.Verify(
            x => x.Log(
                logLevel,
                It.IsAny<EventId>(),
                messageContains != null 
                    ? It.Is<It.IsAnyType>((v, t) => v.ToString()!.Contains(messageContains))
                    : It.IsAny<It.IsAnyType>(),
                It.IsAny<Exception>(),
                It.IsAny<Func<It.IsAnyType, Exception?, string>>()),
            times);
    }

    // Test helper classes
    public class TestRequest : IRequest<TestResponse>
    {
        public string Value { get; set; } = string.Empty;
    }

    public class TestResponse
    {
        public string Result { get; set; } = string.Empty;
    }
}
