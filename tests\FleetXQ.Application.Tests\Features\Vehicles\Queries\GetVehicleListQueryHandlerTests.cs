using FleetXQ.Application.Features.Vehicles.Queries.GetVehicleList;
using FleetXQ.Application.Tests.Common;
using FleetXQ.Domain.Entities;
using FleetXQ.Domain.Enums;
using FleetXQ.Domain.ValueObjects;
using FluentAssertions;
using Moq;

namespace FleetXQ.Application.Tests.Features.Vehicles.Queries;

public class GetVehicleListQueryHandlerTests : QueryHandlerTestBase<GetVehicleListQueryHandler>
{
    private readonly GetVehicleListQueryHandler _handler;

    public GetVehicleListQueryHandlerTests()
    {
        _handler = new GetVehicleListQueryHandler(MockVehicleRepository.Object, Mapper, MockLogger.Object);
    }

    [Fact]
    public async Task Handle_WithDefaultQuery_ShouldReturnAllVehicles()
    {
        // Arrange
        var query = new GetVehicleListQuery();
        var vehicles = CreateTestVehicles();

        MockVehicleRepository.Setup(x => x.GetAllAsync(It.IsAny<CancellationToken>()))
            .ReturnsAsync(vehicles);

        // Act
        var result = await _handler.Handle(query, CreateCancellationToken());

        // Assert
        result.Should().NotBeNull();
        result.Success.Should().BeTrue();
        result.Vehicles.Should().HaveCount(3);
        result.TotalCount.Should().Be(3);
        result.PageNumber.Should().Be(1);
        result.PageSize.Should().Be(50);
        result.TotalPages.Should().Be(1);
        result.HasNextPage.Should().BeFalse();
        result.HasPreviousPage.Should().BeFalse();

        VerifyRepositoryGetAllCalled(MockVehicleRepository);
        VerifyInformationLogged(Times.AtLeastOnce);
    }

    [Fact]
    public async Task Handle_WithStatusFilter_ShouldReturnFilteredVehicles()
    {
        // Arrange
        var query = new GetVehicleListQuery { Status = VehicleStatus.Active };
        var vehicles = CreateTestVehicles();

        MockVehicleRepository.Setup(x => x.GetByStatusAsync(VehicleStatus.Active, It.IsAny<CancellationToken>()))
            .ReturnsAsync(vehicles.Where(v => v.Status == VehicleStatus.Active));

        // Act
        var result = await _handler.Handle(query, CreateCancellationToken());

        // Assert
        result.Should().NotBeNull();
        result.Success.Should().BeTrue();
        result.Vehicles.Should().HaveCount(2); // Only active vehicles
        result.TotalCount.Should().Be(2);

        MockVehicleRepository.Verify(x => x.GetByStatusAsync(VehicleStatus.Active, It.IsAny<CancellationToken>()), Times.Once);
    }

    [Fact]
    public async Task Handle_WithVehicleTypeFilter_ShouldReturnFilteredVehicles()
    {
        // Arrange
        var query = new GetVehicleListQuery { VehicleType = "Car" };
        var vehicles = CreateTestVehicles();

        MockVehicleRepository.Setup(x => x.GetByTypeAsync("Car", It.IsAny<CancellationToken>()))
            .ReturnsAsync(vehicles.Where(v => v.VehicleType == "Car"));

        // Act
        var result = await _handler.Handle(query, CreateCancellationToken());

        // Assert
        result.Should().NotBeNull();
        result.Success.Should().BeTrue();
        result.Vehicles.Should().HaveCount(2); // Only cars
        result.TotalCount.Should().Be(2);

        MockVehicleRepository.Verify(x => x.GetByTypeAsync("Car", It.IsAny<CancellationToken>()), Times.Once);
    }

    [Fact]
    public async Task Handle_WithAvailableOnlyFilter_ShouldReturnAvailableVehicles()
    {
        // Arrange
        var query = new GetVehicleListQuery { AvailableOnly = true };
        var vehicles = CreateTestVehicles();

        MockVehicleRepository.Setup(x => x.GetAvailableVehiclesAsync(It.IsAny<CancellationToken>()))
            .ReturnsAsync(vehicles.Where(v => v.IsAvailable));

        // Act
        var result = await _handler.Handle(query, CreateCancellationToken());

        // Assert
        result.Should().NotBeNull();
        result.Success.Should().BeTrue();
        result.Vehicles.Should().OnlyContain(v => v.IsAvailable);

        MockVehicleRepository.Verify(x => x.GetAvailableVehiclesAsync(It.IsAny<CancellationToken>()), Times.Once);
    }

    [Fact]
    public async Task Handle_WithSearchTerm_ShouldFilterBySearchTerm()
    {
        // Arrange
        var query = new GetVehicleListQuery { SearchTerm = "Toyota" };
        var vehicles = CreateTestVehicles();

        MockVehicleRepository.Setup(x => x.GetAllAsync(It.IsAny<CancellationToken>()))
            .ReturnsAsync(vehicles);

        // Act
        var result = await _handler.Handle(query, CreateCancellationToken());

        // Assert
        result.Should().NotBeNull();
        result.Success.Should().BeTrue();
        result.Vehicles.Should().HaveCount(1); // Only Toyota vehicle
        result.Vehicles.First().Brand.Should().Be("Toyota");
    }

    [Fact]
    public async Task Handle_WithPagination_ShouldReturnCorrectPage()
    {
        // Arrange
        var query = new GetVehicleListQuery 
        { 
            PageNumber = 2, 
            PageSize = 1 
        };
        var vehicles = CreateTestVehicles();

        MockVehicleRepository.Setup(x => x.GetAllAsync(It.IsAny<CancellationToken>()))
            .ReturnsAsync(vehicles);

        // Act
        var result = await _handler.Handle(query, CreateCancellationToken());

        // Assert
        result.Should().NotBeNull();
        result.Success.Should().BeTrue();
        result.Vehicles.Should().HaveCount(1); // One vehicle per page
        result.TotalCount.Should().Be(3);
        result.PageNumber.Should().Be(2);
        result.PageSize.Should().Be(1);
        result.TotalPages.Should().Be(3);
        result.HasNextPage.Should().BeTrue();
        result.HasPreviousPage.Should().BeTrue();
    }

    [Fact]
    public async Task Handle_WithSorting_ShouldReturnSortedResults()
    {
        // Arrange
        var query = new GetVehicleListQuery 
        { 
            SortBy = "VehicleName", 
            SortDescending = true 
        };
        var vehicles = CreateTestVehicles();

        MockVehicleRepository.Setup(x => x.GetAllAsync(It.IsAny<CancellationToken>()))
            .ReturnsAsync(vehicles);

        // Act
        var result = await _handler.Handle(query, CreateCancellationToken());

        // Assert
        result.Should().NotBeNull();
        result.Success.Should().BeTrue();
        result.Vehicles.Should().BeInDescendingOrder(v => v.VehicleName);
    }

    [Fact]
    public async Task Handle_WithRepositoryException_ShouldReturnFailure()
    {
        // Arrange
        var query = new GetVehicleListQuery();

        MockVehicleRepository.Setup(x => x.GetAllAsync(It.IsAny<CancellationToken>()))
            .ThrowsAsync(new InvalidOperationException("Database error"));

        // Act
        var result = await _handler.Handle(query, CreateCancellationToken());

        // Assert
        result.Should().NotBeNull();
        result.Success.Should().BeFalse();
        result.ErrorMessage.Should().Contain("error occurred");
        result.Vehicles.Should().BeEmpty();

        VerifyErrorLogged();
    }

    [Theory]
    [InlineData("VehicleName")]
    [InlineData("LicensePlate")]
    [InlineData("VehicleType")]
    [InlineData("Brand")]
    [InlineData("Model")]
    [InlineData("Status")]
    public async Task Handle_WithDifferentSortFields_ShouldSortCorrectly(string sortField)
    {
        // Arrange
        var query = new GetVehicleListQuery { SortBy = sortField };
        var vehicles = CreateTestVehicles();

        MockVehicleRepository.Setup(x => x.GetAllAsync(It.IsAny<CancellationToken>()))
            .ReturnsAsync(vehicles);

        // Act
        var result = await _handler.Handle(query, CreateCancellationToken());

        // Assert
        result.Should().NotBeNull();
        result.Success.Should().BeTrue();
        result.Vehicles.Should().NotBeEmpty();
        // Verify that sorting was applied (results should be ordered)
        result.Vehicles.Should().BeInAscendingOrder(v => GetPropertyValue(v, sortField));
    }

    private static List<Vehicle> CreateTestVehicles()
    {
        var vehicles = new List<Vehicle>
        {
            new("Vehicle A", "AAA-001", "Car", "Gasoline"),
            new("Vehicle B", "BBB-002", "Car", "Diesel"),
            new("Vehicle C", "CCC-003", "Truck", "Gasoline")
        };

        // Set up additional properties
        vehicles[0].UpdateBasicInfo("Vehicle A", "Toyota", "Camry", 2023, "Blue");
        vehicles[1].UpdateBasicInfo("Vehicle B", "Honda", "Civic", 2022, "Red");
        vehicles[2].UpdateBasicInfo("Vehicle C", "Ford", "F-150", 2021, "White");

        // Set maintenance status for one vehicle
        vehicles[2].ChangeStatus(VehicleStatus.Maintenance, "Scheduled maintenance", Guid.NewGuid());

        return vehicles;
    }

    private static object GetPropertyValue(object obj, string propertyName)
    {
        var property = obj.GetType().GetProperty(propertyName);
        return property?.GetValue(obj) ?? string.Empty;
    }
}
