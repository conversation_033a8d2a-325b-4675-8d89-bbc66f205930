using FleetXQ.Domain.Common;

namespace FleetXQ.Domain.ValueObjects;

/// <summary>
/// Represents a fuel level as a percentage with validation
/// </summary>
public sealed class FuelLevel : ValueObject
{
    /// <summary>
    /// Gets the fuel level as a percentage (0-100)
    /// </summary>
    public decimal Percentage { get; }

    /// <summary>
    /// Gets a value indicating whether the fuel level is low (below 20%)
    /// </summary>
    public bool IsLow => Percentage < 20;

    /// <summary>
    /// Gets a value indicating whether the fuel level is critical (below 10%)
    /// </summary>
    public bool IsCritical => Percentage < 10;

    /// <summary>
    /// Gets a value indicating whether the fuel level is empty (0%)
    /// </summary>
    public bool IsEmpty => Percentage == 0;

    /// <summary>
    /// Gets a value indicating whether the fuel level is full (100%)
    /// </summary>
    public bool IsFull => Percentage == 100;

    /// <summary>
    /// Initializes a new instance of the <see cref="FuelLevel"/> class
    /// </summary>
    /// <param name="percentage">The fuel level as a percentage (0-100)</param>
    /// <exception cref="ArgumentOutOfRangeException">Thrown when percentage is not between 0 and 100</exception>
    public FuelLevel(decimal percentage)
    {
        if (percentage < 0 || percentage > 100)
        {
            throw new ArgumentOutOfRangeException(nameof(percentage), percentage, "Fuel level percentage must be between 0 and 100");
        }

        Percentage = percentage;
    }

    /// <summary>
    /// Creates a FuelLevel from a double value
    /// </summary>
    /// <param name="percentage">The fuel level as a percentage</param>
    /// <returns>A new FuelLevel instance</returns>
    public static FuelLevel FromDouble(double percentage)
    {
        return new FuelLevel((decimal)percentage);
    }

    /// <summary>
    /// Creates a FuelLevel representing an empty tank
    /// </summary>
    /// <returns>A FuelLevel with 0% fuel</returns>
    public static FuelLevel Empty => new(0);

    /// <summary>
    /// Creates a FuelLevel representing a full tank
    /// </summary>
    /// <returns>A FuelLevel with 100% fuel</returns>
    public static FuelLevel Full => new(100);

    /// <summary>
    /// Gets the fuel level category based on the percentage
    /// </summary>
    /// <returns>The fuel level category</returns>
    public FuelLevelCategory GetCategory()
    {
        return Percentage switch
        {
            0 => FuelLevelCategory.Empty,
            < 10 => FuelLevelCategory.Critical,
            < 20 => FuelLevelCategory.Low,
            < 80 => FuelLevelCategory.Normal,
            _ => FuelLevelCategory.High
        };
    }

    /// <summary>
    /// Calculates the fuel amount in liters based on tank capacity
    /// </summary>
    /// <param name="tankCapacityLiters">The tank capacity in liters</param>
    /// <returns>The fuel amount in liters</returns>
    public decimal ToLiters(decimal tankCapacityLiters)
    {
        if (tankCapacityLiters < 0)
        {
            throw new ArgumentOutOfRangeException(nameof(tankCapacityLiters), tankCapacityLiters, "Tank capacity must be non-negative");
        }

        return (Percentage / 100) * tankCapacityLiters;
    }

    /// <summary>
    /// Creates a FuelLevel from liters and tank capacity
    /// </summary>
    /// <param name="liters">The fuel amount in liters</param>
    /// <param name="tankCapacityLiters">The tank capacity in liters</param>
    /// <returns>A new FuelLevel instance</returns>
    public static FuelLevel FromLiters(decimal liters, decimal tankCapacityLiters)
    {
        if (liters < 0)
        {
            throw new ArgumentOutOfRangeException(nameof(liters), liters, "Fuel amount must be non-negative");
        }

        if (tankCapacityLiters <= 0)
        {
            throw new ArgumentOutOfRangeException(nameof(tankCapacityLiters), tankCapacityLiters, "Tank capacity must be positive");
        }

        if (liters > tankCapacityLiters)
        {
            throw new ArgumentOutOfRangeException(nameof(liters), liters, "Fuel amount cannot exceed tank capacity");
        }

        var percentage = (liters / tankCapacityLiters) * 100;
        return new FuelLevel(percentage);
    }

    /// <summary>
    /// Gets the atomic values that define the value object's equality
    /// </summary>
    /// <returns>An enumerable of atomic values</returns>
    protected override IEnumerable<object> GetEqualityComponents()
    {
        yield return Percentage;
    }

    /// <summary>
    /// Returns a string representation of the fuel level
    /// </summary>
    /// <returns>A string representation of the fuel level percentage</returns>
    public override string ToString()
    {
        return $"{Percentage:F1}%";
    }

    /// <summary>
    /// Implicit conversion from decimal to FuelLevel
    /// </summary>
    /// <param name="percentage">The percentage value</param>
    /// <returns>A new FuelLevel instance</returns>
    public static implicit operator FuelLevel(decimal percentage)
    {
        return new FuelLevel(percentage);
    }

    /// <summary>
    /// Implicit conversion from FuelLevel to decimal
    /// </summary>
    /// <param name="fuelLevel">The FuelLevel instance</param>
    /// <returns>The percentage value</returns>
    public static implicit operator decimal(FuelLevel fuelLevel)
    {
        return fuelLevel.Percentage;
    }
}

/// <summary>
/// Represents the category of fuel level
/// </summary>
public enum FuelLevelCategory
{
    /// <summary>
    /// Empty tank (0%)
    /// </summary>
    Empty,

    /// <summary>
    /// Critical level (less than 10%)
    /// </summary>
    Critical,

    /// <summary>
    /// Low level (10-19%)
    /// </summary>
    Low,

    /// <summary>
    /// Normal level (20-79%)
    /// </summary>
    Normal,

    /// <summary>
    /// High level (80-100%)
    /// </summary>
    High
}
