using FleetXQ.Domain.Entities;
using FleetXQ.Domain.ValueObjects;

namespace FleetXQ.Domain.Services;

/// <summary>
/// Domain service for managing vehicle assignments to drivers
/// </summary>
public interface IVehicleAssignmentService
{
    /// <summary>
    /// Determines if a driver can be assigned to a vehicle
    /// </summary>
    /// <param name="driver">The driver to check</param>
    /// <param name="vehicle">The vehicle to check</param>
    /// <returns>Assignment validation result</returns>
    AssignmentValidationResult CanAssignDriverToVehicle(Driver driver, Vehicle vehicle);

    /// <summary>
    /// Finds the best available driver for a vehicle
    /// </summary>
    /// <param name="vehicle">The vehicle that needs a driver</param>
    /// <param name="availableDrivers">The list of available drivers</param>
    /// <param name="location">The current location (optional)</param>
    /// <returns>The best matching driver, or null if none suitable</returns>
    Driver? FindBestDriverForVehicle(Vehicle vehicle, IEnumerable<Driver> availableDrivers, Location? location = null);

    /// <summary>
    /// Finds the best available vehicle for a driver
    /// </summary>
    /// <param name="driver">The driver that needs a vehicle</param>
    /// <param name="availableVehicles">The list of available vehicles</param>
    /// <param name="location">The current location (optional)</param>
    /// <returns>The best matching vehicle, or null if none suitable</returns>
    Vehicle? FindBestVehicleForDriver(Driver driver, IEnumerable<Vehicle> availableVehicles, Location? location = null);

    /// <summary>
    /// Calculates the compatibility score between a driver and vehicle
    /// </summary>
    /// <param name="driver">The driver</param>
    /// <param name="vehicle">The vehicle</param>
    /// <returns>Compatibility score (0-100)</returns>
    int CalculateCompatibilityScore(Driver driver, Vehicle vehicle);

    /// <summary>
    /// Validates if an assignment change is allowed
    /// </summary>
    /// <param name="currentDriver">The current driver</param>
    /// <param name="newDriver">The new driver</param>
    /// <param name="vehicle">The vehicle</param>
    /// <param name="reason">The reason for the change</param>
    /// <returns>Validation result</returns>
    AssignmentValidationResult ValidateAssignmentChange(Driver? currentDriver, Driver newDriver, 
        Vehicle vehicle, string reason);

    /// <summary>
    /// Gets assignment recommendations for optimal fleet utilization
    /// </summary>
    /// <param name="drivers">Available drivers</param>
    /// <param name="vehicles">Available vehicles</param>
    /// <returns>List of assignment recommendations</returns>
    IEnumerable<AssignmentRecommendation> GetAssignmentRecommendations(
        IEnumerable<Driver> drivers, IEnumerable<Vehicle> vehicles);
}

/// <summary>
/// Represents the result of assignment validation
/// </summary>
public class AssignmentValidationResult
{
    /// <summary>
    /// Gets or sets a value indicating whether the assignment is valid
    /// </summary>
    public bool IsValid { get; set; }

    /// <summary>
    /// Gets or sets the validation messages
    /// </summary>
    public List<string> Messages { get; set; } = new();

    /// <summary>
    /// Gets or sets the validation warnings
    /// </summary>
    public List<string> Warnings { get; set; } = new();

    /// <summary>
    /// Gets or sets the compatibility score
    /// </summary>
    public int CompatibilityScore { get; set; }

    /// <summary>
    /// Creates a successful validation result
    /// </summary>
    /// <param name="compatibilityScore">The compatibility score</param>
    /// <returns>A successful validation result</returns>
    public static AssignmentValidationResult Success(int compatibilityScore = 100)
    {
        return new AssignmentValidationResult
        {
            IsValid = true,
            CompatibilityScore = compatibilityScore
        };
    }

    /// <summary>
    /// Creates a failed validation result
    /// </summary>
    /// <param name="message">The error message</param>
    /// <returns>A failed validation result</returns>
    public static AssignmentValidationResult Failure(string message)
    {
        return new AssignmentValidationResult
        {
            IsValid = false,
            Messages = { message }
        };
    }

    /// <summary>
    /// Adds a warning to the validation result
    /// </summary>
    /// <param name="warning">The warning message</param>
    public void AddWarning(string warning)
    {
        Warnings.Add(warning);
    }

    /// <summary>
    /// Adds an error message to the validation result
    /// </summary>
    /// <param name="message">The error message</param>
    public void AddError(string message)
    {
        Messages.Add(message);
        IsValid = false;
    }
}

/// <summary>
/// Represents an assignment recommendation
/// </summary>
public class AssignmentRecommendation
{
    /// <summary>
    /// Gets or sets the recommended driver
    /// </summary>
    public Driver Driver { get; set; } = null!;

    /// <summary>
    /// Gets or sets the recommended vehicle
    /// </summary>
    public Vehicle Vehicle { get; set; } = null!;

    /// <summary>
    /// Gets or sets the compatibility score
    /// </summary>
    public int CompatibilityScore { get; set; }

    /// <summary>
    /// Gets or sets the recommendation reason
    /// </summary>
    public string Reason { get; set; } = string.Empty;

    /// <summary>
    /// Gets or sets the priority level
    /// </summary>
    public RecommendationPriority Priority { get; set; }

    /// <summary>
    /// Gets or sets additional benefits of this assignment
    /// </summary>
    public List<string> Benefits { get; set; } = new();
}

/// <summary>
/// Represents the priority level of an assignment recommendation
/// </summary>
public enum RecommendationPriority
{
    /// <summary>
    /// Low priority recommendation
    /// </summary>
    Low,

    /// <summary>
    /// Medium priority recommendation
    /// </summary>
    Medium,

    /// <summary>
    /// High priority recommendation
    /// </summary>
    High,

    /// <summary>
    /// Critical priority recommendation
    /// </summary>
    Critical
}
