using FleetXQ.Application.Common.Validation;
using FluentValidation;

namespace FleetXQ.Application.Features.Vehicles.Commands.CreateVehicle;

/// <summary>
/// Validator for CreateVehicleCommand
/// </summary>
public sealed class CreateVehicleCommandValidator : AbstractValidator<CreateVehicleCommand>
{
    /// <summary>
    /// Initializes a new instance of the <see cref="CreateVehicleCommandValidator"/> class
    /// </summary>
    public CreateVehicleCommandValidator()
    {
        RuleFor(x => x.VehicleName)
            .NotEmpty()
            .WithMessage("Vehicle name is required")
            .MaximumLength(100)
            .WithMessage("Vehicle name cannot exceed 100 characters");

        RuleFor(x => x.LicensePlate)
            .NotEmpty()
            .WithMessage("License plate is required")
            .ValidLicensePlate();

        RuleFor(x => x.VehicleType)
            .NotEmpty()
            .WithMessage("Vehicle type is required")
            .MaximumLength(50)
            .WithMessage("Vehicle type cannot exceed 50 characters");

        RuleFor(x => x.VIN)
            .ValidVIN()
            .When(x => !string.IsNullOrWhiteSpace(x.VIN));

        RuleFor(x => x.Brand)
            .MaximumLength(50)
            .WithMessage("Brand cannot exceed 50 characters")
            .When(x => !string.IsNullOrWhiteSpace(x.Brand));

        RuleFor(x => x.Model)
            .MaximumLength(50)
            .WithMessage("Model cannot exceed 50 characters")
            .When(x => !string.IsNullOrWhiteSpace(x.Model));

        RuleFor(x => x.Year)
            .GreaterThan(1900)
            .WithMessage("Year must be greater than 1900")
            .LessThanOrEqualTo(DateTime.UtcNow.Year + 1)
            .WithMessage("Year cannot be more than one year in the future")
            .When(x => x.Year.HasValue);

        RuleFor(x => x.Color)
            .MaximumLength(30)
            .WithMessage("Color cannot exceed 30 characters")
            .When(x => !string.IsNullOrWhiteSpace(x.Color));

        RuleFor(x => x.FuelType)
            .NotEmpty()
            .WithMessage("Fuel type is required")
            .MaximumLength(20)
            .WithMessage("Fuel type cannot exceed 20 characters")
            .Must(BeValidFuelType)
            .WithMessage("Invalid fuel type. Valid types are: Gasoline, Diesel, Electric, Hybrid, CNG, LPG");

        RuleFor(x => x.FuelTankCapacity)
            .GreaterThan(0)
            .WithMessage("Fuel tank capacity must be greater than 0")
            .LessThanOrEqualTo(1000)
            .WithMessage("Fuel tank capacity cannot exceed 1000 liters")
            .When(x => x.FuelTankCapacity.HasValue);
    }

    /// <summary>
    /// Validates if the fuel type is valid
    /// </summary>
    /// <param name="fuelType">The fuel type to validate</param>
    /// <returns>True if valid, false otherwise</returns>
    private static bool BeValidFuelType(string fuelType)
    {
        var validFuelTypes = new[] { "Gasoline", "Diesel", "Electric", "Hybrid", "CNG", "LPG" };
        return validFuelTypes.Contains(fuelType, StringComparer.OrdinalIgnoreCase);
    }
}
