using FleetXQ.Domain.Common;
using FleetXQ.Domain.Enums;
using FleetXQ.Domain.ValueObjects;

namespace FleetXQ.Domain.Entities;

/// <summary>
/// Represents a trip in the fleet management system
/// </summary>
public class Trip : BaseEntity
{
    /// <summary>
    /// Gets or sets the vehicle ID
    /// </summary>
    public Guid VehicleId { get; private set; }

    /// <summary>
    /// Gets or sets the driver ID
    /// </summary>
    public Guid? DriverId { get; private set; }

    /// <summary>
    /// Gets or sets the trip start time
    /// </summary>
    public DateTime StartTime { get; private set; }

    /// <summary>
    /// Gets or sets the trip end time
    /// </summary>
    public DateTime? EndTime { get; private set; }

    /// <summary>
    /// Gets or sets the start location
    /// </summary>
    public Location? StartLocation { get; private set; }

    /// <summary>
    /// Gets or sets the end location
    /// </summary>
    public Location? EndLocation { get; private set; }

    /// <summary>
    /// Gets or sets the start address
    /// </summary>
    public string? StartAddress { get; private set; }

    /// <summary>
    /// Gets or sets the end address
    /// </summary>
    public string? EndAddress { get; private set; }

    /// <summary>
    /// Gets or sets the total distance traveled in kilometers
    /// </summary>
    public decimal TotalDistance { get; private set; }

    /// <summary>
    /// Gets or sets the total duration in minutes
    /// </summary>
    public int? TotalDuration { get; private set; }

    /// <summary>
    /// Gets or sets the maximum speed reached during the trip
    /// </summary>
    public Speed? MaxSpeed { get; private set; }

    /// <summary>
    /// Gets or sets the average speed during the trip
    /// </summary>
    public Speed? AvgSpeed { get; private set; }

    /// <summary>
    /// Gets or sets the fuel consumed during the trip in liters
    /// </summary>
    public decimal? FuelConsumed { get; private set; }

    /// <summary>
    /// Gets or sets the idle time in minutes
    /// </summary>
    public int IdleTime { get; private set; }

    /// <summary>
    /// Gets or sets the number of harsh events during the trip
    /// </summary>
    public int HarshEvents { get; private set; }

    /// <summary>
    /// Gets or sets the trip status
    /// </summary>
    public TripStatus Status { get; private set; }

    /// <summary>
    /// Gets or sets the planned route information
    /// </summary>
    public string? PlannedRoute { get; private set; }

    /// <summary>
    /// Gets or sets the actual route taken
    /// </summary>
    public string? ActualRoute { get; private set; }

    /// <summary>
    /// Gets or sets the trip purpose or description
    /// </summary>
    public string? Purpose { get; private set; }

    /// <summary>
    /// Gets or sets additional notes about the trip
    /// </summary>
    public string? Notes { get; private set; }

    /// <summary>
    /// Gets a value indicating whether the trip is currently active
    /// </summary>
    public bool IsActive => Status == TripStatus.Active;

    /// <summary>
    /// Gets a value indicating whether the trip is completed
    /// </summary>
    public bool IsCompleted => Status == TripStatus.Completed;

    /// <summary>
    /// Gets the trip duration in hours
    /// </summary>
    public double? DurationHours => TotalDuration.HasValue ? TotalDuration.Value / 60.0 : null;

    /// <summary>
    /// Gets the fuel efficiency in liters per 100km
    /// </summary>
    public decimal? FuelEfficiency => FuelConsumed.HasValue && TotalDistance > 0 
        ? (FuelConsumed.Value / TotalDistance) * 100 
        : null;

    /// <summary>
    /// Gets the moving time in minutes (total duration minus idle time)
    /// </summary>
    public int? MovingTime => TotalDuration.HasValue ? Math.Max(0, TotalDuration.Value - IdleTime) : null;

    /// <summary>
    /// Gets the idle time percentage
    /// </summary>
    public double? IdleTimePercentage => TotalDuration.HasValue && TotalDuration.Value > 0 
        ? (IdleTime / (double)TotalDuration.Value) * 100 
        : null;

    /// <summary>
    /// Gets the straight-line distance between start and end locations
    /// </summary>
    public double? StraightLineDistance => StartLocation != null && EndLocation != null 
        ? StartLocation.DistanceTo(EndLocation) 
        : null;

    /// <summary>
    /// Gets the route efficiency (straight line distance / actual distance)
    /// </summary>
    public double? RouteEfficiency => StraightLineDistance.HasValue && TotalDistance > 0 
        ? StraightLineDistance.Value / (double)TotalDistance 
        : null;

    /// <summary>
    /// Private constructor for Entity Framework
    /// </summary>
    private Trip() { }

    /// <summary>
    /// Initializes a new instance of the <see cref="Trip"/> class
    /// </summary>
    /// <param name="vehicleId">The vehicle ID</param>
    /// <param name="startTime">The trip start time</param>
    /// <param name="driverId">The driver ID (optional)</param>
    /// <param name="startLocation">The start location (optional)</param>
    /// <param name="purpose">The trip purpose (optional)</param>
    public Trip(Guid vehicleId, DateTime startTime, Guid? driverId = null, Location? startLocation = null, string? purpose = null)
    {
        if (vehicleId == Guid.Empty)
            throw new ArgumentException("Vehicle ID cannot be empty", nameof(vehicleId));

        if (startTime > DateTime.UtcNow)
            throw new ArgumentException("Start time cannot be in the future", nameof(startTime));

        VehicleId = vehicleId;
        DriverId = driverId;
        StartTime = startTime;
        StartLocation = startLocation;
        Purpose = purpose;
        Status = TripStatus.Active;
        TotalDistance = 0;
        IdleTime = 0;
        HarshEvents = 0;
    }

    /// <summary>
    /// Updates the trip's start information
    /// </summary>
    /// <param name="startLocation">The start location</param>
    /// <param name="startAddress">The start address</param>
    public void UpdateStartInfo(Location startLocation, string? startAddress = null)
    {
        if (Status != TripStatus.Active)
            throw new InvalidOperationException("Can only update start info for active trips");

        StartLocation = startLocation ?? throw new ArgumentNullException(nameof(startLocation));
        StartAddress = startAddress;
    }

    /// <summary>
    /// Updates the trip's planned route
    /// </summary>
    /// <param name="plannedRoute">The planned route information</param>
    public void UpdatePlannedRoute(string plannedRoute)
    {
        if (Status == TripStatus.Completed)
            throw new InvalidOperationException("Cannot update planned route for completed trips");

        PlannedRoute = plannedRoute;
    }

    /// <summary>
    /// Records a harsh driving event during the trip
    /// </summary>
    /// <param name="eventType">The type of harsh event</param>
    public void RecordHarshEvent(string eventType)
    {
        if (Status != TripStatus.Active)
            throw new InvalidOperationException("Can only record harsh events for active trips");

        HarshEvents++;
    }

    /// <summary>
    /// Updates the trip's real-time statistics
    /// </summary>
    /// <param name="currentDistance">The current total distance</param>
    /// <param name="currentSpeed">The current speed</param>
    /// <param name="isIdle">Whether the vehicle is currently idle</param>
    /// <param name="idleTimeIncrement">Additional idle time in minutes</param>
    public void UpdateRealTimeStats(decimal currentDistance, Speed currentSpeed, bool isIdle = false, int idleTimeIncrement = 0)
    {
        if (Status != TripStatus.Active)
            throw new InvalidOperationException("Can only update stats for active trips");

        if (currentDistance >= TotalDistance)
        {
            TotalDistance = currentDistance;
        }

        if (MaxSpeed == null || currentSpeed > MaxSpeed)
        {
            MaxSpeed = currentSpeed;
        }

        if (isIdle && idleTimeIncrement > 0)
        {
            IdleTime += idleTimeIncrement;
        }
    }

    /// <summary>
    /// Completes the trip
    /// </summary>
    /// <param name="endTime">The trip end time</param>
    /// <param name="endLocation">The end location</param>
    /// <param name="endAddress">The end address</param>
    /// <param name="fuelConsumed">The fuel consumed during the trip</param>
    /// <param name="actualRoute">The actual route taken</param>
    public void Complete(DateTime endTime, Location? endLocation = null, string? endAddress = null, 
        decimal? fuelConsumed = null, string? actualRoute = null)
    {
        if (Status != TripStatus.Active)
            throw new InvalidOperationException("Only active trips can be completed");

        if (endTime < StartTime)
            throw new ArgumentException("End time cannot be before start time", nameof(endTime));

        EndTime = endTime;
        EndLocation = endLocation;
        EndAddress = endAddress;
        FuelConsumed = fuelConsumed;
        ActualRoute = actualRoute;
        Status = TripStatus.Completed;

        // Calculate total duration
        TotalDuration = (int)(endTime - StartTime).TotalMinutes;

        // Calculate average speed
        if (TotalDistance > 0 && TotalDuration > 0)
        {
            var movingTimeHours = (TotalDuration.Value - IdleTime) / 60.0;
            if (movingTimeHours > 0)
            {
                AvgSpeed = Speed.FromKilometersPerHour((decimal)(TotalDistance / (decimal)movingTimeHours));
            }
        }
    }

    /// <summary>
    /// Cancels the trip
    /// </summary>
    /// <param name="reason">The reason for cancellation</param>
    public void Cancel(string? reason = null)
    {
        if (Status == TripStatus.Completed)
            throw new InvalidOperationException("Cannot cancel completed trips");

        Status = TripStatus.Cancelled;
        Notes = string.IsNullOrWhiteSpace(Notes) ? reason : $"{Notes}; Cancelled: {reason}";
    }

    /// <summary>
    /// Adds notes to the trip
    /// </summary>
    /// <param name="notes">The notes to add</param>
    public void AddNotes(string notes)
    {
        if (string.IsNullOrWhiteSpace(notes))
            return;

        Notes = string.IsNullOrWhiteSpace(Notes) ? notes : $"{Notes}; {notes}";
    }

    /// <summary>
    /// Assigns a driver to the trip
    /// </summary>
    /// <param name="driverId">The driver ID</param>
    public void AssignDriver(Guid driverId)
    {
        if (driverId == Guid.Empty)
            throw new ArgumentException("Driver ID cannot be empty", nameof(driverId));

        if (Status == TripStatus.Completed)
            throw new InvalidOperationException("Cannot assign driver to completed trips");

        DriverId = driverId;
    }

    /// <summary>
    /// Calculates the trip score based on various factors
    /// </summary>
    /// <returns>A score between 0 and 100</returns>
    public int CalculateTripScore()
    {
        if (!IsCompleted)
            return 0;

        var score = 100;

        // Deduct points for harsh events
        score -= HarshEvents * 5;

        // Deduct points for excessive idle time
        if (IdleTimePercentage > 20)
        {
            score -= (int)((IdleTimePercentage.Value - 20) * 2);
        }

        // Deduct points for poor fuel efficiency (if available)
        if (FuelEfficiency.HasValue && FuelEfficiency.Value > 15) // Assuming 15L/100km as threshold
        {
            score -= (int)((FuelEfficiency.Value - 15) * 2);
        }

        // Deduct points for poor route efficiency
        if (RouteEfficiency.HasValue && RouteEfficiency.Value < 0.8)
        {
            score -= (int)((0.8 - RouteEfficiency.Value) * 50);
        }

        return Math.Max(0, Math.Min(100, score));
    }

    /// <summary>
    /// Gets trip analytics summary
    /// </summary>
    /// <returns>A summary of trip analytics</returns>
    public TripAnalytics GetAnalytics()
    {
        return new TripAnalytics
        {
            TripId = Id,
            Duration = DurationHours,
            Distance = TotalDistance,
            FuelEfficiency = FuelEfficiency,
            IdleTimePercentage = IdleTimePercentage,
            RouteEfficiency = RouteEfficiency,
            HarshEventsCount = HarshEvents,
            MaxSpeed = MaxSpeed?.KilometersPerHour,
            AvgSpeed = AvgSpeed?.KilometersPerHour,
            Score = CalculateTripScore()
        };
    }
}

/// <summary>
/// Represents trip analytics data
/// </summary>
public class TripAnalytics
{
    public Guid TripId { get; set; }
    public double? Duration { get; set; }
    public decimal Distance { get; set; }
    public decimal? FuelEfficiency { get; set; }
    public double? IdleTimePercentage { get; set; }
    public double? RouteEfficiency { get; set; }
    public int HarshEventsCount { get; set; }
    public decimal? MaxSpeed { get; set; }
    public decimal? AvgSpeed { get; set; }
    public int Score { get; set; }
}
