using FleetXQ.Application.Features.Vehicles.DTOs;

namespace FleetXQ.Application.Features.Telemetry.DTOs;

/// <summary>
/// Data transfer object for telemetry information
/// </summary>
public class TelemetryDto
{
    /// <summary>
    /// Gets or sets the vehicle ID
    /// </summary>
    public Guid VehicleId { get; set; }

    /// <summary>
    /// Gets or sets the vehicle name
    /// </summary>
    public string VehicleName { get; set; } = string.Empty;

    /// <summary>
    /// Gets or sets the license plate
    /// </summary>
    public string LicensePlate { get; set; } = string.Empty;

    /// <summary>
    /// Gets or sets the current location
    /// </summary>
    public LocationDto Location { get; set; } = null!;

    /// <summary>
    /// Gets or sets the speed in km/h
    /// </summary>
    public decimal SpeedKmh { get; set; }

    /// <summary>
    /// Gets or sets the speed in mph
    /// </summary>
    public decimal SpeedMph { get; set; }

    /// <summary>
    /// Gets or sets the fuel level percentage
    /// </summary>
    public decimal? FuelLevelPercentage { get; set; }

    /// <summary>
    /// Gets or sets the current mileage
    /// </summary>
    public decimal CurrentMileage { get; set; }

    /// <summary>
    /// Gets or sets a value indicating whether the vehicle is moving
    /// </summary>
    public bool IsMoving { get; set; }

    /// <summary>
    /// Gets or sets the timestamp when the telemetry was recorded
    /// </summary>
    public DateTime Timestamp { get; set; }

    /// <summary>
    /// Gets or sets the engine temperature in Celsius
    /// </summary>
    public decimal? EngineTemperature { get; set; }

    /// <summary>
    /// Gets or sets the RPM
    /// </summary>
    public int? RPM { get; set; }

    /// <summary>
    /// Gets or sets additional telemetry data
    /// </summary>
    public Dictionary<string, object>? AdditionalData { get; set; }
}

/// <summary>
/// Data transfer object for telemetry history information
/// </summary>
public class TelemetryHistoryDto
{
    /// <summary>
    /// Gets or sets the vehicle ID
    /// </summary>
    public Guid VehicleId { get; set; }

    /// <summary>
    /// Gets or sets the telemetry records
    /// </summary>
    public IEnumerable<TelemetryRecordDto> Records { get; set; } = new List<TelemetryRecordDto>();

    /// <summary>
    /// Gets or sets the total count of records
    /// </summary>
    public int TotalCount { get; set; }

    /// <summary>
    /// Gets or sets the start date of the query
    /// </summary>
    public DateTime StartDate { get; set; }

    /// <summary>
    /// Gets or sets the end date of the query
    /// </summary>
    public DateTime EndDate { get; set; }
}

/// <summary>
/// Data transfer object for individual telemetry record
/// </summary>
public class TelemetryRecordDto
{
    /// <summary>
    /// Gets or sets the location
    /// </summary>
    public LocationDto Location { get; set; } = null!;

    /// <summary>
    /// Gets or sets the speed in km/h
    /// </summary>
    public decimal SpeedKmh { get; set; }

    /// <summary>
    /// Gets or sets the fuel level percentage
    /// </summary>
    public decimal? FuelLevelPercentage { get; set; }

    /// <summary>
    /// Gets or sets the mileage at this point
    /// </summary>
    public decimal? Mileage { get; set; }

    /// <summary>
    /// Gets or sets the timestamp
    /// </summary>
    public DateTime Timestamp { get; set; }

    /// <summary>
    /// Gets or sets the engine temperature
    /// </summary>
    public decimal? EngineTemperature { get; set; }

    /// <summary>
    /// Gets or sets the RPM
    /// </summary>
    public int? RPM { get; set; }
}
