using MediatR;

namespace FleetXQ.Application.Features.Vehicles.Commands.CreateVehicle;

/// <summary>
/// Command to create a new vehicle
/// </summary>
public sealed class CreateVehicleCommand : IRequest<CreateVehicleResult>
{
    /// <summary>
    /// Gets or sets the vehicle name
    /// </summary>
    public string VehicleName { get; set; } = string.Empty;

    /// <summary>
    /// Gets or sets the license plate number
    /// </summary>
    public string LicensePlate { get; set; } = string.Empty;

    /// <summary>
    /// Gets or sets the vehicle type
    /// </summary>
    public string VehicleType { get; set; } = string.Empty;

    /// <summary>
    /// Gets or sets the Vehicle Identification Number (VIN)
    /// </summary>
    public string? VIN { get; set; }

    /// <summary>
    /// Gets or sets the vehicle brand
    /// </summary>
    public string? Brand { get; set; }

    /// <summary>
    /// Gets or sets the vehicle model
    /// </summary>
    public string? Model { get; set; }

    /// <summary>
    /// Gets or sets the vehicle year
    /// </summary>
    public int? Year { get; set; }

    /// <summary>
    /// Gets or sets the vehicle color
    /// </summary>
    public string? Color { get; set; }

    /// <summary>
    /// Gets or sets the fuel type
    /// </summary>
    public string FuelType { get; set; } = "Gasoline";

    /// <summary>
    /// Gets or sets the fuel tank capacity in liters
    /// </summary>
    public decimal? FuelTankCapacity { get; set; }
}

/// <summary>
/// Result of creating a vehicle
/// </summary>
public sealed class CreateVehicleResult
{
    /// <summary>
    /// Gets or sets the created vehicle ID
    /// </summary>
    public Guid VehicleId { get; set; }

    /// <summary>
    /// Gets or sets a value indicating whether the operation was successful
    /// </summary>
    public bool Success { get; set; }

    /// <summary>
    /// Gets or sets the error message if the operation failed
    /// </summary>
    public string? ErrorMessage { get; set; }

    /// <summary>
    /// Creates a successful result
    /// </summary>
    /// <param name="vehicleId">The created vehicle ID</param>
    /// <returns>A successful result</returns>
    public static CreateVehicleResult Successful(Guid vehicleId)
    {
        return new CreateVehicleResult
        {
            VehicleId = vehicleId,
            Success = true
        };
    }

    /// <summary>
    /// Creates a failed result
    /// </summary>
    /// <param name="errorMessage">The error message</param>
    /// <returns>A failed result</returns>
    public static CreateVehicleResult Failed(string errorMessage)
    {
        return new CreateVehicleResult
        {
            Success = false,
            ErrorMessage = errorMessage
        };
    }
}
