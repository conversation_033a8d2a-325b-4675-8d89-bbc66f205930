using FleetXQ.Domain.Enums;

namespace FleetXQ.Application.Features.Drivers.DTOs;

/// <summary>
/// Data transfer object for driver information
/// </summary>
public class DriverDto
{
    /// <summary>
    /// Gets or sets the driver ID
    /// </summary>
    public Guid Id { get; set; }

    /// <summary>
    /// Gets or sets the employee ID
    /// </summary>
    public string EmployeeId { get; set; } = string.Empty;

    /// <summary>
    /// Gets or sets the first name
    /// </summary>
    public string FirstName { get; set; } = string.Empty;

    /// <summary>
    /// Gets or sets the last name
    /// </summary>
    public string LastName { get; set; } = string.Empty;

    /// <summary>
    /// Gets or sets the full name
    /// </summary>
    public string FullName { get; set; } = string.Empty;

    /// <summary>
    /// Gets or sets the email address
    /// </summary>
    public string? Email { get; set; }

    /// <summary>
    /// Gets or sets the phone number
    /// </summary>
    public string? PhoneNumber { get; set; }

    /// <summary>
    /// Gets or sets the license number
    /// </summary>
    public string? LicenseNumber { get; set; }

    /// <summary>
    /// Gets or sets the license class
    /// </summary>
    public string? LicenseClass { get; set; }

    /// <summary>
    /// Gets or sets the license expiry date
    /// </summary>
    public DateTime? LicenseExpiryDate { get; set; }

    /// <summary>
    /// Gets or sets the hire date
    /// </summary>
    public DateTime? HireDate { get; set; }

    /// <summary>
    /// Gets or sets the driver status
    /// </summary>
    public DriverStatus Status { get; set; }

    /// <summary>
    /// Gets or sets the currently assigned vehicle ID
    /// </summary>
    public Guid? AssignedVehicleId { get; set; }

    /// <summary>
    /// Gets or sets the currently assigned vehicle name
    /// </summary>
    public string? AssignedVehicleName { get; set; }

    /// <summary>
    /// Gets or sets a value indicating whether the driver is available
    /// </summary>
    public bool IsAvailable { get; set; }

    /// <summary>
    /// Gets or sets a value indicating whether the license is valid
    /// </summary>
    public bool IsLicenseValid { get; set; }

    /// <summary>
    /// Gets or sets the date when the driver was created
    /// </summary>
    public DateTime CreatedAt { get; set; }

    /// <summary>
    /// Gets or sets the date when the driver was last updated
    /// </summary>
    public DateTime UpdatedAt { get; set; }
}

/// <summary>
/// Data transfer object for driver list information (summary view)
/// </summary>
public class DriverListDto
{
    /// <summary>
    /// Gets or sets the driver ID
    /// </summary>
    public Guid Id { get; set; }

    /// <summary>
    /// Gets or sets the employee ID
    /// </summary>
    public string EmployeeId { get; set; } = string.Empty;

    /// <summary>
    /// Gets or sets the full name
    /// </summary>
    public string FullName { get; set; } = string.Empty;

    /// <summary>
    /// Gets or sets the driver status
    /// </summary>
    public DriverStatus Status { get; set; }

    /// <summary>
    /// Gets or sets the currently assigned vehicle name
    /// </summary>
    public string? AssignedVehicleName { get; set; }

    /// <summary>
    /// Gets or sets a value indicating whether the driver is available
    /// </summary>
    public bool IsAvailable { get; set; }

    /// <summary>
    /// Gets or sets a value indicating whether the license is valid
    /// </summary>
    public bool IsLicenseValid { get; set; }
}
