using FleetXQ.Domain.Entities;
using FleetXQ.Domain.Enums;

namespace FleetXQ.Domain.Interfaces;

/// <summary>
/// Repository interface for User aggregate root
/// </summary>
public interface IUserRepository : IRepository<User>
{
    /// <summary>
    /// Gets a user by their username
    /// </summary>
    /// <param name="username">The username</param>
    /// <param name="cancellationToken">The cancellation token</param>
    /// <returns>The user if found, null otherwise</returns>
    Task<User?> GetByUsernameAsync(string username, CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets a user by their email address
    /// </summary>
    /// <param name="email">The email address</param>
    /// <param name="cancellationToken">The cancellation token</param>
    /// <returns>The user if found, null otherwise</returns>
    Task<User?> GetByEmailAsync(string email, CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets users by their role
    /// </summary>
    /// <param name="role">The user role</param>
    /// <param name="cancellationToken">The cancellation token</param>
    /// <returns>A collection of users with the specified role</returns>
    Task<IEnumerable<User>> GetByRoleAsync(UserRole role, CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets active users
    /// </summary>
    /// <param name="cancellationToken">The cancellation token</param>
    /// <returns>A collection of active users</returns>
    Task<IEnumerable<User>> GetActiveUsersAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets inactive users
    /// </summary>
    /// <param name="cancellationToken">The cancellation token</param>
    /// <returns>A collection of inactive users</returns>
    Task<IEnumerable<User>> GetInactiveUsersAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets locked out users
    /// </summary>
    /// <param name="cancellationToken">The cancellation token</param>
    /// <returns>A collection of locked out users</returns>
    Task<IEnumerable<User>> GetLockedOutUsersAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets users with unconfirmed emails
    /// </summary>
    /// <param name="cancellationToken">The cancellation token</param>
    /// <returns>A collection of users with unconfirmed emails</returns>
    Task<IEnumerable<User>> GetUsersWithUnconfirmedEmailsAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets users who haven't logged in within the specified days
    /// </summary>
    /// <param name="daysThreshold">The number of days threshold</param>
    /// <param name="cancellationToken">The cancellation token</param>
    /// <returns>A collection of users with stale logins</returns>
    Task<IEnumerable<User>> GetUsersWithStaleLoginsAsync(int daysThreshold = 90, 
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets users created within a date range
    /// </summary>
    /// <param name="fromDate">The start date</param>
    /// <param name="toDate">The end date</param>
    /// <param name="cancellationToken">The cancellation token</param>
    /// <returns>A collection of users created within the date range</returns>
    Task<IEnumerable<User>> GetUsersCreatedBetweenAsync(DateTime fromDate, DateTime toDate, 
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets users who logged in within a date range
    /// </summary>
    /// <param name="fromDate">The start date</param>
    /// <param name="toDate">The end date</param>
    /// <param name="cancellationToken">The cancellation token</param>
    /// <returns>A collection of users who logged in within the date range</returns>
    Task<IEnumerable<User>> GetUsersLoggedInBetweenAsync(DateTime fromDate, DateTime toDate, 
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets user statistics by role
    /// </summary>
    /// <param name="cancellationToken">The cancellation token</param>
    /// <returns>A dictionary with role counts</returns>
    Task<Dictionary<UserRole, int>> GetUserStatisticsByRoleAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets user activity statistics
    /// </summary>
    /// <param name="cancellationToken">The cancellation token</param>
    /// <returns>User activity statistics</returns>
    Task<UserActivityStatistics> GetUserActivityStatisticsAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// Searches users by multiple criteria
    /// </summary>
    /// <param name="searchCriteria">The search criteria</param>
    /// <param name="cancellationToken">The cancellation token</param>
    /// <returns>A collection of users matching the criteria</returns>
    Task<IEnumerable<User>> SearchUsersAsync(UserSearchCriteria searchCriteria, 
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Checks if a username is already in use
    /// </summary>
    /// <param name="username">The username to check</param>
    /// <param name="excludeUserId">User ID to exclude from the check</param>
    /// <param name="cancellationToken">The cancellation token</param>
    /// <returns>True if the username is in use, false otherwise</returns>
    Task<bool> IsUsernameInUseAsync(string username, Guid? excludeUserId = null, 
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Checks if an email address is already in use
    /// </summary>
    /// <param name="email">The email address to check</param>
    /// <param name="excludeUserId">User ID to exclude from the check</param>
    /// <param name="cancellationToken">The cancellation token</param>
    /// <returns>True if the email is in use, false otherwise</returns>
    Task<bool> IsEmailInUseAsync(string email, Guid? excludeUserId = null, 
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets a user by their password reset token
    /// </summary>
    /// <param name="token">The password reset token</param>
    /// <param name="cancellationToken">The cancellation token</param>
    /// <returns>The user if found and token is valid, null otherwise</returns>
    Task<User?> GetByPasswordResetTokenAsync(string token, CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets a user by their email confirmation token
    /// </summary>
    /// <param name="token">The email confirmation token</param>
    /// <param name="cancellationToken">The cancellation token</param>
    /// <returns>The user if found, null otherwise</returns>
    Task<User?> GetByEmailConfirmationTokenAsync(string token, CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets users with failed login attempts above threshold
    /// </summary>
    /// <param name="attemptsThreshold">The failed attempts threshold</param>
    /// <param name="cancellationToken">The cancellation token</param>
    /// <returns>A collection of users with high failed login attempts</returns>
    Task<IEnumerable<User>> GetUsersWithHighFailedAttemptsAsync(int attemptsThreshold = 3, 
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets administrators
    /// </summary>
    /// <param name="cancellationToken">The cancellation token</param>
    /// <returns>A collection of administrator users</returns>
    Task<IEnumerable<User>> GetAdministratorsAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets managers
    /// </summary>
    /// <param name="cancellationToken">The cancellation token</param>
    /// <returns>A collection of manager users</returns>
    Task<IEnumerable<User>> GetManagersAsync(CancellationToken cancellationToken = default);
}

/// <summary>
/// Represents search criteria for users
/// </summary>
public class UserSearchCriteria
{
    /// <summary>
    /// Gets or sets the search term for username, email, or name
    /// </summary>
    public string? SearchTerm { get; set; }

    /// <summary>
    /// Gets or sets the user role filter
    /// </summary>
    public UserRole? Role { get; set; }

    /// <summary>
    /// Gets or sets the active status filter
    /// </summary>
    public bool? IsActive { get; set; }

    /// <summary>
    /// Gets or sets the email confirmed status filter
    /// </summary>
    public bool? IsEmailConfirmed { get; set; }

    /// <summary>
    /// Gets or sets the locked out status filter
    /// </summary>
    public bool? IsLockedOut { get; set; }

    /// <summary>
    /// Gets or sets the created date from filter
    /// </summary>
    public DateTime? CreatedFrom { get; set; }

    /// <summary>
    /// Gets or sets the created date to filter
    /// </summary>
    public DateTime? CreatedTo { get; set; }

    /// <summary>
    /// Gets or sets the last login date from filter
    /// </summary>
    public DateTime? LastLoginFrom { get; set; }

    /// <summary>
    /// Gets or sets the last login date to filter
    /// </summary>
    public DateTime? LastLoginTo { get; set; }

    /// <summary>
    /// Gets or sets the minimum failed login attempts filter
    /// </summary>
    public int? MinFailedAttempts { get; set; }
}

/// <summary>
/// Represents user activity statistics
/// </summary>
public class UserActivityStatistics
{
    /// <summary>
    /// Gets or sets the total number of users
    /// </summary>
    public int TotalUsers { get; set; }

    /// <summary>
    /// Gets or sets the number of active users
    /// </summary>
    public int ActiveUsers { get; set; }

    /// <summary>
    /// Gets or sets the number of inactive users
    /// </summary>
    public int InactiveUsers { get; set; }

    /// <summary>
    /// Gets or sets the number of locked out users
    /// </summary>
    public int LockedOutUsers { get; set; }

    /// <summary>
    /// Gets or sets the number of users with unconfirmed emails
    /// </summary>
    public int UnconfirmedEmailUsers { get; set; }

    /// <summary>
    /// Gets or sets the number of users who logged in today
    /// </summary>
    public int LoggedInToday { get; set; }

    /// <summary>
    /// Gets or sets the number of users who logged in this week
    /// </summary>
    public int LoggedInThisWeek { get; set; }

    /// <summary>
    /// Gets or sets the number of users who logged in this month
    /// </summary>
    public int LoggedInThisMonth { get; set; }

    /// <summary>
    /// Gets or sets the number of new users this month
    /// </summary>
    public int NewUsersThisMonth { get; set; }
}
