using FleetXQ.Domain.Enums;
using FluentValidation;

namespace FleetXQ.Application.Features.Vehicles.Commands.UpdateVehicleStatus;

/// <summary>
/// Validator for UpdateVehicleStatusCommand
/// </summary>
public sealed class UpdateVehicleStatusCommandValidator : AbstractValidator<UpdateVehicleStatusCommand>
{
    /// <summary>
    /// Initializes a new instance of the <see cref="UpdateVehicleStatusCommandValidator"/> class
    /// </summary>
    public UpdateVehicleStatusCommandValidator()
    {
        RuleFor(x => x.VehicleId)
            .NotEmpty()
            .WithMessage("Vehicle ID is required");

        RuleFor(x => x.Status)
            .IsInEnum()
            .WithMessage("Invalid vehicle status");

        RuleFor(x => x.Reason)
            .MaximumLength(500)
            .WithMessage("Reason cannot exceed 500 characters")
            .When(x => !string.IsNullOrWhiteSpace(x.Reason));

        RuleFor(x => x.Context)
            .MaximumLength(1000)
            .WithMessage("Context cannot exceed 1000 characters")
            .When(x => !string.IsNullOrWhiteSpace(x.Context));

        // Business rule: Reason is required when changing to certain statuses
        RuleFor(x => x.Reason)
            .NotEmpty()
            .WithMessage("Reason is required when changing status to Maintenance, Offline, or Retired")
            .When(x => x.Status == VehicleStatus.Maintenance || 
                      x.Status == VehicleStatus.Offline || 
                      x.Status == VehicleStatus.Retired);
    }
}
