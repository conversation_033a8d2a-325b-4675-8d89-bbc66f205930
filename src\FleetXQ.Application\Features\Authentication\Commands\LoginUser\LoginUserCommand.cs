using MediatR;

namespace FleetXQ.Application.Features.Authentication.Commands.LoginUser;

/// <summary>
/// Command to authenticate a user
/// </summary>
public sealed class LoginUserCommand : IRequest<LoginUserResult>
{
    /// <summary>
    /// Gets or sets the username or email
    /// </summary>
    public string UsernameOrEmail { get; set; } = string.Empty;

    /// <summary>
    /// Gets or sets the password
    /// </summary>
    public string Password { get; set; } = string.Empty;

    /// <summary>
    /// Gets or sets a value indicating whether to remember the user
    /// </summary>
    public bool RememberMe { get; set; } = false;

    /// <summary>
    /// Gets or sets the client IP address
    /// </summary>
    public string? ClientIpAddress { get; set; }

    /// <summary>
    /// Gets or sets the user agent
    /// </summary>
    public string? UserAgent { get; set; }
}

/// <summary>
/// Result of user login
/// </summary>
public sealed class LoginUserResult
{
    /// <summary>
    /// Gets or sets a value indicating whether the login was successful
    /// </summary>
    public bool Success { get; set; }

    /// <summary>
    /// Gets or sets the error message if login failed
    /// </summary>
    public string? ErrorMessage { get; set; }

    /// <summary>
    /// Gets or sets the access token
    /// </summary>
    public string? AccessToken { get; set; }

    /// <summary>
    /// Gets or sets the refresh token
    /// </summary>
    public string? RefreshToken { get; set; }

    /// <summary>
    /// Gets or sets the token expiry time
    /// </summary>
    public DateTime? TokenExpiry { get; set; }

    /// <summary>
    /// Gets or sets the user information
    /// </summary>
    public UserInfo? User { get; set; }

    /// <summary>
    /// Gets or sets a value indicating whether the account is locked
    /// </summary>
    public bool IsAccountLocked { get; set; }

    /// <summary>
    /// Gets or sets the lockout end time
    /// </summary>
    public DateTime? LockoutEnd { get; set; }

    /// <summary>
    /// Gets or sets the remaining failed attempts before lockout
    /// </summary>
    public int? RemainingAttempts { get; set; }

    /// <summary>
    /// Creates a successful login result
    /// </summary>
    /// <param name="accessToken">The access token</param>
    /// <param name="refreshToken">The refresh token</param>
    /// <param name="tokenExpiry">The token expiry</param>
    /// <param name="user">The user information</param>
    /// <returns>A successful result</returns>
    public static LoginUserResult Successful(string accessToken, string refreshToken, DateTime tokenExpiry, UserInfo user)
    {
        return new LoginUserResult
        {
            Success = true,
            AccessToken = accessToken,
            RefreshToken = refreshToken,
            TokenExpiry = tokenExpiry,
            User = user
        };
    }

    /// <summary>
    /// Creates a failed login result
    /// </summary>
    /// <param name="errorMessage">The error message</param>
    /// <param name="remainingAttempts">The remaining attempts</param>
    /// <returns>A failed result</returns>
    public static LoginUserResult Failed(string errorMessage, int? remainingAttempts = null)
    {
        return new LoginUserResult
        {
            Success = false,
            ErrorMessage = errorMessage,
            RemainingAttempts = remainingAttempts
        };
    }

    /// <summary>
    /// Creates a locked account result
    /// </summary>
    /// <param name="lockoutEnd">The lockout end time</param>
    /// <returns>A locked account result</returns>
    public static LoginUserResult AccountLocked(DateTime lockoutEnd)
    {
        return new LoginUserResult
        {
            Success = false,
            IsAccountLocked = true,
            LockoutEnd = lockoutEnd,
            ErrorMessage = $"Account is locked until {lockoutEnd:yyyy-MM-dd HH:mm:ss} UTC"
        };
    }
}

/// <summary>
/// User information for login response
/// </summary>
public sealed class UserInfo
{
    /// <summary>
    /// Gets or sets the user ID
    /// </summary>
    public Guid Id { get; set; }

    /// <summary>
    /// Gets or sets the username
    /// </summary>
    public string Username { get; set; } = string.Empty;

    /// <summary>
    /// Gets or sets the email
    /// </summary>
    public string Email { get; set; } = string.Empty;

    /// <summary>
    /// Gets or sets the full name
    /// </summary>
    public string FullName { get; set; } = string.Empty;

    /// <summary>
    /// Gets or sets the user role
    /// </summary>
    public string Role { get; set; } = string.Empty;

    /// <summary>
    /// Gets or sets a value indicating whether the email is confirmed
    /// </summary>
    public bool IsEmailConfirmed { get; set; }
}
