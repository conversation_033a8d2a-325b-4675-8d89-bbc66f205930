using FleetXQ.Domain.Common;
using FleetXQ.Domain.Enums;
using FleetXQ.Domain.Events;

namespace FleetXQ.Domain.Entities;

/// <summary>
/// Represents a driver in the fleet management system
/// </summary>
public class Driver : BaseEntity
{
    /// <summary>
    /// Gets or sets the user ID associated with this driver
    /// </summary>
    public Guid? UserId { get; private set; }

    /// <summary>
    /// Gets or sets the employee ID
    /// </summary>
    public string? EmployeeId { get; private set; }

    /// <summary>
    /// Gets or sets the first name
    /// </summary>
    public string FirstName { get; private set; } = string.Empty;

    /// <summary>
    /// Gets or sets the last name
    /// </summary>
    public string LastName { get; private set; } = string.Empty;

    /// <summary>
    /// Gets or sets the email address
    /// </summary>
    public string? Email { get; private set; }

    /// <summary>
    /// Gets or sets the phone number
    /// </summary>
    public string? Phone { get; private set; }

    /// <summary>
    /// Gets or sets the license number
    /// </summary>
    public string? LicenseNumber { get; private set; }

    /// <summary>
    /// Gets or sets the license class
    /// </summary>
    public string? LicenseClass { get; private set; }

    /// <summary>
    /// Gets or sets the license expiry date
    /// </summary>
    public DateTime? LicenseExpiryDate { get; private set; }

    /// <summary>
    /// Gets or sets the hire date
    /// </summary>
    public DateTime? HireDate { get; private set; }

    /// <summary>
    /// Gets or sets the date of birth
    /// </summary>
    public DateTime? DateOfBirth { get; private set; }

    /// <summary>
    /// Gets or sets the address
    /// </summary>
    public string? Address { get; private set; }

    /// <summary>
    /// Gets or sets the emergency contact name
    /// </summary>
    public string? EmergencyContact { get; private set; }

    /// <summary>
    /// Gets or sets the emergency contact phone
    /// </summary>
    public string? EmergencyPhone { get; private set; }

    /// <summary>
    /// Gets or sets the driver status
    /// </summary>
    public DriverStatus Status { get; private set; }

    /// <summary>
    /// Gets the full name of the driver
    /// </summary>
    public string FullName => $"{FirstName} {LastName}".Trim();

    /// <summary>
    /// Gets a value indicating whether the driver's license is expired
    /// </summary>
    public bool IsLicenseExpired => LicenseExpiryDate.HasValue && LicenseExpiryDate.Value < DateTime.UtcNow;

    /// <summary>
    /// Gets a value indicating whether the driver's license is expiring soon (within 30 days)
    /// </summary>
    public bool IsLicenseExpiringSoon => LicenseExpiryDate.HasValue && 
        LicenseExpiryDate.Value > DateTime.UtcNow && 
        LicenseExpiryDate.Value <= DateTime.UtcNow.AddDays(30);

    /// <summary>
    /// Gets a value indicating whether the driver is available for assignment
    /// </summary>
    public bool IsAvailable => Status == DriverStatus.Active && !IsLicenseExpired;

    /// <summary>
    /// Gets the driver's age in years
    /// </summary>
    public int? Age => DateOfBirth.HasValue ? 
        (int)((DateTime.UtcNow - DateOfBirth.Value).TotalDays / 365.25) : null;

    /// <summary>
    /// Gets the years of service
    /// </summary>
    public double? YearsOfService => HireDate.HasValue ? 
        (DateTime.UtcNow - HireDate.Value).TotalDays / 365.25 : null;

    /// <summary>
    /// Private constructor for Entity Framework
    /// </summary>
    private Driver() { }

    /// <summary>
    /// Initializes a new instance of the <see cref="Driver"/> class
    /// </summary>
    /// <param name="firstName">The first name</param>
    /// <param name="lastName">The last name</param>
    /// <param name="email">The email address</param>
    public Driver(string firstName, string lastName, string? email = null)
    {
        if (string.IsNullOrWhiteSpace(firstName))
            throw new ArgumentException("First name cannot be empty", nameof(firstName));
        
        if (string.IsNullOrWhiteSpace(lastName))
            throw new ArgumentException("Last name cannot be empty", nameof(lastName));

        FirstName = firstName;
        LastName = lastName;
        Email = email;
        Status = DriverStatus.Active;
    }

    /// <summary>
    /// Updates the driver's basic information
    /// </summary>
    /// <param name="firstName">The first name</param>
    /// <param name="lastName">The last name</param>
    /// <param name="email">The email address</param>
    /// <param name="phone">The phone number</param>
    /// <param name="address">The address</param>
    public void UpdateBasicInfo(string firstName, string lastName, string? email = null, string? phone = null, string? address = null)
    {
        if (string.IsNullOrWhiteSpace(firstName))
            throw new ArgumentException("First name cannot be empty", nameof(firstName));
        
        if (string.IsNullOrWhiteSpace(lastName))
            throw new ArgumentException("Last name cannot be empty", nameof(lastName));

        FirstName = firstName;
        LastName = lastName;
        Email = email;
        Phone = phone;
        Address = address;
    }

    /// <summary>
    /// Updates the driver's license information
    /// </summary>
    /// <param name="licenseNumber">The license number</param>
    /// <param name="licenseClass">The license class</param>
    /// <param name="expiryDate">The license expiry date</param>
    public void UpdateLicenseInfo(string licenseNumber, string licenseClass, DateTime expiryDate)
    {
        if (string.IsNullOrWhiteSpace(licenseNumber))
            throw new ArgumentException("License number cannot be empty", nameof(licenseNumber));
        
        if (string.IsNullOrWhiteSpace(licenseClass))
            throw new ArgumentException("License class cannot be empty", nameof(licenseClass));

        if (expiryDate <= DateTime.UtcNow)
            throw new ArgumentException("License expiry date must be in the future", nameof(expiryDate));

        LicenseNumber = licenseNumber;
        LicenseClass = licenseClass;
        LicenseExpiryDate = expiryDate;
    }

    /// <summary>
    /// Updates the driver's employment information
    /// </summary>
    /// <param name="employeeId">The employee ID</param>
    /// <param name="hireDate">The hire date</param>
    public void UpdateEmploymentInfo(string employeeId, DateTime hireDate)
    {
        if (string.IsNullOrWhiteSpace(employeeId))
            throw new ArgumentException("Employee ID cannot be empty", nameof(employeeId));

        if (hireDate > DateTime.UtcNow)
            throw new ArgumentException("Hire date cannot be in the future", nameof(hireDate));

        EmployeeId = employeeId;
        HireDate = hireDate;
    }

    /// <summary>
    /// Updates the driver's emergency contact information
    /// </summary>
    /// <param name="contactName">The emergency contact name</param>
    /// <param name="contactPhone">The emergency contact phone</param>
    public void UpdateEmergencyContact(string contactName, string contactPhone)
    {
        if (string.IsNullOrWhiteSpace(contactName))
            throw new ArgumentException("Emergency contact name cannot be empty", nameof(contactName));
        
        if (string.IsNullOrWhiteSpace(contactPhone))
            throw new ArgumentException("Emergency contact phone cannot be empty", nameof(contactPhone));

        EmergencyContact = contactName;
        EmergencyPhone = contactPhone;
    }

    /// <summary>
    /// Sets the driver's date of birth
    /// </summary>
    /// <param name="dateOfBirth">The date of birth</param>
    public void SetDateOfBirth(DateTime dateOfBirth)
    {
        if (dateOfBirth >= DateTime.UtcNow)
            throw new ArgumentException("Date of birth must be in the past", nameof(dateOfBirth));

        var age = (DateTime.UtcNow - dateOfBirth).TotalDays / 365.25;
        if (age < 18)
            throw new ArgumentException("Driver must be at least 18 years old", nameof(dateOfBirth));

        DateOfBirth = dateOfBirth;
    }

    /// <summary>
    /// Changes the driver status
    /// </summary>
    /// <param name="newStatus">The new status</param>
    /// <param name="reason">The reason for the status change</param>
    public void ChangeStatus(DriverStatus newStatus, string? reason = null)
    {
        if (Status == newStatus)
            return;

        var previousStatus = Status;
        Status = newStatus;

        // Add domain event for status change
        // This would be implemented when we create the domain events
    }

    /// <summary>
    /// Associates the driver with a user account
    /// </summary>
    /// <param name="userId">The user ID</param>
    public void AssociateWithUser(Guid userId)
    {
        if (userId == Guid.Empty)
            throw new ArgumentException("User ID cannot be empty", nameof(userId));

        UserId = userId;
    }

    /// <summary>
    /// Validates if the driver can be assigned to a vehicle
    /// </summary>
    /// <returns>True if the driver can be assigned, false otherwise</returns>
    public bool CanBeAssignedToVehicle()
    {
        return IsAvailable && !string.IsNullOrWhiteSpace(LicenseNumber);
    }

    /// <summary>
    /// Checks if the driver's license is compatible with a vehicle type
    /// </summary>
    /// <param name="vehicleType">The vehicle type</param>
    /// <returns>True if compatible, false otherwise</returns>
    public bool IsLicenseCompatibleWith(string vehicleType)
    {
        if (string.IsNullOrWhiteSpace(LicenseClass) || string.IsNullOrWhiteSpace(vehicleType))
            return false;

        // Basic license compatibility logic
        return vehicleType.ToLower() switch
        {
            "truck" => LicenseClass.Contains("A") || LicenseClass.Contains("CDL"),
            "van" => LicenseClass.Contains("B") || LicenseClass.Contains("A") || LicenseClass.Contains("CDL"),
            "car" => true, // Most licenses allow cars
            "motorcycle" => LicenseClass.Contains("M") || LicenseClass.Contains("A"),
            _ => LicenseClass.Contains("B") || LicenseClass.Contains("A")
        };
    }
}
