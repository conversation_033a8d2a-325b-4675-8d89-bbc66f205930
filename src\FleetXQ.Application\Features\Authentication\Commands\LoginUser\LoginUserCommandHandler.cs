using FleetXQ.Application.Interfaces;
using FleetXQ.Domain.Interfaces;
using MediatR;
using Microsoft.Extensions.Logging;

namespace FleetXQ.Application.Features.Authentication.Commands.LoginUser;

/// <summary>
/// Handler for LoginUserCommand
/// </summary>
public sealed class LoginUserCommandHandler : IRequestHandler<LoginUserCommand, LoginUserResult>
{
    private readonly IUserRepository _userRepository;
    private readonly IPasswordHashingService _passwordHashingService;
    private readonly ITokenService _tokenService;
    private readonly ILogger<LoginUserCommandHandler> _logger;

    /// <summary>
    /// Initializes a new instance of the <see cref="LoginUserCommandHandler"/> class
    /// </summary>
    /// <param name="userRepository">The user repository</param>
    /// <param name="passwordHashingService">The password hashing service</param>
    /// <param name="tokenService">The token service</param>
    /// <param name="logger">The logger</param>
    public LoginUserCommandHandler(
        IUserRepository userRepository,
        IPasswordHashingService passwordHashingService,
        ITokenService tokenService,
        ILogger<LoginUserCommandHandler> logger)
    {
        _userRepository = userRepository ?? throw new ArgumentNullException(nameof(userRepository));
        _passwordHashingService = passwordHashingService ?? throw new ArgumentNullException(nameof(passwordHashingService));
        _tokenService = tokenService ?? throw new ArgumentNullException(nameof(tokenService));
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
    }

    /// <summary>
    /// Handles the LoginUserCommand
    /// </summary>
    /// <param name="request">The command request</param>
    /// <param name="cancellationToken">The cancellation token</param>
    /// <returns>The command result</returns>
    public async Task<LoginUserResult> Handle(LoginUserCommand request, CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogInformation("Login attempt for user: {UsernameOrEmail}", request.UsernameOrEmail);

            // Find user by username or email
            var user = await FindUserByUsernameOrEmail(request.UsernameOrEmail, cancellationToken);
            if (user == null)
            {
                _logger.LogWarning("Login failed - user not found: {UsernameOrEmail}", request.UsernameOrEmail);
                return LoginUserResult.Failed("Invalid username/email or password");
            }

            // Check if account is locked
            if (user.IsLockedOut)
            {
                _logger.LogWarning("Login failed - account locked: {UserId}", user.Id);
                return LoginUserResult.AccountLocked(user.LockoutEnd!.Value);
            }

            // Check if account is active
            if (!user.IsActive)
            {
                _logger.LogWarning("Login failed - account inactive: {UserId}", user.Id);
                return LoginUserResult.Failed("Account is inactive");
            }

            // Verify password
            if (!_passwordHashingService.VerifyPassword(request.Password, user.PasswordHash))
            {
                _logger.LogWarning("Login failed - invalid password: {UserId}", user.Id);
                
                // Record failed login attempt
                user.RecordFailedLogin();
                await _userRepository.UpdateAsync(user, cancellationToken);

                var remainingAttempts = Math.Max(0, 5 - user.FailedLoginAttempts);
                return LoginUserResult.Failed("Invalid username/email or password", remainingAttempts);
            }

            // Successful login
            user.RecordSuccessfulLogin();
            await _userRepository.UpdateAsync(user, cancellationToken);

            // Generate tokens
            var accessToken = _tokenService.GenerateAccessToken(user);
            var refreshToken = _tokenService.GenerateRefreshToken();
            var tokenExpiry = DateTime.UtcNow.Add(_tokenService.GetAccessTokenExpiry());

            // Create user info
            var userInfo = new UserInfo
            {
                Id = user.Id,
                Username = user.Username,
                Email = user.Email,
                FullName = user.FullName,
                Role = user.Role.ToString(),
                IsEmailConfirmed = user.IsEmailConfirmed
            };

            _logger.LogInformation("Login successful for user: {UserId}", user.Id);

            return LoginUserResult.Successful(accessToken, refreshToken, tokenExpiry, userInfo);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during login for user: {UsernameOrEmail}", request.UsernameOrEmail);
            return LoginUserResult.Failed("An error occurred during login");
        }
    }

    /// <summary>
    /// Finds a user by username or email
    /// </summary>
    /// <param name="usernameOrEmail">The username or email</param>
    /// <param name="cancellationToken">The cancellation token</param>
    /// <returns>The user if found, null otherwise</returns>
    private async Task<Domain.Entities.User?> FindUserByUsernameOrEmail(string usernameOrEmail, CancellationToken cancellationToken)
    {
        // Try to find by username first
        var user = await _userRepository.GetByUsernameAsync(usernameOrEmail, cancellationToken);
        
        // If not found by username, try by email
        if (user == null && usernameOrEmail.Contains('@'))
        {
            user = await _userRepository.GetByEmailAsync(usernameOrEmail, cancellationToken);
        }

        return user;
    }
}
