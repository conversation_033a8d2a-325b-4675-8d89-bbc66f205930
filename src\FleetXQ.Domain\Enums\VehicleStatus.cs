namespace FleetXQ.Domain.Enums;

/// <summary>
/// Represents the operational status of a vehicle
/// </summary>
public enum VehicleStatus
{
    /// <summary>
    /// Vehicle is active and operational
    /// </summary>
    Active = 1,

    /// <summary>
    /// Vehicle is under maintenance
    /// </summary>
    Maintenance = 2,

    /// <summary>
    /// Vehicle is offline or temporarily unavailable
    /// </summary>
    Offline = 3,

    /// <summary>
    /// Vehicle is retired from service
    /// </summary>
    Retired = 4
}
