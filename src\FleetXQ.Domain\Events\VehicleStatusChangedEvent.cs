using FleetXQ.Domain.Common;
using FleetXQ.Domain.Enums;

namespace FleetXQ.Domain.Events;

/// <summary>
/// Domain event raised when a vehicle's status changes
/// </summary>
public sealed class VehicleStatusChangedEvent : BaseDomainEvent
{
    /// <summary>
    /// Gets the vehicle ID
    /// </summary>
    public Guid VehicleId { get; }

    /// <summary>
    /// Gets the previous status
    /// </summary>
    public VehicleStatus PreviousStatus { get; }

    /// <summary>
    /// Gets the new status
    /// </summary>
    public VehicleStatus NewStatus { get; }

    /// <summary>
    /// Gets the reason for the status change
    /// </summary>
    public string? Reason { get; }

    /// <summary>
    /// Gets the user who initiated the status change
    /// </summary>
    public Guid? ChangedBy { get; }

    /// <summary>
    /// Gets additional context about the status change
    /// </summary>
    public Dictionary<string, object> Context { get; }

    /// <summary>
    /// Gets a value indicating whether the status change represents an improvement
    /// </summary>
    public bool IsImprovement => GetStatusPriority(NewStatus) > GetStatusPriority(PreviousStatus);

    /// <summary>
    /// Gets a value indicating whether the status change represents a degradation
    /// </summary>
    public bool IsDegradation => GetStatusPriority(NewStatus) < GetStatusPriority(PreviousStatus);

    /// <summary>
    /// Gets a value indicating whether the vehicle became available
    /// </summary>
    public bool BecameAvailable => NewStatus == VehicleStatus.Active && PreviousStatus != VehicleStatus.Active;

    /// <summary>
    /// Gets a value indicating whether the vehicle became unavailable
    /// </summary>
    public bool BecameUnavailable => PreviousStatus == VehicleStatus.Active && NewStatus != VehicleStatus.Active;

    /// <summary>
    /// Initializes a new instance of the <see cref="VehicleStatusChangedEvent"/> class
    /// </summary>
    /// <param name="vehicleId">The vehicle ID</param>
    /// <param name="previousStatus">The previous status</param>
    /// <param name="newStatus">The new status</param>
    /// <param name="reason">The reason for the change</param>
    /// <param name="changedBy">The user who initiated the change</param>
    /// <param name="context">Additional context</param>
    public VehicleStatusChangedEvent(Guid vehicleId, VehicleStatus previousStatus, VehicleStatus newStatus,
        string? reason = null, Guid? changedBy = null, Dictionary<string, object>? context = null)
    {
        if (vehicleId == Guid.Empty)
            throw new ArgumentException("Vehicle ID cannot be empty", nameof(vehicleId));

        if (previousStatus == newStatus)
            throw new ArgumentException("Previous and new status cannot be the same");

        VehicleId = vehicleId;
        PreviousStatus = previousStatus;
        NewStatus = newStatus;
        Reason = reason;
        ChangedBy = changedBy;
        Context = context ?? new Dictionary<string, object>();
    }

    /// <summary>
    /// Creates a vehicle status changed event for activation
    /// </summary>
    /// <param name="vehicleId">The vehicle ID</param>
    /// <param name="previousStatus">The previous status</param>
    /// <param name="changedBy">The user who activated the vehicle</param>
    /// <param name="reason">The reason for activation</param>
    /// <returns>A new VehicleStatusChangedEvent</returns>
    public static VehicleStatusChangedEvent ForActivation(Guid vehicleId, VehicleStatus previousStatus, 
        Guid? changedBy = null, string? reason = null)
    {
        var context = new Dictionary<string, object>
        {
            ["changeType"] = "activation",
            ["automated"] = changedBy == null
        };

        return new VehicleStatusChangedEvent(vehicleId, previousStatus, VehicleStatus.Active, 
            reason ?? "Vehicle activated", changedBy, context);
    }

    /// <summary>
    /// Creates a vehicle status changed event for maintenance
    /// </summary>
    /// <param name="vehicleId">The vehicle ID</param>
    /// <param name="previousStatus">The previous status</param>
    /// <param name="maintenanceType">The type of maintenance</param>
    /// <param name="scheduledDate">The scheduled maintenance date</param>
    /// <param name="changedBy">The user who scheduled the maintenance</param>
    /// <returns>A new VehicleStatusChangedEvent</returns>
    public static VehicleStatusChangedEvent ForMaintenance(Guid vehicleId, VehicleStatus previousStatus,
        string maintenanceType, DateTime? scheduledDate = null, Guid? changedBy = null)
    {
        var context = new Dictionary<string, object>
        {
            ["changeType"] = "maintenance",
            ["maintenanceType"] = maintenanceType,
            ["automated"] = changedBy == null
        };

        if (scheduledDate.HasValue)
        {
            context["scheduledDate"] = scheduledDate.Value;
        }

        var reason = $"Vehicle scheduled for {maintenanceType} maintenance";
        return new VehicleStatusChangedEvent(vehicleId, previousStatus, VehicleStatus.Maintenance, reason, changedBy, context);
    }

    /// <summary>
    /// Creates a vehicle status changed event for going offline
    /// </summary>
    /// <param name="vehicleId">The vehicle ID</param>
    /// <param name="previousStatus">The previous status</param>
    /// <param name="reason">The reason for going offline</param>
    /// <param name="changedBy">The user who took the vehicle offline</param>
    /// <returns>A new VehicleStatusChangedEvent</returns>
    public static VehicleStatusChangedEvent ForOffline(Guid vehicleId, VehicleStatus previousStatus,
        string? reason = null, Guid? changedBy = null)
    {
        var context = new Dictionary<string, object>
        {
            ["changeType"] = "offline",
            ["automated"] = changedBy == null
        };

        return new VehicleStatusChangedEvent(vehicleId, previousStatus, VehicleStatus.Offline, 
            reason ?? "Vehicle taken offline", changedBy, context);
    }

    /// <summary>
    /// Creates a vehicle status changed event for retirement
    /// </summary>
    /// <param name="vehicleId">The vehicle ID</param>
    /// <param name="previousStatus">The previous status</param>
    /// <param name="retirementReason">The reason for retirement</param>
    /// <param name="changedBy">The user who retired the vehicle</param>
    /// <returns>A new VehicleStatusChangedEvent</returns>
    public static VehicleStatusChangedEvent ForRetirement(Guid vehicleId, VehicleStatus previousStatus,
        string retirementReason, Guid changedBy)
    {
        var context = new Dictionary<string, object>
        {
            ["changeType"] = "retirement",
            ["automated"] = false,
            ["retirementReason"] = retirementReason
        };

        return new VehicleStatusChangedEvent(vehicleId, previousStatus, VehicleStatus.Retired, 
            $"Vehicle retired: {retirementReason}", changedBy, context);
    }

    /// <summary>
    /// Gets the priority level of a vehicle status for comparison
    /// </summary>
    /// <param name="status">The vehicle status</param>
    /// <returns>The priority level (higher is better)</returns>
    private static int GetStatusPriority(VehicleStatus status)
    {
        return status switch
        {
            VehicleStatus.Active => 4,
            VehicleStatus.Maintenance => 3,
            VehicleStatus.Offline => 2,
            VehicleStatus.Retired => 1,
            _ => 0
        };
    }

    /// <summary>
    /// Gets a human-readable description of the status change
    /// </summary>
    /// <returns>A description of the status change</returns>
    public string GetDescription()
    {
        var direction = IsImprovement ? "improved" : IsDegradation ? "degraded" : "changed";
        var description = $"Vehicle status {direction} from {PreviousStatus} to {NewStatus}";
        
        if (!string.IsNullOrWhiteSpace(Reason))
        {
            description += $": {Reason}";
        }

        return description;
    }

    /// <summary>
    /// Gets the impact level of the status change
    /// </summary>
    /// <returns>The impact level</returns>
    public StatusChangeImpact GetImpact()
    {
        if (BecameAvailable)
            return StatusChangeImpact.Positive;
        
        if (BecameUnavailable)
            return StatusChangeImpact.Negative;
        
        if (NewStatus == VehicleStatus.Retired)
            return StatusChangeImpact.Critical;
        
        return StatusChangeImpact.Neutral;
    }
}

/// <summary>
/// Represents the impact level of a status change
/// </summary>
public enum StatusChangeImpact
{
    /// <summary>
    /// Positive impact (vehicle became available)
    /// </summary>
    Positive,

    /// <summary>
    /// Neutral impact (no availability change)
    /// </summary>
    Neutral,

    /// <summary>
    /// Negative impact (vehicle became unavailable)
    /// </summary>
    Negative,

    /// <summary>
    /// Critical impact (vehicle retired)
    /// </summary>
    Critical
}
