using FleetXQ.Application.Features.Telemetry.DTOs;
using MediatR;

namespace FleetXQ.Application.Features.Telemetry.Queries.GetTelemetryHistory;

/// <summary>
/// Query to get telemetry history for a vehicle
/// </summary>
public sealed class GetTelemetryHistoryQuery : IRequest<GetTelemetryHistoryResult>
{
    /// <summary>
    /// Gets or sets the vehicle ID
    /// </summary>
    public Guid VehicleId { get; set; }

    /// <summary>
    /// Gets or sets the start date for the history query
    /// </summary>
    public DateTime StartDate { get; set; }

    /// <summary>
    /// Gets or sets the end date for the history query
    /// </summary>
    public DateTime EndDate { get; set; }

    /// <summary>
    /// Gets or sets the page number for pagination (1-based)
    /// </summary>
    public int PageNumber { get; set; } = 1;

    /// <summary>
    /// Gets or sets the page size for pagination
    /// </summary>
    public int PageSize { get; set; } = 100;

    /// <summary>
    /// Gets or sets the interval in minutes for data aggregation (optional)
    /// </summary>
    public int? IntervalMinutes { get; set; }

    /// <summary>
    /// Gets or sets a value indicating whether to include only significant changes
    /// </summary>
    public bool SignificantChangesOnly { get; set; } = false;

    /// <summary>
    /// Initializes a new instance of the <see cref="GetTelemetryHistoryQuery"/> class
    /// </summary>
    /// <param name="vehicleId">The vehicle ID</param>
    /// <param name="startDate">The start date</param>
    /// <param name="endDate">The end date</param>
    public GetTelemetryHistoryQuery(Guid vehicleId, DateTime startDate, DateTime endDate)
    {
        VehicleId = vehicleId;
        StartDate = startDate;
        EndDate = endDate;
    }

    /// <summary>
    /// Initializes a new instance of the <see cref="GetTelemetryHistoryQuery"/> class
    /// </summary>
    public GetTelemetryHistoryQuery()
    {
    }
}

/// <summary>
/// Result of getting telemetry history
/// </summary>
public sealed class GetTelemetryHistoryResult
{
    /// <summary>
    /// Gets or sets the telemetry history data
    /// </summary>
    public TelemetryHistoryDto? History { get; set; }

    /// <summary>
    /// Gets or sets the total count of records (before pagination)
    /// </summary>
    public int TotalCount { get; set; }

    /// <summary>
    /// Gets or sets the current page number
    /// </summary>
    public int PageNumber { get; set; }

    /// <summary>
    /// Gets or sets the page size
    /// </summary>
    public int PageSize { get; set; }

    /// <summary>
    /// Gets or sets the total number of pages
    /// </summary>
    public int TotalPages { get; set; }

    /// <summary>
    /// Gets or sets a value indicating whether there is a next page
    /// </summary>
    public bool HasNextPage { get; set; }

    /// <summary>
    /// Gets or sets a value indicating whether there is a previous page
    /// </summary>
    public bool HasPreviousPage { get; set; }

    /// <summary>
    /// Gets or sets a value indicating whether the operation was successful
    /// </summary>
    public bool Success { get; set; }

    /// <summary>
    /// Gets or sets the error message if the operation failed
    /// </summary>
    public string? ErrorMessage { get; set; }

    /// <summary>
    /// Creates a successful result
    /// </summary>
    /// <param name="history">The telemetry history</param>
    /// <param name="totalCount">The total count</param>
    /// <param name="pageNumber">The page number</param>
    /// <param name="pageSize">The page size</param>
    /// <returns>A successful result</returns>
    public static GetTelemetryHistoryResult CreateSuccess(TelemetryHistoryDto history, int totalCount, int pageNumber, int pageSize)
    {
        var totalPages = (int)Math.Ceiling((double)totalCount / pageSize);
        
        return new GetTelemetryHistoryResult
        {
            History = history,
            TotalCount = totalCount,
            PageNumber = pageNumber,
            PageSize = pageSize,
            TotalPages = totalPages,
            HasNextPage = pageNumber < totalPages,
            HasPreviousPage = pageNumber > 1,
            Success = true
        };
    }

    /// <summary>
    /// Creates a not found result
    /// </summary>
    /// <returns>A not found result</returns>
    public static GetTelemetryHistoryResult NotFound()
    {
        return new GetTelemetryHistoryResult
        {
            Success = false,
            ErrorMessage = "No telemetry history found"
        };
    }

    /// <summary>
    /// Creates a failed result
    /// </summary>
    /// <param name="errorMessage">The error message</param>
    /// <returns>A failed result</returns>
    public static GetTelemetryHistoryResult Failed(string errorMessage)
    {
        return new GetTelemetryHistoryResult
        {
            Success = false,
            ErrorMessage = errorMessage
        };
    }
}
