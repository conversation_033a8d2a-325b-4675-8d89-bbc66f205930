using FleetXQ.Domain.Entities;
using FleetXQ.Domain.Enums;
using FleetXQ.Domain.ValueObjects;

namespace FleetXQ.Domain.Services;

/// <summary>
/// Domain service for evaluating and creating alerts based on various conditions
/// </summary>
public interface IAlertEvaluationService
{
    /// <summary>
    /// Evaluates telemetry data and creates alerts if necessary
    /// </summary>
    /// <param name="vehicle">The vehicle</param>
    /// <param name="driver">The driver (optional)</param>
    /// <param name="location">The current location</param>
    /// <param name="speed">The current speed</param>
    /// <param name="fuelLevel">The current fuel level</param>
    /// <returns>List of alerts that should be created</returns>
    IEnumerable<Alert> EvaluateTelemetryData(Vehicle vehicle, Driver? driver, Location location, 
        Speed speed, FuelLevel? fuelLevel);

    /// <summary>
    /// Evaluates if a speed alert should be created
    /// </summary>
    /// <param name="vehicle">The vehicle</param>
    /// <param name="driver">The driver</param>
    /// <param name="currentSpeed">The current speed</param>
    /// <param name="speedLimit">The speed limit</param>
    /// <param name="location">The location</param>
    /// <returns>Speed alert if conditions are met, null otherwise</returns>
    Alert? EvaluateSpeedAlert(Vehicle vehicle, Driver? driver, Speed currentSpeed, Speed speedLimit, Location location);

    /// <summary>
    /// Evaluates if a fuel alert should be created
    /// </summary>
    /// <param name="vehicle">The vehicle</param>
    /// <param name="fuelLevel">The current fuel level</param>
    /// <param name="location">The location</param>
    /// <returns>Fuel alert if conditions are met, null otherwise</returns>
    Alert? EvaluateFuelAlert(Vehicle vehicle, FuelLevel fuelLevel, Location? location);

    /// <summary>
    /// Evaluates if a maintenance alert should be created
    /// </summary>
    /// <param name="vehicle">The vehicle</param>
    /// <returns>Maintenance alert if conditions are met, null otherwise</returns>
    Alert? EvaluateMaintenanceAlert(Vehicle vehicle);

    /// <summary>
    /// Evaluates if a harsh driving alert should be created
    /// </summary>
    /// <param name="vehicle">The vehicle</param>
    /// <param name="driver">The driver</param>
    /// <param name="eventType">The type of harsh driving event</param>
    /// <param name="location">The location</param>
    /// <param name="severity">The severity of the event</param>
    /// <returns>Harsh driving alert if conditions are met, null otherwise</returns>
    Alert? EvaluateHarshDrivingAlert(Vehicle vehicle, Driver driver, string eventType, Location location, 
        HarshDrivingSeverity severity = HarshDrivingSeverity.Medium);

    /// <summary>
    /// Evaluates if a geofence alert should be created
    /// </summary>
    /// <param name="vehicle">The vehicle</param>
    /// <param name="driver">The driver</param>
    /// <param name="location">The current location</param>
    /// <param name="geofenceName">The name of the geofence</param>
    /// <param name="eventType">The type of geofence event (enter/exit)</param>
    /// <returns>Geofence alert if conditions are met, null otherwise</returns>
    Alert? EvaluateGeofenceAlert(Vehicle vehicle, Driver? driver, Location location, string geofenceName, 
        GeofenceEventType eventType);

    /// <summary>
    /// Evaluates if a system alert should be created
    /// </summary>
    /// <param name="vehicle">The vehicle</param>
    /// <param name="systemMessage">The system message</param>
    /// <param name="severity">The severity level</param>
    /// <returns>System alert if conditions are met, null otherwise</returns>
    Alert? EvaluateSystemAlert(Vehicle vehicle, string systemMessage, AlertSeverity severity);

    /// <summary>
    /// Determines the appropriate alert severity based on conditions
    /// </summary>
    /// <param name="alertType">The alert type</param>
    /// <param name="context">The alert context</param>
    /// <returns>The appropriate alert severity</returns>
    AlertSeverity DetermineAlertSeverity(AlertType alertType, AlertEvaluationContext context);

    /// <summary>
    /// Checks if an alert should be suppressed due to recent similar alerts
    /// </summary>
    /// <param name="alertType">The alert type</param>
    /// <param name="vehicleId">The vehicle ID</param>
    /// <param name="driverId">The driver ID</param>
    /// <param name="recentAlerts">Recent alerts for comparison</param>
    /// <returns>True if alert should be suppressed, false otherwise</returns>
    bool ShouldSuppressAlert(AlertType alertType, Guid vehicleId, Guid? driverId, 
        IEnumerable<Alert> recentAlerts);

    /// <summary>
    /// Gets alert suppression rules for different alert types
    /// </summary>
    /// <param name="alertType">The alert type</param>
    /// <returns>Suppression rules for the alert type</returns>
    AlertSuppressionRules GetSuppressionRules(AlertType alertType);

    /// <summary>
    /// Evaluates if existing alerts should be auto-resolved
    /// </summary>
    /// <param name="existingAlerts">The existing active alerts</param>
    /// <param name="currentConditions">The current vehicle/driver conditions</param>
    /// <returns>List of alerts that should be auto-resolved</returns>
    IEnumerable<Alert> EvaluateAutoResolution(IEnumerable<Alert> existingAlerts, 
        AlertEvaluationContext currentConditions);
}

/// <summary>
/// Represents the context for alert evaluation
/// </summary>
public class AlertEvaluationContext
{
    /// <summary>
    /// Gets or sets the vehicle
    /// </summary>
    public Vehicle Vehicle { get; set; } = null!;

    /// <summary>
    /// Gets or sets the driver
    /// </summary>
    public Driver? Driver { get; set; }

    /// <summary>
    /// Gets or sets the current location
    /// </summary>
    public Location? Location { get; set; }

    /// <summary>
    /// Gets or sets the current speed
    /// </summary>
    public Speed? Speed { get; set; }

    /// <summary>
    /// Gets or sets the current fuel level
    /// </summary>
    public FuelLevel? FuelLevel { get; set; }

    /// <summary>
    /// Gets or sets the speed limit at current location
    /// </summary>
    public Speed? SpeedLimit { get; set; }

    /// <summary>
    /// Gets or sets the timestamp
    /// </summary>
    public DateTime Timestamp { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// Gets or sets additional context data
    /// </summary>
    public Dictionary<string, object> AdditionalData { get; set; } = new();
}

/// <summary>
/// Represents alert suppression rules
/// </summary>
public class AlertSuppressionRules
{
    /// <summary>
    /// Gets or sets the minimum time between similar alerts in minutes
    /// </summary>
    public int MinimumIntervalMinutes { get; set; }

    /// <summary>
    /// Gets or sets the maximum number of similar alerts per hour
    /// </summary>
    public int MaxAlertsPerHour { get; set; }

    /// <summary>
    /// Gets or sets a value indicating whether to suppress duplicate alerts
    /// </summary>
    public bool SuppressDuplicates { get; set; }

    /// <summary>
    /// Gets or sets the conditions that must change to allow a new alert
    /// </summary>
    public List<string> RequiredChangeConditions { get; set; } = new();

    /// <summary>
    /// Gets default suppression rules for speed alerts
    /// </summary>
    public static AlertSuppressionRules SpeedAlerts => new()
    {
        MinimumIntervalMinutes = 5,
        MaxAlertsPerHour = 6,
        SuppressDuplicates = true,
        RequiredChangeConditions = { "speed_change_threshold_10_percent" }
    };

    /// <summary>
    /// Gets default suppression rules for fuel alerts
    /// </summary>
    public static AlertSuppressionRules FuelAlerts => new()
    {
        MinimumIntervalMinutes = 15,
        MaxAlertsPerHour = 2,
        SuppressDuplicates = true,
        RequiredChangeConditions = { "fuel_level_change_5_percent" }
    };

    /// <summary>
    /// Gets default suppression rules for harsh driving alerts
    /// </summary>
    public static AlertSuppressionRules HarshDrivingAlerts => new()
    {
        MinimumIntervalMinutes = 2,
        MaxAlertsPerHour = 10,
        SuppressDuplicates = false,
        RequiredChangeConditions = { }
    };

    /// <summary>
    /// Gets default suppression rules for maintenance alerts
    /// </summary>
    public static AlertSuppressionRules MaintenanceAlerts => new()
    {
        MinimumIntervalMinutes = 1440, // 24 hours
        MaxAlertsPerHour = 1,
        SuppressDuplicates = true,
        RequiredChangeConditions = { "maintenance_date_change" }
    };

    /// <summary>
    /// Gets default suppression rules for system alerts
    /// </summary>
    public static AlertSuppressionRules SystemAlerts => new()
    {
        MinimumIntervalMinutes = 10,
        MaxAlertsPerHour = 3,
        SuppressDuplicates = true,
        RequiredChangeConditions = { }
    };
}

/// <summary>
/// Represents the severity of harsh driving events
/// </summary>
public enum HarshDrivingSeverity
{
    /// <summary>
    /// Low severity harsh driving
    /// </summary>
    Low,

    /// <summary>
    /// Medium severity harsh driving
    /// </summary>
    Medium,

    /// <summary>
    /// High severity harsh driving
    /// </summary>
    High,

    /// <summary>
    /// Critical severity harsh driving
    /// </summary>
    Critical
}

/// <summary>
/// Represents the type of geofence event
/// </summary>
public enum GeofenceEventType
{
    /// <summary>
    /// Vehicle entered a geofence
    /// </summary>
    Enter,

    /// <summary>
    /// Vehicle exited a geofence
    /// </summary>
    Exit,

    /// <summary>
    /// Vehicle violated a restricted geofence
    /// </summary>
    Violation
}
