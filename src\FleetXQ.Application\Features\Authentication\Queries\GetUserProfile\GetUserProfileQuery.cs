using FleetXQ.Application.Features.Authentication.DTOs;
using MediatR;

namespace FleetXQ.Application.Features.Authentication.Queries.GetUserProfile;

/// <summary>
/// Query to get user profile information
/// </summary>
public sealed class GetUserProfileQuery : IRequest<GetUserProfileResult>
{
    /// <summary>
    /// Gets or sets the user ID
    /// </summary>
    public Guid UserId { get; set; }

    /// <summary>
    /// Gets or sets the requesting user ID (for security checks)
    /// </summary>
    public Guid RequestingUserId { get; set; }

    /// <summary>
    /// Gets or sets a value indicating whether to include sensitive information
    /// </summary>
    public bool IncludeSensitiveInfo { get; set; } = false;

    /// <summary>
    /// Initializes a new instance of the <see cref="GetUserProfileQuery"/> class
    /// </summary>
    /// <param name="userId">The user ID</param>
    /// <param name="requestingUserId">The requesting user ID</param>
    public GetUserProfileQuery(Guid userId, Guid requestingUserId)
    {
        UserId = userId;
        RequestingUserId = requestingUserId;
    }

    /// <summary>
    /// Initializes a new instance of the <see cref="GetUserProfileQuery"/> class
    /// </summary>
    public GetUserProfileQuery()
    {
    }
}

/// <summary>
/// Result of getting user profile
/// </summary>
public sealed class GetUserProfileResult
{
    /// <summary>
    /// Gets or sets the user profile data
    /// </summary>
    public UserProfileDto? Profile { get; set; }

    /// <summary>
    /// Gets or sets a value indicating whether the user was found
    /// </summary>
    public bool Found { get; set; }

    /// <summary>
    /// Gets or sets a value indicating whether the operation was successful
    /// </summary>
    public bool Success { get; set; }

    /// <summary>
    /// Gets or sets the error message if the operation failed
    /// </summary>
    public string? ErrorMessage { get; set; }

    /// <summary>
    /// Gets or sets a value indicating whether access was denied
    /// </summary>
    public bool AccessDenied { get; set; }

    /// <summary>
    /// Creates a successful result with profile data
    /// </summary>
    /// <param name="profile">The user profile</param>
    /// <returns>A successful result</returns>
    public static GetUserProfileResult CreateSuccess(UserProfileDto profile)
    {
        return new GetUserProfileResult
        {
            Profile = profile,
            Found = true,
            Success = true
        };
    }

    /// <summary>
    /// Creates a not found result
    /// </summary>
    /// <returns>A not found result</returns>
    public static GetUserProfileResult NotFound()
    {
        return new GetUserProfileResult
        {
            Found = false,
            Success = false,
            ErrorMessage = "User not found"
        };
    }

    /// <summary>
    /// Creates an access denied result
    /// </summary>
    /// <returns>An access denied result</returns>
    public static GetUserProfileResult CreateAccessDenied()
    {
        return new GetUserProfileResult
        {
            Found = false,
            Success = false,
            AccessDenied = true,
            ErrorMessage = "Access denied"
        };
    }

    /// <summary>
    /// Creates a failed result
    /// </summary>
    /// <param name="errorMessage">The error message</param>
    /// <returns>A failed result</returns>
    public static GetUserProfileResult Failed(string errorMessage)
    {
        return new GetUserProfileResult
        {
            Found = false,
            Success = false,
            ErrorMessage = errorMessage
        };
    }
}
