using MediatR;

namespace FleetXQ.Application.Features.Telemetry.Commands.ProcessTelemetryData;

/// <summary>
/// Command to process telemetry data from a vehicle
/// </summary>
public sealed class ProcessTelemetryDataCommand : IRequest<ProcessTelemetryDataResult>
{
    /// <summary>
    /// Gets or sets the vehicle ID
    /// </summary>
    public Guid VehicleId { get; set; }

    /// <summary>
    /// Gets or sets the latitude coordinate
    /// </summary>
    public decimal Latitude { get; set; }

    /// <summary>
    /// Gets or sets the longitude coordinate
    /// </summary>
    public decimal Longitude { get; set; }

    /// <summary>
    /// Gets or sets the speed in kilometers per hour
    /// </summary>
    public decimal SpeedKmh { get; set; }

    /// <summary>
    /// Gets or sets the fuel level percentage (0-100)
    /// </summary>
    public decimal? FuelLevelPercentage { get; set; }

    /// <summary>
    /// Gets or sets the current mileage
    /// </summary>
    public decimal? Mileage { get; set; }

    /// <summary>
    /// Gets or sets the timestamp when the telemetry data was recorded
    /// </summary>
    public DateTime Timestamp { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// Gets or sets the engine temperature in Celsius
    /// </summary>
    public decimal? EngineTemperature { get; set; }

    /// <summary>
    /// Gets or sets the RPM (revolutions per minute)
    /// </summary>
    public int? RPM { get; set; }

    /// <summary>
    /// Gets or sets additional telemetry data as JSON
    /// </summary>
    public string? AdditionalData { get; set; }
}

/// <summary>
/// Result of processing telemetry data
/// </summary>
public sealed class ProcessTelemetryDataResult
{
    /// <summary>
    /// Gets or sets the vehicle ID
    /// </summary>
    public Guid VehicleId { get; set; }

    /// <summary>
    /// Gets or sets a value indicating whether the operation was successful
    /// </summary>
    public bool Success { get; set; }

    /// <summary>
    /// Gets or sets the error message if the operation failed
    /// </summary>
    public string? ErrorMessage { get; set; }

    /// <summary>
    /// Gets or sets a value indicating whether any alerts were triggered
    /// </summary>
    public bool AlertsTriggered { get; set; }

    /// <summary>
    /// Gets or sets the list of triggered alert types
    /// </summary>
    public List<string> TriggeredAlerts { get; set; } = new();

    /// <summary>
    /// Gets or sets the timestamp when the data was processed
    /// </summary>
    public DateTime ProcessedAt { get; set; }

    /// <summary>
    /// Creates a successful result
    /// </summary>
    /// <param name="vehicleId">The vehicle ID</param>
    /// <param name="alertsTriggered">Whether alerts were triggered</param>
    /// <param name="triggeredAlerts">The list of triggered alerts</param>
    /// <returns>A successful result</returns>
    public static ProcessTelemetryDataResult Successful(Guid vehicleId, bool alertsTriggered = false, List<string>? triggeredAlerts = null)
    {
        return new ProcessTelemetryDataResult
        {
            VehicleId = vehicleId,
            Success = true,
            AlertsTriggered = alertsTriggered,
            TriggeredAlerts = triggeredAlerts ?? new List<string>(),
            ProcessedAt = DateTime.UtcNow
        };
    }

    /// <summary>
    /// Creates a failed result
    /// </summary>
    /// <param name="vehicleId">The vehicle ID</param>
    /// <param name="errorMessage">The error message</param>
    /// <returns>A failed result</returns>
    public static ProcessTelemetryDataResult Failed(Guid vehicleId, string errorMessage)
    {
        return new ProcessTelemetryDataResult
        {
            VehicleId = vehicleId,
            Success = false,
            ErrorMessage = errorMessage,
            ProcessedAt = DateTime.UtcNow
        };
    }
}
