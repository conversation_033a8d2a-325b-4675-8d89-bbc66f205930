using FleetXQ.Domain.Common;
using FleetXQ.Domain.Enums;
using FleetXQ.Domain.ValueObjects;
using FleetXQ.Domain.Events;

namespace FleetXQ.Domain.Entities;

/// <summary>
/// Represents an alert in the fleet management system
/// </summary>
public class Alert : BaseEntity
{
    /// <summary>
    /// Gets or sets the vehicle ID associated with this alert
    /// </summary>
    public Guid? VehicleId { get; private set; }

    /// <summary>
    /// Gets or sets the driver ID associated with this alert
    /// </summary>
    public Guid? DriverId { get; private set; }

    /// <summary>
    /// Gets or sets the alert type
    /// </summary>
    public AlertType AlertType { get; private set; }

    /// <summary>
    /// Gets or sets the alert severity
    /// </summary>
    public AlertSeverity Severity { get; private set; }

    /// <summary>
    /// Gets or sets the alert message
    /// </summary>
    public string Message { get; private set; } = string.Empty;

    /// <summary>
    /// Gets or sets the alert status
    /// </summary>
    public AlertStatus Status { get; private set; }

    /// <summary>
    /// Gets or sets the date and time when the alert was acknowledged
    /// </summary>
    public DateTime? AcknowledgedDate { get; private set; }

    /// <summary>
    /// Gets or sets the ID of the user who acknowledged the alert
    /// </summary>
    public Guid? AcknowledgedBy { get; private set; }

    /// <summary>
    /// Gets or sets the acknowledgment notes
    /// </summary>
    public string? AcknowledgmentNotes { get; private set; }

    /// <summary>
    /// Gets or sets the date and time when the alert was resolved
    /// </summary>
    public DateTime? ResolvedDate { get; private set; }

    /// <summary>
    /// Gets or sets the ID of the user who resolved the alert
    /// </summary>
    public Guid? ResolvedBy { get; private set; }

    /// <summary>
    /// Gets or sets the resolution notes
    /// </summary>
    public string? ResolutionNotes { get; private set; }

    /// <summary>
    /// Gets or sets the location where the alert occurred
    /// </summary>
    public Location? Location { get; private set; }

    /// <summary>
    /// Gets or sets additional metadata as JSON
    /// </summary>
    public string? Metadata { get; private set; }

    /// <summary>
    /// Gets or sets the expiry date for the alert
    /// </summary>
    public DateTime? ExpiryDate { get; private set; }

    /// <summary>
    /// Gets a value indicating whether the alert is active
    /// </summary>
    public bool IsActive => Status == AlertStatus.Active;

    /// <summary>
    /// Gets a value indicating whether the alert is acknowledged
    /// </summary>
    public bool IsAcknowledged => Status == AlertStatus.Acknowledged || Status == AlertStatus.Resolved;

    /// <summary>
    /// Gets a value indicating whether the alert is resolved
    /// </summary>
    public bool IsResolved => Status == AlertStatus.Resolved;

    /// <summary>
    /// Gets a value indicating whether the alert is expired
    /// </summary>
    public bool IsExpired => ExpiryDate.HasValue && ExpiryDate.Value < DateTime.UtcNow;

    /// <summary>
    /// Gets a value indicating whether the alert requires immediate attention
    /// </summary>
    public bool RequiresImmediateAttention => Severity == AlertSeverity.Critical && IsActive;

    /// <summary>
    /// Gets the age of the alert in hours
    /// </summary>
    public double AgeInHours => (DateTime.UtcNow - CreatedAt).TotalHours;

    /// <summary>
    /// Gets the time taken to acknowledge the alert in minutes
    /// </summary>
    public double? AcknowledgmentTimeMinutes => AcknowledgedDate.HasValue ? 
        (AcknowledgedDate.Value - CreatedAt).TotalMinutes : null;

    /// <summary>
    /// Gets the time taken to resolve the alert in minutes
    /// </summary>
    public double? ResolutionTimeMinutes => ResolvedDate.HasValue ? 
        (ResolvedDate.Value - CreatedAt).TotalMinutes : null;

    /// <summary>
    /// Private constructor for Entity Framework
    /// </summary>
    private Alert() { }

    /// <summary>
    /// Initializes a new instance of the <see cref="Alert"/> class
    /// </summary>
    /// <param name="alertType">The alert type</param>
    /// <param name="severity">The alert severity</param>
    /// <param name="message">The alert message</param>
    /// <param name="vehicleId">The vehicle ID (optional)</param>
    /// <param name="driverId">The driver ID (optional)</param>
    /// <param name="location">The location where the alert occurred (optional)</param>
    public Alert(AlertType alertType, AlertSeverity severity, string message, 
        Guid? vehicleId = null, Guid? driverId = null, Location? location = null)
    {
        if (string.IsNullOrWhiteSpace(message))
            throw new ArgumentException("Alert message cannot be empty", nameof(message));

        AlertType = alertType;
        Severity = severity;
        Message = message;
        VehicleId = vehicleId;
        DriverId = driverId;
        Location = location;
        Status = AlertStatus.Active;

        // Set expiry date based on severity
        ExpiryDate = severity switch
        {
            AlertSeverity.Critical => DateTime.UtcNow.AddHours(1),
            AlertSeverity.High => DateTime.UtcNow.AddHours(4),
            AlertSeverity.Medium => DateTime.UtcNow.AddHours(24),
            AlertSeverity.Low => DateTime.UtcNow.AddDays(7),
            _ => null
        };

        AddDomainEvent(new AlertTriggeredEvent(Id, alertType, severity, message, vehicleId, driverId, location));
    }

    /// <summary>
    /// Creates a speed-related alert
    /// </summary>
    /// <param name="vehicleId">The vehicle ID</param>
    /// <param name="driverId">The driver ID</param>
    /// <param name="currentSpeed">The current speed</param>
    /// <param name="speedLimit">The speed limit</param>
    /// <param name="location">The location</param>
    /// <returns>A new speed alert</returns>
    public static Alert CreateSpeedAlert(Guid vehicleId, Guid? driverId, Speed currentSpeed, Speed speedLimit, Location location)
    {
        var percentage = currentSpeed.PercentageOverLimit(speedLimit);
        var severity = percentage switch
        {
            > 50 => AlertSeverity.Critical,
            > 25 => AlertSeverity.High,
            > 10 => AlertSeverity.Medium,
            _ => AlertSeverity.Low
        };

        var message = $"Speed violation detected: {currentSpeed} in {speedLimit} zone ({percentage:F1}% over limit)";
        return new Alert(AlertType.Speed, severity, message, vehicleId, driverId, location);
    }

    /// <summary>
    /// Creates a fuel-related alert
    /// </summary>
    /// <param name="vehicleId">The vehicle ID</param>
    /// <param name="fuelLevel">The current fuel level</param>
    /// <param name="location">The location</param>
    /// <returns>A new fuel alert</returns>
    public static Alert CreateFuelAlert(Guid vehicleId, FuelLevel fuelLevel, Location? location = null)
    {
        var severity = fuelLevel.GetCategory() switch
        {
            FuelLevelCategory.Empty => AlertSeverity.Critical,
            FuelLevelCategory.Critical => AlertSeverity.Critical,
            FuelLevelCategory.Low => AlertSeverity.High,
            _ => AlertSeverity.Medium
        };

        var message = $"Fuel level alert: {fuelLevel} ({fuelLevel.GetCategory()})";
        return new Alert(AlertType.Fuel, severity, message, vehicleId, null, location);
    }

    /// <summary>
    /// Creates a maintenance-related alert
    /// </summary>
    /// <param name="vehicleId">The vehicle ID</param>
    /// <param name="maintenanceType">The type of maintenance required</param>
    /// <param name="dueDate">The maintenance due date</param>
    /// <returns>A new maintenance alert</returns>
    public static Alert CreateMaintenanceAlert(Guid vehicleId, string maintenanceType, DateTime dueDate)
    {
        var daysUntilDue = (dueDate - DateTime.UtcNow).TotalDays;
        var severity = daysUntilDue switch
        {
            < 0 => AlertSeverity.Critical, // Overdue
            < 1 => AlertSeverity.High,     // Due today
            < 7 => AlertSeverity.Medium,   // Due this week
            _ => AlertSeverity.Low         // Due later
        };

        var message = daysUntilDue < 0 
            ? $"Maintenance overdue: {maintenanceType} (overdue by {Math.Abs(daysUntilDue):F0} days)"
            : $"Maintenance due: {maintenanceType} (due in {daysUntilDue:F0} days)";

        return new Alert(AlertType.Maintenance, severity, message, vehicleId);
    }

    /// <summary>
    /// Creates a harsh driving alert
    /// </summary>
    /// <param name="vehicleId">The vehicle ID</param>
    /// <param name="driverId">The driver ID</param>
    /// <param name="eventType">The type of harsh driving event</param>
    /// <param name="location">The location</param>
    /// <returns>A new harsh driving alert</returns>
    public static Alert CreateHarshDrivingAlert(Guid vehicleId, Guid driverId, string eventType, Location location)
    {
        var message = $"Harsh driving detected: {eventType}";
        return new Alert(AlertType.HarshDriving, AlertSeverity.Medium, message, vehicleId, driverId, location);
    }

    /// <summary>
    /// Acknowledges the alert
    /// </summary>
    /// <param name="acknowledgedBy">The user who acknowledged the alert</param>
    /// <param name="notes">Optional acknowledgment notes</param>
    public void Acknowledge(Guid acknowledgedBy, string? notes = null)
    {
        if (Status != AlertStatus.Active)
            throw new InvalidOperationException("Only active alerts can be acknowledged");

        if (acknowledgedBy == Guid.Empty)
            throw new ArgumentException("Acknowledged by user ID cannot be empty", nameof(acknowledgedBy));

        Status = AlertStatus.Acknowledged;
        AcknowledgedDate = DateTime.UtcNow;
        AcknowledgedBy = acknowledgedBy;
        AcknowledgmentNotes = notes;
    }

    /// <summary>
    /// Resolves the alert
    /// </summary>
    /// <param name="resolvedBy">The user who resolved the alert</param>
    /// <param name="notes">Optional resolution notes</param>
    public void Resolve(Guid resolvedBy, string? notes = null)
    {
        if (Status == AlertStatus.Resolved)
            throw new InvalidOperationException("Alert is already resolved");

        if (resolvedBy == Guid.Empty)
            throw new ArgumentException("Resolved by user ID cannot be empty", nameof(resolvedBy));

        Status = AlertStatus.Resolved;
        ResolvedDate = DateTime.UtcNow;
        ResolvedBy = resolvedBy;
        ResolutionNotes = notes;

        // If not previously acknowledged, set acknowledgment info
        if (!AcknowledgedDate.HasValue)
        {
            AcknowledgedDate = DateTime.UtcNow;
            AcknowledgedBy = resolvedBy;
        }
    }

    /// <summary>
    /// Updates the alert message
    /// </summary>
    /// <param name="newMessage">The new message</param>
    public void UpdateMessage(string newMessage)
    {
        if (string.IsNullOrWhiteSpace(newMessage))
            throw new ArgumentException("Alert message cannot be empty", nameof(newMessage));

        Message = newMessage;
    }

    /// <summary>
    /// Updates the alert severity
    /// </summary>
    /// <param name="newSeverity">The new severity</param>
    public void UpdateSeverity(AlertSeverity newSeverity)
    {
        if (Status == AlertStatus.Resolved)
            throw new InvalidOperationException("Cannot update severity of resolved alert");

        Severity = newSeverity;

        // Update expiry date based on new severity
        if (Status == AlertStatus.Active)
        {
            ExpiryDate = newSeverity switch
            {
                AlertSeverity.Critical => DateTime.UtcNow.AddHours(1),
                AlertSeverity.High => DateTime.UtcNow.AddHours(4),
                AlertSeverity.Medium => DateTime.UtcNow.AddHours(24),
                AlertSeverity.Low => DateTime.UtcNow.AddDays(7),
                _ => null
            };
        }
    }

    /// <summary>
    /// Sets metadata for the alert
    /// </summary>
    /// <param name="metadata">The metadata as JSON string</param>
    public void SetMetadata(string metadata)
    {
        Metadata = metadata;
    }

    /// <summary>
    /// Extends the expiry date of the alert
    /// </summary>
    /// <param name="additionalHours">Additional hours to extend</param>
    public void ExtendExpiry(int additionalHours)
    {
        if (additionalHours <= 0)
            throw new ArgumentException("Additional hours must be positive", nameof(additionalHours));

        if (Status == AlertStatus.Resolved)
            throw new InvalidOperationException("Cannot extend expiry of resolved alert");

        ExpiryDate = (ExpiryDate ?? DateTime.UtcNow).AddHours(additionalHours);
    }

    /// <summary>
    /// Checks if the alert should be auto-resolved based on conditions
    /// </summary>
    /// <returns>True if the alert should be auto-resolved</returns>
    public bool ShouldAutoResolve()
    {
        return IsExpired && Status == AlertStatus.Active;
    }
}
