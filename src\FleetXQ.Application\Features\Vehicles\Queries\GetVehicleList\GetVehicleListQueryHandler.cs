using AutoMapper;
using FleetXQ.Application.Features.Vehicles.DTOs;
using FleetXQ.Domain.Interfaces;
using MediatR;
using Microsoft.Extensions.Logging;

namespace FleetXQ.Application.Features.Vehicles.Queries.GetVehicleList;

/// <summary>
/// Handler for GetVehicleListQuery
/// </summary>
public sealed class GetVehicleListQueryHandler : IRequestHandler<GetVehicleListQuery, GetVehicleListResult>
{
    private readonly IVehicleRepository _vehicleRepository;
    private readonly IMapper _mapper;
    private readonly ILogger<GetVehicleListQueryHandler> _logger;

    /// <summary>
    /// Initializes a new instance of the <see cref="GetVehicleListQueryHandler"/> class
    /// </summary>
    /// <param name="vehicleRepository">The vehicle repository</param>
    /// <param name="mapper">The AutoMapper instance</param>
    /// <param name="logger">The logger</param>
    public GetVehicleListQueryHandler(
        IVehicleRepository vehicleRepository,
        I<PERSON><PERSON>per mapper,
        ILogger<GetVehicleListQueryHandler> logger)
    {
        _vehicleRepository = vehicleRepository ?? throw new ArgumentNullException(nameof(vehicleRepository));
        _mapper = mapper ?? throw new ArgumentNullException(nameof(mapper));
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
    }

    /// <summary>
    /// Handles the GetVehicleListQuery
    /// </summary>
    /// <param name="request">The query request</param>
    /// <param name="cancellationToken">The cancellation token</param>
    /// <returns>The query result</returns>
    public async Task<GetVehicleListResult> Handle(GetVehicleListQuery request, CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogInformation("Getting vehicle list with filters - Status: {Status}, Type: {VehicleType}, AvailableOnly: {AvailableOnly}, Page: {PageNumber}, Size: {PageSize}", 
                request.Status, request.VehicleType, request.AvailableOnly, request.PageNumber, request.PageSize);

            // Get vehicles based on filters
            IEnumerable<Domain.Entities.Vehicle> vehicles;

            if (request.Status.HasValue)
            {
                vehicles = await _vehicleRepository.GetByStatusAsync(request.Status.Value, cancellationToken);
            }
            else if (!string.IsNullOrWhiteSpace(request.VehicleType))
            {
                vehicles = await _vehicleRepository.GetByTypeAsync(request.VehicleType, cancellationToken);
            }
            else if (request.AvailableOnly == true)
            {
                vehicles = await _vehicleRepository.GetAvailableVehiclesAsync(cancellationToken);
            }
            else
            {
                vehicles = await _vehicleRepository.GetAllAsync(cancellationToken);
            }

            // Apply search filter if provided
            if (!string.IsNullOrWhiteSpace(request.SearchTerm))
            {
                var searchTerm = request.SearchTerm.ToLowerInvariant();
                vehicles = vehicles.Where(v => 
                    v.VehicleName.ToLowerInvariant().Contains(searchTerm) ||
                    v.LicensePlate.ToLowerInvariant().Contains(searchTerm) ||
                    (v.Brand?.ToLowerInvariant().Contains(searchTerm) == true) ||
                    (v.Model?.ToLowerInvariant().Contains(searchTerm) == true));
            }

            // Apply additional filters
            if (request.AvailableOnly == true && !request.Status.HasValue)
            {
                vehicles = vehicles.Where(v => v.IsAvailable);
            }

            // Apply sorting
            vehicles = ApplySorting(vehicles, request.SortBy, request.SortDescending);

            var totalCount = vehicles.Count();

            // Apply pagination
            var pagedVehicles = vehicles
                .Skip((request.PageNumber - 1) * request.PageSize)
                .Take(request.PageSize)
                .ToList();

            // Map to DTOs
            var vehicleDtos = _mapper.Map<IEnumerable<VehicleListDto>>(pagedVehicles);

            _logger.LogInformation("Successfully retrieved {Count} vehicles out of {TotalCount} total", 
                pagedVehicles.Count, totalCount);

            return GetVehicleListResult.CreateSuccess(vehicleDtos, totalCount, request.PageNumber, request.PageSize);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting vehicle list");
            return GetVehicleListResult.Failed($"An error occurred while retrieving the vehicle list: {ex.Message}");
        }
    }

    /// <summary>
    /// Applies sorting to the vehicle collection
    /// </summary>
    /// <param name="vehicles">The vehicles to sort</param>
    /// <param name="sortBy">The field to sort by</param>
    /// <param name="sortDescending">Whether to sort in descending order</param>
    /// <returns>The sorted vehicles</returns>
    private static IEnumerable<Domain.Entities.Vehicle> ApplySorting(
        IEnumerable<Domain.Entities.Vehicle> vehicles, 
        string sortBy, 
        bool sortDescending)
    {
        return sortBy.ToLowerInvariant() switch
        {
            "vehiclename" => sortDescending 
                ? vehicles.OrderByDescending(v => v.VehicleName)
                : vehicles.OrderBy(v => v.VehicleName),
            "licenseplate" => sortDescending 
                ? vehicles.OrderByDescending(v => v.LicensePlate)
                : vehicles.OrderBy(v => v.LicensePlate),
            "vehicletype" => sortDescending 
                ? vehicles.OrderByDescending(v => v.VehicleType)
                : vehicles.OrderBy(v => v.VehicleType),
            "brand" => sortDescending 
                ? vehicles.OrderByDescending(v => v.Brand ?? string.Empty)
                : vehicles.OrderBy(v => v.Brand ?? string.Empty),
            "model" => sortDescending 
                ? vehicles.OrderByDescending(v => v.Model ?? string.Empty)
                : vehicles.OrderBy(v => v.Model ?? string.Empty),
            "status" => sortDescending 
                ? vehicles.OrderByDescending(v => v.Status)
                : vehicles.OrderBy(v => v.Status),
            "createdat" => sortDescending 
                ? vehicles.OrderByDescending(v => v.CreatedAt)
                : vehicles.OrderBy(v => v.CreatedAt),
            "updatedat" => sortDescending 
                ? vehicles.OrderByDescending(v => v.UpdatedAt)
                : vehicles.OrderBy(v => v.UpdatedAt),
            "currentmileage" => sortDescending 
                ? vehicles.OrderByDescending(v => v.CurrentMileage)
                : vehicles.OrderBy(v => v.CurrentMileage),
            _ => vehicles.OrderBy(v => v.VehicleName)
        };
    }
}
