namespace FleetXQ.Domain.Enums;

/// <summary>
/// Represents the severity level of an alert
/// </summary>
public enum AlertSeverity
{
    /// <summary>
    /// Low severity alert
    /// </summary>
    Low = 1,

    /// <summary>
    /// Medium severity alert
    /// </summary>
    Medium = 2,

    /// <summary>
    /// High severity alert
    /// </summary>
    High = 3,

    /// <summary>
    /// Critical severity alert
    /// </summary>
    Critical = 4
}
