---
type: "agent_requested"
description: "Example description"
---
# Prompt Completion Summary Rule

## Trigger Conditions
This rule activates when:
- A prompt session from the `prompts/` directory has been completed
- All tasks related to the prompt have been marked as complete
- The user indicates the prompt work is finished

## Rule Behavior

### Step 1: Detection and Confirmation
When a prompt completion is detected, ask the user:

> "Would you like me to create a comprehensive summary document for this completed prompt session? 
> 
> The summary would include:
> - Overview of objectives and what was accomplished
> - Step-by-step walkthrough of implementation in future tense
> - Technical decisions and their rationale
> - Challenges encountered and resolutions
> - Validation that requirements were met
> - Foundation created for future development

Create a comprehensive summary document for the completed "Prompt X.X: -" task. Follow these specific requirements:

1. **File Location**: Create a new file at `summaries/Backend Prompt X.X.md`

2. **Content Scope**: Document only the work completed in this current conversation session, which covers the execution of "Prompt X.X: " from the `prompts/x-prompts.md` file.

3. **Writing Style**: Write the content in a narrative, spoken format as if it's being read aloud during a technical walkthrough or lecture recording. Use:
   - FUTURE tense when describing what was accomplished
   - Clear transitions between topics
   - Explanatory language that would help someone understand the "why" behind each decision
   - Professional but conversational tone suitable for a technical presentation

4. **Required Sections to Include**:
   - Overview of the task and objectives
   - Step-by-step walkthrough of what was implemented
   - Key engineering decisions made and their rationale (e.g., why certain NuGet packages were chosen, why specific folder structures were created)
   - Technical challenges encountered and how they were resolved
   - Final validation that all requirements were met
   - Brief mention of what this foundation enables for future development

5. **Technical Details**: Include specific information about:
   - Solution structure created
   - Project references and Clean Architecture compliance
   - NuGet packages installed per layer with justification
   - Directory.Build.props configuration choices
   - Namespace conventions established
   - Initial foundation files created

6. **Format**: Use Markdown formatting with appropriate headers, code blocks, and bullet points to make it readable and well-structured.
