
# backend.md

## .NET 8 Web API Architecture (Clean Architecture, DB-First EF Core)

This document outlines the architecture of a .NET 8 backend Web API built on Clean Architecture principles, using a database-first EF Core model. This guide is designed for AI coding assistants and developers working within this structure.

---

## 1. Project Structure

```
/src
  /Project.Api
  /Project.Application
  /Project.Domain
  /Project.Infrastructure

/tests
  /Project.Application.Tests
  /Project.Infrastructure.Tests
```

### Responsibilities

- `Api`: HTTP pipeline, controllers, request contracts, SignalR hubs, middleware.
- `Application`: CQRS (commands/queries), DTOs, interfaces, validators, business services.
- `Domain`: Entities, value objects, domain events, enums, core business rules.
- `Infrastructure`: EF Core DB-first context, repository implementations, external service integrations.

---

## 2. EF Core: Database-First Approach

- Existing relational schema is preserved.
- Models are scaffolded using:
  ```
  dotnet ef dbcontext scaffold "YourConnectionString" Pomelo.EntityFrameworkCore.MySql -o Models --data-annotations
  ```
- Data annotations are used for attributes like `[Key]`, `[Table]`, `[Column]`.

### Why Not Code-First?

- Legacy schema already exists in production.
- Maintaining alignment with existing normalized tables and constraints.
- Reduces risk of model drift in regulated environments.

---

## 3. CQRS with MediatR

- MediatR decouples requests from processing logic.

```
/Application/Telemetry/Commands/
  - ProcessTelemetryCommand.cs
  - ProcessTelemetryCommandHandler.cs

/Application/Telemetry/Queries/
  - GetLatestReadingsQuery.cs
  - GetLatestReadingsQueryHandler.cs
```

- Handlers contain all orchestration logic.
- Validators use FluentValidation and execute via pipeline behavior.

---

## 4. Services and Repositories

### Services

- Contain domain logic, orchestrate repositories and external services.
- Registered in `Api` project for dependency injection.

### Repositories

- Implement interfaces in `Domain`.
- Wrap EF Core DbContext and provide data access methods.

```csharp
public interface IDeviceRepository
{
    Task<Device?> GetByIdAsync(Guid id);
    Task SaveAsync(Device device);
}
```

```csharp
public class DeviceRepository : IDeviceRepository
{
    private readonly TelemetryDbContext _context;
    ...
}
```

---

## 5. Domain Events

### Events

- `TelemetryReceived`
- `AlertTriggered`

### Handling

- Raised in Domain layer (`Entity.AddDomainEvent(...)`)
- Dispatched post-save in Infrastructure via mediator.
- Used for:
  - Logging
  - Notification dispatch
  - Telemetry analysis

---

## 6. Real-Time Updates with SignalR

- SignalR Hub in `Api` layer.
- Pushes telemetry changes and alerts to connected clients.

```csharp
public class TelemetryHub : Hub
{
    public async Task SubscribeToDevice(Guid deviceId)
    {
        await Groups.AddToGroupAsync(Context.ConnectionId, deviceId.ToString());
    }
}
```

- Events like `AlertTriggered` invoke SignalR hub via injected `IHubContext<TelemetryHub>`.

---

## 7. Authentication and Authorization

- JWT tokens issued at login via IdentityService.
- Token contains user ID, role claim (`admin`, `staff`).

```json
{
  "sub": "user-id-guid",
  "role": "admin"
}
```

- Role-based access enforced using `[Authorize(Roles = "admin")]`.

---

## 8. Validation and Exception Handling

### Validation

- All commands and queries validated using FluentValidation.
- Validators registered via `services.AddValidatorsFromAssembly(...)`.

### Exception Handling

- Global exception middleware in `Api` project.
- Logs using Serilog, returns standard problem details.

```csharp
app.UseMiddleware<ExceptionHandlingMiddleware>();
```

---

## 9. Logging (Serilog)

- Structured logging via Serilog with sinks to file, console, and optionally ElasticSearch.
- Context-rich logs for telemetry, auth, and errors.

```csharp
Log.Information("Telemetry received: {@TelemetryDto}", telemetry);
```

---

## 10. Unit Testing Strategy

### Frameworks

- `xUnit`: Test runner
- `Moq`: Mocking dependencies
- `Shouldly` or `FluentAssertions`: Assertions

### Structure

```
/tests/Project.Application.Tests/
  /Telemetry/
    - ProcessTelemetryCommandHandlerTests.cs
    - GetLatestReadingsQueryHandlerTests.cs
```

- Handlers are tested in isolation.
- Mocks used for repositories, logger, and SignalR hub.

---

## 11. Code Style and Conventions

### File & Folder Structure

- Use PascalCase for folders: `Commands`, `Queries`, `Services`, `Repositories`.
- One class/interface per file. File name must match class/interface name.

### Naming Conventions

| Item                  | Convention          |
|-----------------------|---------------------|
| Classes               | PascalCase          |
| Interfaces            | IPrefix + PascalCase|
| Methods               | PascalCase          |
| Variables             | camelCase           |
| Constants             | PascalCase          |
| Enums                 | PascalCase          |
| DTOs/Models           | Suffix with `Dto`   |
| Commands/Queries      | Suffix with `Command` / `Query`|
| Repositories/Services | Suffix with `Repository` / `Service`|

### Namespace Structure

```
Project.Application.[Feature].[Type]
Project.Domain.[Feature]
Project.Infrastructure.[Concern]
Project.Api.Controllers
```

### Language Rules

- Prefer expression-bodied members if readable.
- Use pattern matching, `switch` expressions.
- Enforce nullability (`<Nullable>enable</Nullable>`).
- Avoid magic strings/numbers — use constants and enums.

---

## 12. Dependency Injection

```csharp
builder.Services.AddScoped<IDeviceRepository, DeviceRepository>();
builder.Services.AddScoped<IUnitOfWork, UnitOfWork>();
builder.Services.AddScoped<IDeviceService, DeviceService>();
builder.Services.AddMediatR(cfg => cfg.RegisterServicesFromAssembly(typeof(ApplicationAssembly).Assembly));
```

---

## 13. Build and Lint Configuration

Use `Directory.Build.props` to apply consistent settings across all projects:

```xml
<Project>
  <PropertyGroup>
    <LangVersion>latest</LangVersion>
    <Nullable>enable</Nullable>
    <TreatWarningsAsErrors>true</TreatWarningsAsErrors>
  </PropertyGroup>
</Project>
```

---

## 14. Summary

| Concern                | Approach                                |
|------------------------|------------------------------------------|
| Architecture           | Clean Architecture                      |
| DB Modeling            | Database-First EF Core                  |
| Logic Delegation       | CQRS with MediatR                       |
| Data Access            | Repository pattern over DbContext       |
| Domain Communication   | Domain Events                           |
| Real-Time Push         | SignalR Hub                             |
| AuthN/AuthZ            | JWT + Role-based Authorization          |
| Logging                | Serilog (structured logging)            |
| Validation             | FluentValidation                        |
| Testing                | xUnit + Moq                             |
