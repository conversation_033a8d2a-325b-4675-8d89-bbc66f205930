using FleetXQ.Api.Models;
using FleetXQ.Application.Features.Vehicles.Commands.CreateVehicle;
using FleetXQ.Application.Features.Vehicles.Commands.UpdateVehicleStatus;
using FleetXQ.Application.Features.Vehicles.Queries.GetVehicleById;
using FleetXQ.Application.Features.Vehicles.Queries.GetVehicleList;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Swashbuckle.AspNetCore.Annotations;

namespace FleetXQ.Api.Controllers;

/// <summary>
/// Vehicle management controller
/// </summary>
[ApiController]
[Route("api/[controller]")]
[Authorize]
[Produces("application/json")]
public class VehiclesController : BaseApiController
{
    /// <summary>
    /// Gets a list of vehicles with optional filtering and pagination
    /// </summary>
    /// <param name="status">Filter by vehicle status</param>
    /// <param name="vehicleType">Filter by vehicle type</param>
    /// <param name="availableOnly">Filter to show only available vehicles</param>
    /// <param name="searchTerm">Search term for vehicle name, license plate, brand, or model</param>
    /// <param name="sortBy">Sort field (VehicleName, LicensePlate, Status, CreatedAt)</param>
    /// <param name="sortDescending">Sort in descending order</param>
    /// <param name="pageNumber">Page number (default: 1)</param>
    /// <param name="pageSize">Page size (default: 10, max: 100)</param>
    /// <param name="cancellationToken">The cancellation token</param>
    /// <returns>Paginated list of vehicles</returns>
    [HttpGet]
    [Authorize(Roles = "Admin,Manager,Driver,User")]
    [SwaggerOperation(
        Summary = "Get vehicles list",
        Description = "Retrieves a paginated list of vehicles with optional filtering"
    )]
    [SwaggerResponse(200, "Vehicles retrieved successfully", typeof(PaginatedApiResponse<object>))]
    [SwaggerResponse(400, "Invalid request parameters", typeof(ApiResponse<object>))]
    [SwaggerResponse(401, "Unauthorized", typeof(ApiResponse<object>))]
    [SwaggerResponse(500, "Internal server error", typeof(ApiResponse<object>))]
    public async Task<ActionResult<PaginatedApiResponse<object>>> GetVehicles(
        [FromQuery] string? status = null,
        [FromQuery] string? vehicleType = null,
        [FromQuery] bool? availableOnly = null,
        [FromQuery] string? searchTerm = null,
        [FromQuery] string? sortBy = null,
        [FromQuery] bool sortDescending = false,
        [FromQuery] int pageNumber = 1,
        [FromQuery] int pageSize = 10,
        CancellationToken cancellationToken = default)
    {
        // Validate pagination parameters
        if (pageNumber < 1)
            return BadRequest(Error("Page number must be greater than 0"));

        if (pageSize < 1 || pageSize > 100)
            return BadRequest(Error("Page size must be between 1 and 100"));

        var query = new GetVehicleListQuery
        {
            Status = !string.IsNullOrWhiteSpace(status) && Enum.TryParse<FleetXQ.Domain.Enums.VehicleStatus>(status, true, out var statusEnum) ? statusEnum : null,
            VehicleType = vehicleType,
            AvailableOnly = availableOnly,
            SearchTerm = searchTerm,
            SortBy = sortBy ?? "VehicleName",
            SortDescending = sortDescending,
            PageNumber = pageNumber,
            PageSize = pageSize
        };

        var result = await Mediator.Send(query, cancellationToken);

        if (!result.Success)
        {
            return BadRequest(Error(result.ErrorMessage ?? "Failed to retrieve vehicles"));
        }

        return Ok(PaginatedApiResponse<object>.Success(
            result.Vehicles ?? Enumerable.Empty<object>(),
            result.PageNumber,
            result.PageSize,
            result.TotalCount,
            "Vehicles retrieved successfully"
        ));
    }

    /// <summary>
    /// Gets a specific vehicle by ID
    /// </summary>
    /// <param name="id">The vehicle ID</param>
    /// <param name="cancellationToken">The cancellation token</param>
    /// <returns>Vehicle details</returns>
    [HttpGet("{id:guid}")]
    [Authorize(Roles = "Admin,Manager,Driver,User")]
    [SwaggerOperation(
        Summary = "Get vehicle by ID",
        Description = "Retrieves detailed information about a specific vehicle"
    )]
    [SwaggerResponse(200, "Vehicle retrieved successfully", typeof(ApiResponse<object>))]
    [SwaggerResponse(404, "Vehicle not found", typeof(ApiResponse<object>))]
    [SwaggerResponse(401, "Unauthorized", typeof(ApiResponse<object>))]
    [SwaggerResponse(500, "Internal server error", typeof(ApiResponse<object>))]
    public async Task<ActionResult<ApiResponse<object>>> GetVehicle(
        [FromRoute] Guid id,
        CancellationToken cancellationToken = default)
    {
        var query = new GetVehicleByIdQuery(id);
        var result = await Mediator.Send(query, cancellationToken);

        if (!result.Success)
        {
            return NotFound(Error(result.ErrorMessage ?? "Vehicle not found"));
        }

        return Ok(Success(result.Vehicle, "Vehicle retrieved successfully"));
    }

    /// <summary>
    /// Creates a new vehicle
    /// </summary>
    /// <param name="request">The vehicle creation request</param>
    /// <param name="cancellationToken">The cancellation token</param>
    /// <returns>Created vehicle information</returns>
    [HttpPost]
    [Authorize(Roles = "Admin,Manager")]
    [SwaggerOperation(
        Summary = "Create new vehicle",
        Description = "Creates a new vehicle in the fleet (Admin/Manager only)"
    )]
    [SwaggerResponse(201, "Vehicle created successfully", typeof(ApiResponse<object>))]
    [SwaggerResponse(400, "Invalid vehicle data or validation error", typeof(ApiResponse<object>))]
    [SwaggerResponse(401, "Unauthorized", typeof(ApiResponse<object>))]
    [SwaggerResponse(403, "Forbidden - insufficient permissions", typeof(ApiResponse<object>))]
    [SwaggerResponse(409, "Conflict - vehicle with same license plate exists", typeof(ApiResponse<object>))]
    [SwaggerResponse(500, "Internal server error", typeof(ApiResponse<object>))]
    public async Task<ActionResult<ApiResponse<object>>> CreateVehicle(
        [FromBody] CreateVehicleCommand request,
        CancellationToken cancellationToken = default)
    {
        var result = await Mediator.Send(request, cancellationToken);

        if (!result.Success)
        {
            // Check if it's a duplicate license plate error
            if (result.ErrorMessage?.Contains("license plate", StringComparison.OrdinalIgnoreCase) == true)
            {
                return Conflict(ApiResponse<object>.Conflict(result.ErrorMessage));
            }

            return BadRequest(Error(result.ErrorMessage ?? "Failed to create vehicle"));
        }

        return StatusCode(201, ApiResponse<object>.Created(
            new { VehicleId = result.VehicleId },
            "Vehicle created successfully"
        ));
    }

    /// <summary>
    /// Updates a vehicle's status
    /// </summary>
    /// <param name="id">The vehicle ID</param>
    /// <param name="request">The status update request</param>
    /// <param name="cancellationToken">The cancellation token</param>
    /// <returns>Status update confirmation</returns>
    [HttpPut("{id:guid}/status")]
    [Authorize(Roles = "Admin,Manager")]
    [SwaggerOperation(
        Summary = "Update vehicle status",
        Description = "Updates the status of a specific vehicle (Admin/Manager only)"
    )]
    [SwaggerResponse(200, "Vehicle status updated successfully", typeof(ApiResponse<object>))]
    [SwaggerResponse(400, "Invalid status or validation error", typeof(ApiResponse<object>))]
    [SwaggerResponse(401, "Unauthorized", typeof(ApiResponse<object>))]
    [SwaggerResponse(403, "Forbidden - insufficient permissions", typeof(ApiResponse<object>))]
    [SwaggerResponse(404, "Vehicle not found", typeof(ApiResponse<object>))]
    [SwaggerResponse(500, "Internal server error", typeof(ApiResponse<object>))]
    public async Task<ActionResult<ApiResponse<object>>> UpdateVehicleStatus(
        [FromRoute] Guid id,
        [FromBody] UpdateVehicleStatusRequest request,
        CancellationToken cancellationToken = default)
    {
        var command = new UpdateVehicleStatusCommand
        {
            VehicleId = id,
            Status = request.Status,
            Reason = request.Reason,
            ChangedBy = CurrentUserId,
            Context = request.Context
        };

        var result = await Mediator.Send(command, cancellationToken);

        if (!result.Success)
        {
            if (result.ErrorMessage?.Contains("not found", StringComparison.OrdinalIgnoreCase) == true)
            {
                return NotFound(Error(result.ErrorMessage));
            }

            return BadRequest(Error(result.ErrorMessage ?? "Failed to update vehicle status"));
        }

        return Ok(Success(new
        {
            VehicleId = result.VehicleId,
            PreviousStatus = result.PreviousStatus,
            NewStatus = result.NewStatus
        }, "Vehicle status updated successfully"));
    }
}

/// <summary>
/// Request model for updating vehicle status
/// </summary>
public class UpdateVehicleStatusRequest
{
    /// <summary>
    /// Gets or sets the new vehicle status
    /// </summary>
    public FleetXQ.Domain.Enums.VehicleStatus Status { get; set; }

    /// <summary>
    /// Gets or sets the reason for the status change
    /// </summary>
    public string? Reason { get; set; }

    /// <summary>
    /// Gets or sets additional context for the status change
    /// </summary>
    public string? Context { get; set; }
}
