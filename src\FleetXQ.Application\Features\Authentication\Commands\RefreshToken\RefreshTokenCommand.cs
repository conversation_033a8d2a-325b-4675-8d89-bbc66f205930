using MediatR;

namespace FleetXQ.Application.Features.Authentication.Commands.RefreshToken;

/// <summary>
/// Command to refresh an access token using a refresh token
/// </summary>
public sealed class RefreshTokenCommand : IRequest<RefreshTokenResult>
{
    /// <summary>
    /// Gets or sets the access token
    /// </summary>
    public string AccessToken { get; set; } = string.Empty;

    /// <summary>
    /// Gets or sets the refresh token
    /// </summary>
    public string RefreshToken { get; set; } = string.Empty;

    /// <summary>
    /// Gets or sets the client IP address
    /// </summary>
    public string? ClientIpAddress { get; set; }

    /// <summary>
    /// Gets or sets the user agent
    /// </summary>
    public string? UserAgent { get; set; }
}

/// <summary>
/// Result of token refresh
/// </summary>
public sealed class RefreshTokenResult
{
    /// <summary>
    /// Gets or sets a value indicating whether the refresh was successful
    /// </summary>
    public bool Success { get; set; }

    /// <summary>
    /// Gets or sets the error message if refresh failed
    /// </summary>
    public string? ErrorMessage { get; set; }

    /// <summary>
    /// Gets or sets the new access token
    /// </summary>
    public string? AccessToken { get; set; }

    /// <summary>
    /// Gets or sets the new refresh token
    /// </summary>
    public string? RefreshToken { get; set; }

    /// <summary>
    /// Gets or sets the token expiry time
    /// </summary>
    public DateTime? TokenExpiry { get; set; }

    /// <summary>
    /// Gets or sets the user information
    /// </summary>
    public UserTokenInfo? User { get; set; }

    /// <summary>
    /// Creates a successful refresh result
    /// </summary>
    /// <param name="accessToken">The new access token</param>
    /// <param name="refreshToken">The new refresh token</param>
    /// <param name="tokenExpiry">The token expiry</param>
    /// <param name="user">The user information</param>
    /// <returns>A successful result</returns>
    public static RefreshTokenResult Successful(string accessToken, string refreshToken, DateTime tokenExpiry, UserTokenInfo user)
    {
        return new RefreshTokenResult
        {
            Success = true,
            AccessToken = accessToken,
            RefreshToken = refreshToken,
            TokenExpiry = tokenExpiry,
            User = user
        };
    }

    /// <summary>
    /// Creates a failed refresh result
    /// </summary>
    /// <param name="errorMessage">The error message</param>
    /// <returns>A failed result</returns>
    public static RefreshTokenResult Failed(string errorMessage)
    {
        return new RefreshTokenResult
        {
            Success = false,
            ErrorMessage = errorMessage
        };
    }
}

/// <summary>
/// User information for token refresh response
/// </summary>
public sealed class UserTokenInfo
{
    /// <summary>
    /// Gets or sets the user ID
    /// </summary>
    public Guid Id { get; set; }

    /// <summary>
    /// Gets or sets the username
    /// </summary>
    public string Username { get; set; } = string.Empty;

    /// <summary>
    /// Gets or sets the user role
    /// </summary>
    public string Role { get; set; } = string.Empty;
}
