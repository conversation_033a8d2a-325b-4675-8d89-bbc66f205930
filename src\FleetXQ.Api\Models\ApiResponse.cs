using System.Text.Json.Serialization;

namespace FleetXQ.Api.Models;

/// <summary>
/// Standard API response wrapper
/// </summary>
/// <typeparam name="T">The type of data being returned</typeparam>
public class ApiResponse<T>
{
    /// <summary>
    /// Gets or sets a value indicating whether the request was successful
    /// </summary>
    public bool Success { get; set; }

    /// <summary>
    /// Gets or sets the response message
    /// </summary>
    public string? Message { get; set; }

    /// <summary>
    /// Gets or sets the response data
    /// </summary>
    public T? Data { get; set; }

    /// <summary>
    /// Gets or sets validation errors or additional error details
    /// </summary>
    public object? Errors { get; set; }

    /// <summary>
    /// Gets or sets the HTTP status code
    /// </summary>
    public int StatusCode { get; set; }

    /// <summary>
    /// Gets or sets the timestamp of the response
    /// </summary>
    public DateTime Timestamp { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// Gets or sets the trace ID for request tracking
    /// </summary>
    public string? TraceId { get; set; }

    /// <summary>
    /// Creates a successful response
    /// </summary>
    /// <param name="data">The response data</param>
    /// <param name="message">Optional success message</param>
    /// <returns>A successful API response</returns>
    public static ApiResponse<T> Success(T? data, string? message = null)
    {
        return new ApiResponse<T>
        {
            Success = true,
            Data = data,
            Message = message ?? "Request completed successfully",
            StatusCode = 200
        };
    }

    /// <summary>
    /// Creates a created response (201)
    /// </summary>
    /// <param name="data">The created resource data</param>
    /// <param name="message">Optional creation message</param>
    /// <returns>A created API response</returns>
    public static ApiResponse<T> Created(T? data, string? message = null)
    {
        return new ApiResponse<T>
        {
            Success = true,
            Data = data,
            Message = message ?? "Resource created successfully",
            StatusCode = 201
        };
    }

    /// <summary>
    /// Creates a no content response (204)
    /// </summary>
    /// <param name="message">Optional message</param>
    /// <returns>A no content API response</returns>
    public static ApiResponse<T> NoContent(string? message = null)
    {
        return new ApiResponse<T>
        {
            Success = true,
            Data = default,
            Message = message ?? "Request completed successfully",
            StatusCode = 204
        };
    }

    /// <summary>
    /// Creates an error response
    /// </summary>
    /// <param name="message">The error message</param>
    /// <param name="errors">Optional validation errors</param>
    /// <param name="statusCode">The HTTP status code</param>
    /// <returns>An error API response</returns>
    public static ApiResponse<T> Error(string message, object? errors = null, int statusCode = 500)
    {
        return new ApiResponse<T>
        {
            Success = false,
            Message = message,
            Errors = errors,
            StatusCode = statusCode
        };
    }

    /// <summary>
    /// Creates a bad request response (400)
    /// </summary>
    /// <param name="message">The error message</param>
    /// <param name="errors">Optional validation errors</param>
    /// <returns>A bad request API response</returns>
    public static ApiResponse<T> BadRequest(string message, object? errors = null)
    {
        return new ApiResponse<T>
        {
            Success = false,
            Message = message,
            Errors = errors,
            StatusCode = 400
        };
    }

    /// <summary>
    /// Creates an unauthorized response (401)
    /// </summary>
    /// <param name="message">Optional unauthorized message</param>
    /// <returns>An unauthorized API response</returns>
    public static ApiResponse<T> Unauthorized(string? message = null)
    {
        return new ApiResponse<T>
        {
            Success = false,
            Message = message ?? "Unauthorized access",
            StatusCode = 401
        };
    }

    /// <summary>
    /// Creates a forbidden response (403)
    /// </summary>
    /// <param name="message">Optional forbidden message</param>
    /// <returns>A forbidden API response</returns>
    public static ApiResponse<T> Forbidden(string? message = null)
    {
        return new ApiResponse<T>
        {
            Success = false,
            Message = message ?? "Access forbidden",
            StatusCode = 403
        };
    }

    /// <summary>
    /// Creates a not found response (404)
    /// </summary>
    /// <param name="message">Optional not found message</param>
    /// <returns>A not found API response</returns>
    public static ApiResponse<T> NotFound(string? message = null)
    {
        return new ApiResponse<T>
        {
            Success = false,
            Message = message ?? "Resource not found",
            StatusCode = 404
        };
    }

    /// <summary>
    /// Creates a conflict response (409)
    /// </summary>
    /// <param name="message">The conflict message</param>
    /// <param name="errors">Optional conflict details</param>
    /// <returns>A conflict API response</returns>
    public static ApiResponse<T> Conflict(string message, object? errors = null)
    {
        return new ApiResponse<T>
        {
            Success = false,
            Message = message,
            Errors = errors,
            StatusCode = 409
        };
    }
}

/// <summary>
/// Paginated API response wrapper
/// </summary>
/// <typeparam name="T">The type of data being returned</typeparam>
public class PaginatedApiResponse<T> : ApiResponse<IEnumerable<T>>
{
    /// <summary>
    /// Gets or sets the current page number
    /// </summary>
    public int PageNumber { get; set; }

    /// <summary>
    /// Gets or sets the page size
    /// </summary>
    public int PageSize { get; set; }

    /// <summary>
    /// Gets or sets the total number of records
    /// </summary>
    public int TotalRecords { get; set; }

    /// <summary>
    /// Gets or sets the total number of pages
    /// </summary>
    public int TotalPages { get; set; }

    /// <summary>
    /// Gets a value indicating whether there is a next page
    /// </summary>
    [JsonIgnore]
    public bool HasNextPage => PageNumber < TotalPages;

    /// <summary>
    /// Gets a value indicating whether there is a previous page
    /// </summary>
    [JsonIgnore]
    public bool HasPreviousPage => PageNumber > 1;

    /// <summary>
    /// Creates a successful paginated response
    /// </summary>
    /// <param name="data">The response data</param>
    /// <param name="pageNumber">The current page number</param>
    /// <param name="pageSize">The page size</param>
    /// <param name="totalRecords">The total number of records</param>
    /// <param name="message">Optional success message</param>
    /// <returns>A successful paginated API response</returns>
    public static PaginatedApiResponse<T> Success(IEnumerable<T> data, int pageNumber, int pageSize, int totalRecords, string? message = null)
    {
        var totalPages = (int)Math.Ceiling((double)totalRecords / pageSize);
        
        return new PaginatedApiResponse<T>
        {
            Success = true,
            Data = data,
            Message = message ?? "Request completed successfully",
            StatusCode = 200,
            PageNumber = pageNumber,
            PageSize = pageSize,
            TotalRecords = totalRecords,
            TotalPages = totalPages
        };
    }
}
