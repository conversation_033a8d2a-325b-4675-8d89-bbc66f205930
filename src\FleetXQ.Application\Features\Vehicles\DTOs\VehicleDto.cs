using FleetXQ.Domain.Enums;

namespace FleetXQ.Application.Features.Vehicles.DTOs;

/// <summary>
/// Data transfer object for detailed vehicle information
/// </summary>
public class VehicleDto
{
    /// <summary>
    /// Gets or sets the vehicle identifier
    /// </summary>
    public Guid Id { get; set; }

    /// <summary>
    /// Gets or sets the vehicle identification number
    /// </summary>
    public string? VIN { get; set; }

    /// <summary>
    /// Gets or sets the vehicle name
    /// </summary>
    public string VehicleName { get; set; } = string.Empty;

    /// <summary>
    /// Gets or sets the license plate number
    /// </summary>
    public string LicensePlate { get; set; } = string.Empty;

    /// <summary>
    /// Gets or sets the vehicle type
    /// </summary>
    public string VehicleType { get; set; } = string.Empty;

    /// <summary>
    /// Gets or sets the vehicle brand
    /// </summary>
    public string? Brand { get; set; }

    /// <summary>
    /// Gets or sets the vehicle model
    /// </summary>
    public string? Model { get; set; }

    /// <summary>
    /// Gets or sets the vehicle year
    /// </summary>
    public int? Year { get; set; }

    /// <summary>
    /// Gets or sets the vehicle color
    /// </summary>
    public string? Color { get; set; }

    /// <summary>
    /// Gets or sets the fuel type
    /// </summary>
    public string FuelType { get; set; } = string.Empty;

    /// <summary>
    /// Gets or sets the fuel tank capacity in liters
    /// </summary>
    public decimal? FuelTankCapacity { get; set; }

    /// <summary>
    /// Gets or sets the vehicle status
    /// </summary>
    public VehicleStatus Status { get; set; }

    /// <summary>
    /// Gets or sets the current mileage
    /// </summary>
    public decimal CurrentMileage { get; set; }

    /// <summary>
    /// Gets or sets the current location
    /// </summary>
    public LocationDto? CurrentLocation { get; set; }

    /// <summary>
    /// Gets or sets the current speed in km/h
    /// </summary>
    public decimal CurrentSpeedKmh { get; set; }

    /// <summary>
    /// Gets or sets the current fuel level percentage
    /// </summary>
    public decimal? CurrentFuelLevelPercentage { get; set; }

    /// <summary>
    /// Gets or sets the last telemetry update timestamp
    /// </summary>
    public DateTime? LastTelemetryUpdate { get; set; }

    /// <summary>
    /// Gets or sets the last maintenance date
    /// </summary>
    public DateTime? LastMaintenanceDate { get; set; }

    /// <summary>
    /// Gets or sets the next maintenance date
    /// </summary>
    public DateTime? NextMaintenanceDate { get; set; }

    /// <summary>
    /// Gets or sets a value indicating whether the vehicle is available
    /// </summary>
    public bool IsAvailable { get; set; }

    /// <summary>
    /// Gets or sets a value indicating whether the vehicle is moving
    /// </summary>
    public bool IsMoving { get; set; }

    /// <summary>
    /// Gets or sets a value indicating whether the vehicle needs maintenance
    /// </summary>
    public bool NeedsMaintenance { get; set; }

    /// <summary>
    /// Gets or sets the date and time when the vehicle was created
    /// </summary>
    public DateTime CreatedAt { get; set; }

    /// <summary>
    /// Gets or sets the date and time when the vehicle was last updated
    /// </summary>
    public DateTime UpdatedAt { get; set; }
}
