using MediatR;
using Microsoft.AspNetCore.Mvc;
using System.Security.Claims;
using FleetXQ.Api.Models;

namespace FleetXQ.Api.Controllers;

/// <summary>
/// Base controller for all API controllers
/// </summary>
[ApiController]
[Route("api/[controller]")]
[Produces("application/json")]
public abstract class BaseApiController : ControllerBase
{
    private IMediator? _mediator;

    /// <summary>
    /// Gets the mediator instance
    /// </summary>
    protected IMediator Mediator => _mediator ??= HttpContext.RequestServices.GetRequiredService<IMediator>();

    /// <summary>
    /// Gets the current user ID from the JWT token
    /// </summary>
    protected Guid? CurrentUserId
    {
        get
        {
            var userIdClaim = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
            return Guid.TryParse(userIdClaim, out var userId) ? userId : null;
        }
    }

    /// <summary>
    /// Gets the current user's role from the JWT token
    /// </summary>
    protected string? CurrentUserRole => User.FindFirst(ClaimTypes.Role)?.Value;

    /// <summary>
    /// Gets the current username from the JWT token
    /// </summary>
    protected string? CurrentUsername => User.FindFirst(ClaimTypes.Name)?.Value;

    /// <summary>
    /// Checks if the current user has the specified role
    /// </summary>
    /// <param name="role">The role to check</param>
    /// <returns>True if the user has the role, false otherwise</returns>
    protected bool HasRole(string role) => User.IsInRole(role);

    /// <summary>
    /// Checks if the current user has any of the specified roles
    /// </summary>
    /// <param name="roles">The roles to check</param>
    /// <returns>True if the user has any of the roles, false otherwise</returns>
    protected bool HasAnyRole(params string[] roles) => roles.Any(role => User.IsInRole(role));

    /// <summary>
    /// Creates a successful API response
    /// </summary>
    /// <typeparam name="T">The type of data</typeparam>
    /// <param name="data">The response data</param>
    /// <param name="message">Optional success message</param>
    /// <returns>An API response</returns>
    protected ApiResponse<T> Success<T>(T data, string? message = null)
    {
        return ApiResponse<T>.Success(data, message);
    }

    /// <summary>
    /// Creates a successful API response without data
    /// </summary>
    /// <param name="message">Optional success message</param>
    /// <returns>An API response</returns>
    protected ApiResponse<object> Success(string? message = null)
    {
        return ApiResponse<object>.Success(null, message);
    }

    /// <summary>
    /// Creates an error API response
    /// </summary>
    /// <typeparam name="T">The type of data</typeparam>
    /// <param name="message">The error message</param>
    /// <param name="errors">Optional validation errors</param>
    /// <returns>An API response</returns>
    protected ApiResponse<T> Error<T>(string message, object? errors = null)
    {
        return ApiResponse<T>.Error(message, errors);
    }

    /// <summary>
    /// Creates an error API response without data
    /// </summary>
    /// <param name="message">The error message</param>
    /// <param name="errors">Optional validation errors</param>
    /// <returns>An API response</returns>
    protected ApiResponse<object> Error(string message, object? errors = null)
    {
        return ApiResponse<object>.Error(message, errors);
    }

    /// <summary>
    /// Creates a not found API response
    /// </summary>
    /// <typeparam name="T">The type of data</typeparam>
    /// <param name="message">Optional not found message</param>
    /// <returns>An API response</returns>
    protected ApiResponse<T> NotFound<T>(string? message = null)
    {
        return ApiResponse<T>.NotFound(message ?? "Resource not found");
    }

    /// <summary>
    /// Creates a not found API response without data
    /// </summary>
    /// <param name="message">Optional not found message</param>
    /// <returns>An API response</returns>
    protected ApiResponse<object> NotFound(string? message = null)
    {
        return ApiResponse<object>.NotFound(message ?? "Resource not found");
    }

    /// <summary>
    /// Creates an unauthorized API response
    /// </summary>
    /// <typeparam name="T">The type of data</typeparam>
    /// <param name="message">Optional unauthorized message</param>
    /// <returns>An API response</returns>
    protected ApiResponse<T> Unauthorized<T>(string? message = null)
    {
        return ApiResponse<T>.Unauthorized(message ?? "Unauthorized access");
    }

    /// <summary>
    /// Creates an unauthorized API response without data
    /// </summary>
    /// <param name="message">Optional unauthorized message</param>
    /// <returns>An API response</returns>
    protected ApiResponse<object> Unauthorized(string? message = null)
    {
        return ApiResponse<object>.Unauthorized(message ?? "Unauthorized access");
    }

    /// <summary>
    /// Creates a forbidden API response
    /// </summary>
    /// <typeparam name="T">The type of data</typeparam>
    /// <param name="message">Optional forbidden message</param>
    /// <returns>An API response</returns>
    protected ApiResponse<T> Forbidden<T>(string? message = null)
    {
        return ApiResponse<T>.Forbidden(message ?? "Access forbidden");
    }

    /// <summary>
    /// Creates a forbidden API response without data
    /// </summary>
    /// <param name="message">Optional forbidden message</param>
    /// <returns>An API response</returns>
    protected ApiResponse<object> Forbidden(string? message = null)
    {
        return ApiResponse<object>.Forbidden(message ?? "Access forbidden");
    }

    /// <summary>
    /// Creates a bad request API response
    /// </summary>
    /// <typeparam name="T">The type of data</typeparam>
    /// <param name="message">The error message</param>
    /// <param name="errors">Optional validation errors</param>
    /// <returns>An API response</returns>
    protected ApiResponse<T> BadRequest<T>(string message, object? errors = null)
    {
        return ApiResponse<T>.BadRequest(message, errors);
    }

    /// <summary>
    /// Creates a bad request API response without data
    /// </summary>
    /// <param name="message">The error message</param>
    /// <param name="errors">Optional validation errors</param>
    /// <returns>An API response</returns>
    protected ApiResponse<object> BadRequest(string message, object? errors = null)
    {
        return ApiResponse<object>.BadRequest(message, errors);
    }
}
