using FleetXQ.Domain.Entities;

namespace FleetXQ.Application.Interfaces;

/// <summary>
/// Service for JWT token generation and validation
/// </summary>
public interface ITokenService
{
    /// <summary>
    /// Generates an access token for a user
    /// </summary>
    /// <param name="user">The user</param>
    /// <returns>The access token</returns>
    string GenerateAccessToken(User user);

    /// <summary>
    /// Generates a refresh token
    /// </summary>
    /// <returns>The refresh token</returns>
    string GenerateRefreshToken();

    /// <summary>
    /// Validates a token
    /// </summary>
    /// <param name="token">The token to validate</param>
    /// <returns>True if the token is valid</returns>
    bool ValidateToken(string token);

    /// <summary>
    /// Gets the user ID from a token
    /// </summary>
    /// <param name="token">The token</param>
    /// <returns>The user ID if valid, null otherwise</returns>
    Guid? GetUserIdFromToken(string token);

    /// <summary>
    /// Gets the token expiry time
    /// </summary>
    /// <param name="token">The token</param>
    /// <returns>The expiry time if valid, null otherwise</returns>
    DateTime? GetTokenExpiry(string token);

    /// <summary>
    /// Gets the default access token expiry duration
    /// </summary>
    /// <returns>The expiry duration</returns>
    TimeSpan GetAccessTokenExpiry();

    /// <summary>
    /// Gets the default refresh token expiry duration
    /// </summary>
    /// <returns>The expiry duration</returns>
    TimeSpan GetRefreshTokenExpiry();
}
