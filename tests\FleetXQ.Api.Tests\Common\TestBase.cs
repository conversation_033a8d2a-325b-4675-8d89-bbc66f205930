using FleetXQ.Api.Controllers;
using FleetXQ.Api.Models;
using MediatR;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using System.Security.Claims;

namespace FleetXQ.Api.Tests.Common;

/// <summary>
/// Base class for controller tests
/// </summary>
public abstract class TestBase : IDisposable
{
    protected readonly Mock<IMediator> MockMediator;
    protected readonly Mock<ILogger> MockLogger;
    protected readonly ServiceCollection Services;
    protected readonly ServiceProvider ServiceProvider;

    protected TestBase()
    {
        MockMediator = new Mock<IMediator>();
        MockLogger = new Mock<ILogger>();
        Services = new ServiceCollection();
        
        // Register common services
        Services.AddSingleton(MockMediator.Object);
        Services.AddLogging();
        
        ServiceProvider = Services.BuildServiceProvider();
    }

    /// <summary>
    /// Creates a controller with mocked dependencies
    /// </summary>
    /// <typeparam name="T">The controller type</typeparam>
    /// <returns>The controller instance</returns>
    protected T CreateController<T>() where T : BaseApiController
    {
        var controller = (T)Activator.CreateInstance(typeof(T))!;
        
        // Set up HTTP context
        var httpContext = new DefaultHttpContext();
        httpContext.RequestServices = ServiceProvider;
        
        controller.ControllerContext = new ControllerContext
        {
            HttpContext = httpContext
        };

        return controller;
    }

    /// <summary>
    /// Creates a controller with authenticated user context
    /// </summary>
    /// <typeparam name="T">The controller type</typeparam>
    /// <param name="userId">The user ID</param>
    /// <param name="username">The username</param>
    /// <param name="role">The user role</param>
    /// <returns>The controller instance</returns>
    protected T CreateAuthenticatedController<T>(Guid userId, string username, string role) where T : BaseApiController
    {
        var controller = CreateController<T>();
        
        var claims = new List<Claim>
        {
            new(ClaimTypes.NameIdentifier, userId.ToString()),
            new(ClaimTypes.Name, username),
            new(ClaimTypes.Role, role)
        };

        var identity = new ClaimsIdentity(claims, "Test");
        var principal = new ClaimsPrincipal(identity);
        
        controller.ControllerContext.HttpContext.User = principal;

        return controller;
    }

    /// <summary>
    /// Asserts that the result is a successful API response
    /// </summary>
    /// <typeparam name="T">The data type</typeparam>
    /// <param name="result">The action result</param>
    /// <returns>The API response</returns>
    protected ApiResponse<T> AssertSuccessResult<T>(ActionResult<ApiResponse<T>> result)
    {
        result.Should().NotBeNull();
        
        var okResult = result.Result.Should().BeOfType<OkObjectResult>().Subject;
        var apiResponse = okResult.Value.Should().BeOfType<ApiResponse<T>>().Subject;
        
        apiResponse.Success.Should().BeTrue();
        apiResponse.StatusCode.Should().Be(200);
        
        return apiResponse;
    }

    /// <summary>
    /// Asserts that the result is a created API response
    /// </summary>
    /// <typeparam name="T">The data type</typeparam>
    /// <param name="result">The action result</param>
    /// <returns>The API response</returns>
    protected ApiResponse<T> AssertCreatedResult<T>(ActionResult<ApiResponse<T>> result)
    {
        result.Should().NotBeNull();
        
        var createdResult = result.Result.Should().BeOfType<ObjectResult>().Subject;
        createdResult.StatusCode.Should().Be(201);
        
        var apiResponse = createdResult.Value.Should().BeOfType<ApiResponse<T>>().Subject;
        
        apiResponse.Success.Should().BeTrue();
        apiResponse.StatusCode.Should().Be(201);
        
        return apiResponse;
    }

    /// <summary>
    /// Asserts that the result is a bad request API response
    /// </summary>
    /// <typeparam name="T">The data type</typeparam>
    /// <param name="result">The action result</param>
    /// <returns>The API response</returns>
    protected ApiResponse<T> AssertBadRequestResult<T>(ActionResult<ApiResponse<T>> result)
    {
        result.Should().NotBeNull();
        
        var badRequestResult = result.Result.Should().BeOfType<BadRequestObjectResult>().Subject;
        var apiResponse = badRequestResult.Value.Should().BeOfType<ApiResponse<T>>().Subject;
        
        apiResponse.Success.Should().BeFalse();
        apiResponse.StatusCode.Should().Be(400);
        
        return apiResponse;
    }

    /// <summary>
    /// Asserts that the result is a not found API response
    /// </summary>
    /// <typeparam name="T">The data type</typeparam>
    /// <param name="result">The action result</param>
    /// <returns>The API response</returns>
    protected ApiResponse<T> AssertNotFoundResult<T>(ActionResult<ApiResponse<T>> result)
    {
        result.Should().NotBeNull();
        
        var notFoundResult = result.Result.Should().BeOfType<NotFoundObjectResult>().Subject;
        var apiResponse = notFoundResult.Value.Should().BeOfType<ApiResponse<T>>().Subject;
        
        apiResponse.Success.Should().BeFalse();
        apiResponse.StatusCode.Should().Be(404);
        
        return apiResponse;
    }

    /// <summary>
    /// Asserts that the result is an unauthorized API response
    /// </summary>
    /// <typeparam name="T">The data type</typeparam>
    /// <param name="result">The action result</param>
    /// <returns>The API response</returns>
    protected ApiResponse<T> AssertUnauthorizedResult<T>(ActionResult<ApiResponse<T>> result)
    {
        result.Should().NotBeNull();
        
        var unauthorizedResult = result.Result.Should().BeOfType<UnauthorizedObjectResult>().Subject;
        var apiResponse = unauthorizedResult.Value.Should().BeOfType<ApiResponse<T>>().Subject;
        
        apiResponse.Success.Should().BeFalse();
        apiResponse.StatusCode.Should().Be(401);
        
        return apiResponse;
    }

    /// <summary>
    /// Asserts that the result is a paginated success response
    /// </summary>
    /// <typeparam name="T">The data type</typeparam>
    /// <param name="result">The action result</param>
    /// <returns>The paginated API response</returns>
    protected PaginatedApiResponse<T> AssertPaginatedSuccessResult<T>(ActionResult<PaginatedApiResponse<T>> result)
    {
        result.Should().NotBeNull();
        
        var okResult = result.Result.Should().BeOfType<OkObjectResult>().Subject;
        var apiResponse = okResult.Value.Should().BeOfType<PaginatedApiResponse<T>>().Subject;
        
        apiResponse.Success.Should().BeTrue();
        apiResponse.StatusCode.Should().Be(200);
        
        return apiResponse;
    }

    public virtual void Dispose()
    {
        ServiceProvider?.Dispose();
        GC.SuppressFinalize(this);
    }
}
