# FleetXQ Frontend Development Prompts

## Phase 1: React Application Foundation

### Prompt 1.1: Project Structure and Setup
**Objective**: Create a React application following the structure and conventions outlined in `docs/frontend.md` for FleetXQ.

**Context**:
- React 18+ with Vite build tool
- Tailwind CSS for styling
- Redux Toolkit for state management
- TypeScript for type safety
- Feature-based folder structure

**Instructions**:
1. Initialize React project with Vite:
   ```bash
   npm create vite@latest fleetxq-frontend -- --template react-ts
   ```
2. Create folder structure following `docs/frontend.md`:
   ```
   /src
     /assets - Images, icons, static files
     /components - Reusable UI components
     /features - Domain-specific logic and pages
       /auth - Authentication pages and logic
       /dashboard - Main dashboard components
       /vehicles - Vehicle management features
       /drivers - Driver management features
       /telemetry - Real-time telemetry displays
       /alerts - Alert management interface
     /hooks - Custom React hooks
     /layouts - Page layout components
     /routes - Route definitions and guards
     /services - API abstraction layer
     /store - Redux store configuration
     /types - TypeScript type definitions
     /utils - Pure utility functions
   ```
3. Install required dependencies:
   - UI: `@headlessui/react`, `@heroicons/react`, `tailwindcss`
   - State: `@reduxjs/toolkit`, `react-redux`
   - Routing: `react-router-dom`
   - API: `axios`
   - Real-time: `@microsoft/signalr`
   - Charts: `recharts`
   - Forms: `react-hook-form`, `@hookform/resolvers/yup`
4. Configure Tailwind CSS with FleetXQ theme colors
5. Set up ESLint and Prettier configurations

**Expected Deliverables**:
- Complete React project structure
- All dependencies installed and configured
- Tailwind CSS theme configuration
- ESLint/Prettier setup
- Basic routing structure

**Validation Criteria**:
- Project builds without errors
- Folder structure follows conventions
- All dependencies resolve correctly
- Linting rules enforce code quality

### Prompt 1.2: Redux Store and State Management
**Objective**: Implement Redux Toolkit store with feature-based slices for FleetXQ state management.

**Context**:
- Redux Toolkit for global state management
- Feature-based slices following domain boundaries
- Async thunks for API integration
- Proper TypeScript integration

**Instructions**:
1. Create Redux store configuration:
   ```typescript
   // store/index.ts
   export const store = configureStore({
     reducer: {
       auth: authSlice.reducer,
       vehicles: vehiclesSlice.reducer,
       drivers: driversSlice.reducer,
       telemetry: telemetrySlice.reducer,
       alerts: alertsSlice.reducer,
       dashboard: dashboardSlice.reducer
     }
   });
   ```
2. Implement feature slices:
   - `authSlice` - User authentication state, login/logout
   - `vehiclesSlice` - Vehicle list, current vehicle, CRUD operations
   - `driversSlice` - Driver management and assignments
   - `telemetrySlice` - Real-time telemetry data and history
   - `alertsSlice` - Alert notifications and acknowledgments
   - `dashboardSlice` - Dashboard metrics and summaries
3. Create async thunks for API operations:
   - Authentication: login, logout, refresh token
   - Vehicles: fetch list, create, update, delete
   - Telemetry: fetch latest, fetch history
   - Alerts: fetch active, acknowledge, resolve
4. Implement proper error handling and loading states
5. Add TypeScript types for all state shapes and actions

**Expected Deliverables**:
- Complete Redux store configuration
- All feature slices with actions and reducers
- Async thunks for API integration
- TypeScript types for state management
- Error handling and loading states

**Validation Criteria**:
- Store compiles without TypeScript errors
- All slices handle actions correctly
- Async thunks integrate with API properly
- Error states are managed consistently

### Prompt 1.3: API Service Layer
**Objective**: Create a centralized API service layer with Axios for FleetXQ backend integration.

**Context**:
- Centralized API configuration with interceptors
- JWT token management and refresh
- Error handling and retry logic
- TypeScript interfaces for API responses

**Instructions**:
1. Create base API configuration:
   ```typescript
   // services/api.ts
   const api = axios.create({
     baseURL: import.meta.env.VITE_API_URL,
     headers: { 'Content-Type': 'application/json' }
   });
   ```
2. Implement request/response interceptors:
   - Add JWT token to requests automatically
   - Handle token refresh on 401 responses
   - Global error handling and logging
   - Request/response transformation
3. Create service modules for each domain:
   - `authService` - Login, logout, refresh, profile
   - `vehicleService` - CRUD operations, status updates
   - `driverService` - Driver management, assignments
   - `telemetryService` - Real-time data, history queries
   - `alertService` - Alert management, acknowledgments
   - `dashboardService` - Analytics and summaries
4. Implement proper TypeScript interfaces:
   - Request/response types for each endpoint
   - Error response types
   - API configuration types
5. Add retry logic and timeout handling

**Expected Deliverables**:
- Centralized API configuration with interceptors
- Service modules for all domain areas
- TypeScript interfaces for API contracts
- Error handling and retry mechanisms
- JWT token management

**Validation Criteria**:
- API calls work correctly with backend
- JWT tokens are managed automatically
- Error handling provides meaningful feedback
- TypeScript types prevent API contract issues

## Phase 2: Core Features Implementation

### Prompt 2.1: Authentication and Route Guards
**Objective**: Implement authentication system with JWT tokens and role-based route protection.

**Context**:
- JWT token storage and management
- Role-based access control (Admin, Manager, Driver, User)
- Protected routes and navigation guards
- Login/logout functionality

**Instructions**:
1. Create authentication components:
   - `LoginPage` - Email/password login form
   - `LogoutButton` - Logout functionality
   - `ProtectedRoute` - Route guard component
   - `RoleGuard` - Role-based access component
2. Implement authentication hooks:
   - `useAuth` - Current user state and actions
   - `usePermissions` - Role-based permission checking
   - `useTokenRefresh` - Automatic token refresh
3. Create route protection system:
   - Public routes (login, forgot password)
   - Protected routes (dashboard, vehicles, etc.)
   - Role-specific routes (admin panel, driver view)
   - Redirect logic for unauthorized access
4. Implement token persistence:
   - Secure token storage (localStorage/sessionStorage)
   - Token expiration handling
   - Automatic logout on token expiry
5. Add authentication UI components:
   - Login form with validation
   - User profile dropdown
   - Role indicator badges

**Expected Deliverables**:
- Complete authentication system
- Role-based route protection
- Authentication UI components
- Token management and persistence
- Permission-based UI rendering

**Validation Criteria**:
- Login/logout works correctly
- Routes are protected based on authentication
- Role-based access control functions properly
- Token refresh happens automatically

### Prompt 2.2: Vehicle Management Interface
**Objective**: Create comprehensive vehicle management interface with real-time status updates.

**Context**:
- Vehicle CRUD operations
- Real-time status monitoring
- Vehicle assignment management
- Telemetry data visualization

**Instructions**:
1. Create vehicle management components:
   - `VehicleList` - Paginated vehicle grid/table
   - `VehicleCard` - Individual vehicle status card
   - `VehicleDetails` - Detailed vehicle information
   - `VehicleForm` - Create/edit vehicle form
   - `VehicleMap` - Real-time vehicle locations
2. Implement vehicle status features:
   - Real-time status indicators (online, offline, moving, idle)
   - Last known location display
   - Fuel level and engine status
   - Maintenance alerts and schedules
3. Create vehicle assignment interface:
   - Driver assignment dropdown
   - Assignment history
   - Bulk assignment operations
   - Assignment conflict detection
4. Add vehicle analytics:
   - Utilization charts
   - Performance metrics
   - Fuel consumption trends
   - Maintenance cost tracking
5. Implement search and filtering:
   - Vehicle name/license plate search
   - Status-based filtering
   - Vehicle type filtering
   - Advanced filter combinations

**Expected Deliverables**:
- Complete vehicle management interface
- Real-time status monitoring
- Vehicle assignment functionality
- Analytics and reporting components
- Search and filtering capabilities

**Validation Criteria**:
- Vehicle CRUD operations work correctly
- Real-time updates display properly
- Assignment management functions smoothly
- Analytics provide meaningful insights

### Prompt 2.3: Real-time Dashboard Implementation
**Objective**: Create a comprehensive dashboard with real-time metrics, charts, and alerts for FleetXQ.

**Context**:
- Real-time data updates via SignalR
- Interactive charts and visualizations
- Key performance indicators (KPIs)
- Alert notifications and management

**Instructions**:
1. Create dashboard layout components:
   - `DashboardLayout` - Main dashboard container
   - `MetricCard` - KPI display cards
   - `ChartContainer` - Chart wrapper with loading states
   - `AlertPanel` - Real-time alert notifications
   - `QuickActions` - Common action buttons
2. Implement key metrics display:
   - Total vehicles and active count
   - Drivers on duty vs available
   - Active alerts by severity
   - Fuel consumption trends
   - Maintenance due notifications
3. Create interactive charts using Recharts:
   - Vehicle utilization over time
   - Fuel consumption by vehicle type
   - Driver performance metrics
   - Alert frequency trends
   - Geographic distribution of vehicles
4. Add real-time features:
   - Live vehicle location updates
   - Real-time alert notifications
   - Status change animations
   - Auto-refresh intervals
5. Implement dashboard customization:
   - Widget arrangement (drag-and-drop)
   - Metric selection preferences
   - Time range filters
   - Export functionality

**Expected Deliverables**:
- Comprehensive dashboard interface
- Real-time metric updates
- Interactive charts and visualizations
- Alert notification system
- Dashboard customization features

**Validation Criteria**:
- Dashboard loads quickly with all metrics
- Real-time updates work smoothly
- Charts are
</augment_code_snippet>