using FleetXQ.Application.Features.Vehicles.DTOs;
using MediatR;

namespace FleetXQ.Application.Features.Vehicles.Queries.GetVehicleById;

/// <summary>
/// Query to get a vehicle by its ID
/// </summary>
public sealed class GetVehicleByIdQuery : IRequest<GetVehicleByIdResult>
{
    /// <summary>
    /// Gets or sets the vehicle ID
    /// </summary>
    public Guid VehicleId { get; set; }

    /// <summary>
    /// Initializes a new instance of the <see cref="GetVehicleByIdQuery"/> class
    /// </summary>
    /// <param name="vehicleId">The vehicle ID</param>
    public GetVehicleByIdQuery(Guid vehicleId)
    {
        VehicleId = vehicleId;
    }
}

/// <summary>
/// Result of getting a vehicle by ID
/// </summary>
public sealed class GetVehicleByIdResult
{
    /// <summary>
    /// Gets or sets the vehicle data
    /// </summary>
    public VehicleDto? Vehicle { get; set; }

    /// <summary>
    /// Gets or sets a value indicating whether the vehicle was found
    /// </summary>
    public bool Found { get; set; }

    /// <summary>
    /// Gets or sets the error message if the operation failed
    /// </summary>
    public string? ErrorMessage { get; set; }

    /// <summary>
    /// Creates a successful result with vehicle data
    /// </summary>
    /// <param name="vehicle">The vehicle data</param>
    /// <returns>A successful result</returns>
    public static GetVehicleByIdResult Success(VehicleDto vehicle)
    {
        return new GetVehicleByIdResult
        {
            Vehicle = vehicle,
            Found = true
        };
    }

    /// <summary>
    /// Creates a not found result
    /// </summary>
    /// <returns>A not found result</returns>
    public static GetVehicleByIdResult NotFound()
    {
        return new GetVehicleByIdResult
        {
            Found = false
        };
    }

    /// <summary>
    /// Creates a failed result
    /// </summary>
    /// <param name="errorMessage">The error message</param>
    /// <returns>A failed result</returns>
    public static GetVehicleByIdResult Failed(string errorMessage)
    {
        return new GetVehicleByIdResult
        {
            Found = false,
            ErrorMessage = errorMessage
        };
    }
}
