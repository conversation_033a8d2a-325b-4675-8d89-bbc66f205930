namespace FleetXQ.Domain.Enums;

/// <summary>
/// Represents the type of alert
/// </summary>
public enum AlertType
{
    /// <summary>
    /// Speed-related alert (speeding, etc.)
    /// </summary>
    Speed = 1,

    /// <summary>
    /// Fuel-related alert (low fuel, etc.)
    /// </summary>
    Fuel = 2,

    /// <summary>
    /// Maintenance-related alert
    /// </summary>
    Maintenance = 3,

    /// <summary>
    /// Geofence-related alert (entering/leaving zones)
    /// </summary>
    Geofence = 4,

    /// <summary>
    /// Harsh driving behavior alert
    /// </summary>
    HarshDriving = 5,

    /// <summary>
    /// Engine or system-related alert
    /// </summary>
    System = 6,

    /// <summary>
    /// Security-related alert
    /// </summary>
    Security = 7
}
