using FleetXQ.Domain.Entities;
using FleetXQ.Domain.Enums;

namespace FleetXQ.Domain.Interfaces;

/// <summary>
/// Repository interface for Driver aggregate root
/// </summary>
public interface IDriverRepository : IRepository<Driver>
{
    /// <summary>
    /// Gets a driver by their employee ID
    /// </summary>
    /// <param name="employeeId">The employee ID</param>
    /// <param name="cancellationToken">The cancellation token</param>
    /// <returns>The driver if found, null otherwise</returns>
    Task<Driver?> GetByEmployeeIdAsync(string employeeId, CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets a driver by their email address
    /// </summary>
    /// <param name="email">The email address</param>
    /// <param name="cancellationToken">The cancellation token</param>
    /// <returns>The driver if found, null otherwise</returns>
    Task<Driver?> GetByEmailAsync(string email, CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets a driver by their license number
    /// </summary>
    /// <param name="licenseNumber">The license number</param>
    /// <param name="cancellationToken">The cancellation token</param>
    /// <returns>The driver if found, null otherwise</returns>
    Task<Driver?> GetByLicenseNumberAsync(string licenseNumber, CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets a driver by their associated user ID
    /// </summary>
    /// <param name="userId">The user ID</param>
    /// <param name="cancellationToken">The cancellation token</param>
    /// <returns>The driver if found, null otherwise</returns>
    Task<Driver?> GetByUserIdAsync(Guid userId, CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets drivers by their status
    /// </summary>
    /// <param name="status">The driver status</param>
    /// <param name="cancellationToken">The cancellation token</param>
    /// <returns>A collection of drivers with the specified status</returns>
    Task<IEnumerable<Driver>> GetByStatusAsync(DriverStatus status, CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets drivers by their license class
    /// </summary>
    /// <param name="licenseClass">The license class</param>
    /// <param name="cancellationToken">The cancellation token</param>
    /// <returns>A collection of drivers with the specified license class</returns>
    Task<IEnumerable<Driver>> GetByLicenseClassAsync(string licenseClass, CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets drivers that are available for assignment
    /// </summary>
    /// <param name="cancellationToken">The cancellation token</param>
    /// <returns>A collection of available drivers</returns>
    Task<IEnumerable<Driver>> GetAvailableDriversAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets drivers whose licenses are expiring within the specified days
    /// </summary>
    /// <param name="daysThreshold">The number of days threshold</param>
    /// <param name="cancellationToken">The cancellation token</param>
    /// <returns>A collection of drivers with expiring licenses</returns>
    Task<IEnumerable<Driver>> GetDriversWithExpiringLicensesAsync(int daysThreshold = 30, 
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets drivers whose licenses have expired
    /// </summary>
    /// <param name="cancellationToken">The cancellation token</param>
    /// <returns>A collection of drivers with expired licenses</returns>
    Task<IEnumerable<Driver>> GetDriversWithExpiredLicensesAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets drivers hired within a date range
    /// </summary>
    /// <param name="fromDate">The start date</param>
    /// <param name="toDate">The end date</param>
    /// <param name="cancellationToken">The cancellation token</param>
    /// <returns>A collection of drivers hired within the date range</returns>
    Task<IEnumerable<Driver>> GetDriversHiredBetweenAsync(DateTime fromDate, DateTime toDate, 
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets drivers by age range
    /// </summary>
    /// <param name="minAge">The minimum age</param>
    /// <param name="maxAge">The maximum age</param>
    /// <param name="cancellationToken">The cancellation token</param>
    /// <returns>A collection of drivers within the age range</returns>
    Task<IEnumerable<Driver>> GetDriversByAgeRangeAsync(int minAge, int maxAge, 
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets drivers compatible with a specific vehicle type
    /// </summary>
    /// <param name="vehicleType">The vehicle type</param>
    /// <param name="cancellationToken">The cancellation token</param>
    /// <returns>A collection of compatible drivers</returns>
    Task<IEnumerable<Driver>> GetDriversCompatibleWithVehicleTypeAsync(string vehicleType, 
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets driver statistics by status
    /// </summary>
    /// <param name="cancellationToken">The cancellation token</param>
    /// <returns>A dictionary with status counts</returns>
    Task<Dictionary<DriverStatus, int>> GetDriverStatisticsByStatusAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets driver statistics by license class
    /// </summary>
    /// <param name="cancellationToken">The cancellation token</param>
    /// <returns>A dictionary with license class counts</returns>
    Task<Dictionary<string, int>> GetDriverStatisticsByLicenseClassAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// Searches drivers by multiple criteria
    /// </summary>
    /// <param name="searchCriteria">The search criteria</param>
    /// <param name="cancellationToken">The cancellation token</param>
    /// <returns>A collection of drivers matching the criteria</returns>
    Task<IEnumerable<Driver>> SearchDriversAsync(DriverSearchCriteria searchCriteria, 
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Checks if an employee ID is already in use
    /// </summary>
    /// <param name="employeeId">The employee ID to check</param>
    /// <param name="excludeDriverId">Driver ID to exclude from the check</param>
    /// <param name="cancellationToken">The cancellation token</param>
    /// <returns>True if the employee ID is in use, false otherwise</returns>
    Task<bool> IsEmployeeIdInUseAsync(string employeeId, Guid? excludeDriverId = null, 
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Checks if an email address is already in use
    /// </summary>
    /// <param name="email">The email address to check</param>
    /// <param name="excludeDriverId">Driver ID to exclude from the check</param>
    /// <param name="cancellationToken">The cancellation token</param>
    /// <returns>True if the email is in use, false otherwise</returns>
    Task<bool> IsEmailInUseAsync(string email, Guid? excludeDriverId = null, 
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Checks if a license number is already in use
    /// </summary>
    /// <param name="licenseNumber">The license number to check</param>
    /// <param name="excludeDriverId">Driver ID to exclude from the check</param>
    /// <param name="cancellationToken">The cancellation token</param>
    /// <returns>True if the license number is in use, false otherwise</returns>
    Task<bool> IsLicenseNumberInUseAsync(string licenseNumber, Guid? excludeDriverId = null, 
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets drivers with upcoming birthdays within the specified days
    /// </summary>
    /// <param name="daysThreshold">The number of days threshold</param>
    /// <param name="cancellationToken">The cancellation token</param>
    /// <returns>A collection of drivers with upcoming birthdays</returns>
    Task<IEnumerable<Driver>> GetDriversWithUpcomingBirthdaysAsync(int daysThreshold = 7, 
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets drivers with years of service within a range
    /// </summary>
    /// <param name="minYears">The minimum years of service</param>
    /// <param name="maxYears">The maximum years of service</param>
    /// <param name="cancellationToken">The cancellation token</param>
    /// <returns>A collection of drivers within the service years range</returns>
    Task<IEnumerable<Driver>> GetDriversByServiceYearsAsync(double minYears, double maxYears, 
        CancellationToken cancellationToken = default);
}

/// <summary>
/// Represents search criteria for drivers
/// </summary>
public class DriverSearchCriteria
{
    /// <summary>
    /// Gets or sets the search term for name, email, or employee ID
    /// </summary>
    public string? SearchTerm { get; set; }

    /// <summary>
    /// Gets or sets the driver status filter
    /// </summary>
    public DriverStatus? Status { get; set; }

    /// <summary>
    /// Gets or sets the license class filter
    /// </summary>
    public string? LicenseClass { get; set; }

    /// <summary>
    /// Gets or sets the minimum age filter
    /// </summary>
    public int? MinAge { get; set; }

    /// <summary>
    /// Gets or sets the maximum age filter
    /// </summary>
    public int? MaxAge { get; set; }

    /// <summary>
    /// Gets or sets the minimum years of service filter
    /// </summary>
    public double? MinServiceYears { get; set; }

    /// <summary>
    /// Gets or sets the maximum years of service filter
    /// </summary>
    public double? MaxServiceYears { get; set; }

    /// <summary>
    /// Gets or sets the hire date from filter
    /// </summary>
    public DateTime? HiredFrom { get; set; }

    /// <summary>
    /// Gets or sets the hire date to filter
    /// </summary>
    public DateTime? HiredTo { get; set; }

    /// <summary>
    /// Gets or sets a value indicating whether to include only available drivers
    /// </summary>
    public bool? AvailableOnly { get; set; }

    /// <summary>
    /// Gets or sets a value indicating whether to include only drivers with expiring licenses
    /// </summary>
    public bool? ExpiringLicensesOnly { get; set; }

    /// <summary>
    /// Gets or sets the vehicle type compatibility filter
    /// </summary>
    public string? VehicleTypeCompatibility { get; set; }
}
