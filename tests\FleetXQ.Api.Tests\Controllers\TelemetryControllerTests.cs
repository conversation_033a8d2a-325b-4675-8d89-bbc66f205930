using FleetXQ.Api.Controllers;
using FleetXQ.Api.Models;
using FleetXQ.Api.Tests.Common;
using FleetXQ.Application.Features.Telemetry.Commands.ProcessTelemetryData;
using FleetXQ.Application.Features.Telemetry.Queries.GetLatestTelemetry;
using FleetXQ.Application.Features.Telemetry.Queries.GetTelemetryHistory;

namespace FleetXQ.Api.Tests.Controllers;

/// <summary>
/// Unit tests for TelemetryController
/// </summary>
public class TelemetryControllerTests : TestBase
{
    private readonly TelemetryController _controller;

    public TelemetryControllerTests()
    {
        _controller = CreateAuthenticatedController<TelemetryController>(Guid.NewGuid(), "testuser", "Manager");
    }

    [Fact]
    public async Task ProcessTelemetryData_WithValidData_ShouldReturnSuccess()
    {
        // Arrange
        var vehicleId = Guid.NewGuid();
        var command = new ProcessTelemetryDataCommand
        {
            VehicleId = vehicleId,
            Latitude = 40.7128m,
            Longitude = -74.0060m,
            SpeedKmh = 65m,
            FuelLevelPercentage = 75m,
            Mileage = 50000m
        };

        var expectedResult = ProcessTelemetryDataResult.Successful(
            vehicleId,
            true,
            new List<string> { "Speed", "Fuel" }
        );

        MockMediator.Setup(x => x.Send(It.IsAny<ProcessTelemetryDataCommand>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(expectedResult);

        // Act
        var result = await _controller.ProcessTelemetryData(command);

        // Assert
        var response = AssertSuccessResult(result);
        response.Data.Should().NotBeNull();
        response.Message.Should().Contain("processed successfully");

        MockMediator.Verify(x => x.Send(It.IsAny<ProcessTelemetryDataCommand>(), It.IsAny<CancellationToken>()), Times.Once);
    }

    [Fact]
    public async Task ProcessTelemetryData_WithNonExistentVehicle_ShouldReturnNotFound()
    {
        // Arrange
        var vehicleId = Guid.NewGuid();
        var command = new ProcessTelemetryDataCommand
        {
            VehicleId = vehicleId,
            Latitude = 40.7128m,
            Longitude = -74.0060m,
            SpeedKmh = 65m
        };

        var expectedResult = ProcessTelemetryDataResult.Failed("Vehicle not found");

        MockMediator.Setup(x => x.Send(It.IsAny<ProcessTelemetryDataCommand>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(expectedResult);

        // Act
        var result = await _controller.ProcessTelemetryData(command);

        // Assert
        var response = AssertNotFoundResult(result);
        response.Message.Should().Contain("Vehicle not found");

        MockMediator.Verify(x => x.Send(It.IsAny<ProcessTelemetryDataCommand>(), It.IsAny<CancellationToken>()), Times.Once);
    }

    [Fact]
    public async Task ProcessTelemetryData_WithInvalidData_ShouldReturnBadRequest()
    {
        // Arrange
        var vehicleId = Guid.NewGuid();
        var command = new ProcessTelemetryDataCommand
        {
            VehicleId = vehicleId,
            Latitude = 40.7128m,
            Longitude = -74.0060m,
            SpeedKmh = -10m // Invalid speed
        };

        var expectedResult = ProcessTelemetryDataResult.Failed("Invalid telemetry data");

        MockMediator.Setup(x => x.Send(It.IsAny<ProcessTelemetryDataCommand>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(expectedResult);

        // Act
        var result = await _controller.ProcessTelemetryData(command);

        // Assert
        var response = AssertBadRequestResult(result);
        response.Message.Should().Contain("Invalid telemetry data");

        MockMediator.Verify(x => x.Send(It.IsAny<ProcessTelemetryDataCommand>(), It.IsAny<CancellationToken>()), Times.Once);
    }

    [Fact]
    public async Task GetLatestTelemetry_WithExistingVehicle_ShouldReturnTelemetryData()
    {
        // Arrange
        var vehicleId = Guid.NewGuid();
        var telemetryData = new
        {
            VehicleId = vehicleId,
            Latitude = 40.7128,
            Longitude = -74.0060,
            Speed = 65.0,
            FuelLevel = 75.0,
            Timestamp = DateTime.UtcNow
        };

        var expectedResult = GetLatestTelemetryResult.Success(telemetryData);

        MockMediator.Setup(x => x.Send(It.IsAny<GetLatestTelemetryQuery>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(expectedResult);

        // Act
        var result = await _controller.GetLatestTelemetry(vehicleId);

        // Assert
        var response = AssertSuccessResult(result);
        response.Data.Should().NotBeNull();
        response.Message.Should().Contain("retrieved successfully");

        MockMediator.Verify(x => x.Send(It.Is<GetLatestTelemetryQuery>(q => 
            q.VehicleId == vehicleId), It.IsAny<CancellationToken>()), Times.Once);
    }

    [Fact]
    public async Task GetLatestTelemetry_WithNonExistentVehicle_ShouldReturnNotFound()
    {
        // Arrange
        var vehicleId = Guid.NewGuid();
        var expectedResult = GetLatestTelemetryResult.NotFound("No telemetry data found");

        MockMediator.Setup(x => x.Send(It.IsAny<GetLatestTelemetryQuery>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(expectedResult);

        // Act
        var result = await _controller.GetLatestTelemetry(vehicleId);

        // Assert
        var response = AssertNotFoundResult(result);
        response.Message.Should().Contain("No telemetry data found");

        MockMediator.Verify(x => x.Send(It.IsAny<GetLatestTelemetryQuery>(), It.IsAny<CancellationToken>()), Times.Once);
    }

    [Fact]
    public async Task GetTelemetryHistory_WithValidParameters_ShouldReturnPaginatedData()
    {
        // Arrange
        var vehicleId = Guid.NewGuid();
        var startDate = DateTime.UtcNow.AddDays(-7);
        var endDate = DateTime.UtcNow;

        var telemetryData = new List<object>
        {
            new { VehicleId = vehicleId, Speed = 60.0, Timestamp = DateTime.UtcNow.AddHours(-1) },
            new { VehicleId = vehicleId, Speed = 55.0, Timestamp = DateTime.UtcNow.AddHours(-2) }
        };

        var expectedResult = GetTelemetryHistoryResult.Success(
            telemetryData,
            100, // totalCount
            1,   // pageNumber
            50   // pageSize
        );

        MockMediator.Setup(x => x.Send(It.IsAny<GetTelemetryHistoryQuery>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(expectedResult);

        // Act
        var result = await _controller.GetTelemetryHistory(
            vehicleId,
            startDate,
            endDate,
            pageNumber: 1,
            pageSize: 50
        );

        // Assert
        var response = AssertPaginatedSuccessResult(result);
        response.Data.Should().NotBeNull();
        response.TotalRecords.Should().Be(100);
        response.PageNumber.Should().Be(1);
        response.PageSize.Should().Be(50);

        MockMediator.Verify(x => x.Send(It.Is<GetTelemetryHistoryQuery>(q => 
            q.VehicleId == vehicleId && 
            q.StartDate == startDate && 
            q.EndDate == endDate &&
            q.PageNumber == 1 &&
            q.PageSize == 50), It.IsAny<CancellationToken>()), Times.Once);
    }

    [Fact]
    public async Task GetTelemetryHistory_WithInvalidPageNumber_ShouldReturnBadRequest()
    {
        // Arrange
        var vehicleId = Guid.NewGuid();

        // Act
        var result = await _controller.GetTelemetryHistory(vehicleId, pageNumber: 0);

        // Assert
        var response = AssertBadRequestResult(result);
        response.Message.Should().Contain("Page number must be greater than 0");

        MockMediator.Verify(x => x.Send(It.IsAny<GetTelemetryHistoryQuery>(), It.IsAny<CancellationToken>()), Times.Never);
    }

    [Fact]
    public async Task GetTelemetryHistory_WithInvalidPageSize_ShouldReturnBadRequest()
    {
        // Arrange
        var vehicleId = Guid.NewGuid();

        // Act
        var result = await _controller.GetTelemetryHistory(vehicleId, pageSize: 1001);

        // Assert
        var response = AssertBadRequestResult(result);
        response.Message.Should().Contain("Page size must be between 1 and 1000");

        MockMediator.Verify(x => x.Send(It.IsAny<GetTelemetryHistoryQuery>(), It.IsAny<CancellationToken>()), Times.Never);
    }

    [Fact]
    public async Task GetTelemetryHistory_WithInvalidDateRange_ShouldReturnBadRequest()
    {
        // Arrange
        var vehicleId = Guid.NewGuid();
        var startDate = DateTime.UtcNow;
        var endDate = DateTime.UtcNow.AddDays(-1); // End date before start date

        // Act
        var result = await _controller.GetTelemetryHistory(vehicleId, startDate, endDate);

        // Assert
        var response = AssertBadRequestResult(result);
        response.Message.Should().Contain("Start date cannot be after end date");

        MockMediator.Verify(x => x.Send(It.IsAny<GetTelemetryHistoryQuery>(), It.IsAny<CancellationToken>()), Times.Never);
    }

    [Fact]
    public async Task GetLatestTelemetryBatch_WithValidVehicleIds_ShouldReturnBatchData()
    {
        // Arrange
        var vehicleIds = new List<Guid> { Guid.NewGuid(), Guid.NewGuid() };
        var request = new BatchTelemetryRequest { VehicleIds = vehicleIds };

        var telemetryResult1 = GetLatestTelemetryResult.Success(new { VehicleId = vehicleIds[0], Speed = 60.0 });
        var telemetryResult2 = GetLatestTelemetryResult.Success(new { VehicleId = vehicleIds[1], Speed = 55.0 });

        MockMediator.SetupSequence(x => x.Send(It.IsAny<GetLatestTelemetryQuery>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(telemetryResult1)
            .ReturnsAsync(telemetryResult2);

        // Act
        var result = await _controller.GetLatestTelemetryBatch(request);

        // Assert
        var response = AssertSuccessResult(result);
        response.Data.Should().NotBeNull();
        response.Message.Should().Contain("Batch telemetry data retrieved successfully");

        MockMediator.Verify(x => x.Send(It.IsAny<GetLatestTelemetryQuery>(), It.IsAny<CancellationToken>()), Times.Exactly(2));
    }

    [Fact]
    public async Task GetLatestTelemetryBatch_WithEmptyVehicleIds_ShouldReturnBadRequest()
    {
        // Arrange
        var request = new BatchTelemetryRequest { VehicleIds = new List<Guid>() };

        // Act
        var result = await _controller.GetLatestTelemetryBatch(request);

        // Assert
        var response = AssertBadRequestResult(result);
        response.Message.Should().Contain("Vehicle IDs are required");

        MockMediator.Verify(x => x.Send(It.IsAny<GetLatestTelemetryQuery>(), It.IsAny<CancellationToken>()), Times.Never);
    }

    [Fact]
    public async Task GetLatestTelemetryBatch_WithTooManyVehicleIds_ShouldReturnBadRequest()
    {
        // Arrange
        var vehicleIds = Enumerable.Range(0, 101).Select(_ => Guid.NewGuid()).ToList();
        var request = new BatchTelemetryRequest { VehicleIds = vehicleIds };

        // Act
        var result = await _controller.GetLatestTelemetryBatch(request);

        // Assert
        var response = AssertBadRequestResult(result);
        response.Message.Should().Contain("Maximum 100 vehicle IDs allowed per request");

        MockMediator.Verify(x => x.Send(It.IsAny<GetLatestTelemetryQuery>(), It.IsAny<CancellationToken>()), Times.Never);
    }

    [Fact]
    public async Task GetTelemetryStatistics_ShouldReturnStatistics()
    {
        // Act
        var result = await _controller.GetTelemetryStatistics();

        // Assert
        var response = AssertSuccessResult(result);
        response.Data.Should().NotBeNull();
        response.Message.Should().Contain("Telemetry statistics retrieved successfully");
    }
}
