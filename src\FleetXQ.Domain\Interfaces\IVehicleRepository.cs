using FleetXQ.Domain.Entities;
using FleetXQ.Domain.Enums;
using FleetXQ.Domain.ValueObjects;

namespace FleetXQ.Domain.Interfaces;

/// <summary>
/// Repository interface for Vehicle aggregate root
/// </summary>
public interface IVehicleRepository : IRepository<Vehicle>
{
    /// <summary>
    /// Gets a vehicle by its license plate
    /// </summary>
    /// <param name="licensePlate">The license plate</param>
    /// <param name="cancellationToken">The cancellation token</param>
    /// <returns>The vehicle if found, null otherwise</returns>
    Task<Vehicle?> GetByLicensePlateAsync(string licensePlate, CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets a vehicle by its VIN
    /// </summary>
    /// <param name="vin">The Vehicle Identification Number</param>
    /// <param name="cancellationToken">The cancellation token</param>
    /// <returns>The vehicle if found, null otherwise</returns>
    Task<Vehicle?> GetByVinAsync(string vin, CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets vehicles by their status
    /// </summary>
    /// <param name="status">The vehicle status</param>
    /// <param name="cancellationToken">The cancellation token</param>
    /// <returns>A collection of vehicles with the specified status</returns>
    Task<IEnumerable<Vehicle>> GetByStatusAsync(VehicleStatus status, CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets vehicles by their type
    /// </summary>
    /// <param name="vehicleType">The vehicle type</param>
    /// <param name="cancellationToken">The cancellation token</param>
    /// <returns>A collection of vehicles of the specified type</returns>
    Task<IEnumerable<Vehicle>> GetByTypeAsync(string vehicleType, CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets vehicles that are available for assignment
    /// </summary>
    /// <param name="cancellationToken">The cancellation token</param>
    /// <returns>A collection of available vehicles</returns>
    Task<IEnumerable<Vehicle>> GetAvailableVehiclesAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets vehicles that need maintenance
    /// </summary>
    /// <param name="cancellationToken">The cancellation token</param>
    /// <returns>A collection of vehicles needing maintenance</returns>
    Task<IEnumerable<Vehicle>> GetVehiclesNeedingMaintenanceAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets vehicles within a specified location radius
    /// </summary>
    /// <param name="centerLocation">The center location</param>
    /// <param name="radiusKm">The radius in kilometers</param>
    /// <param name="cancellationToken">The cancellation token</param>
    /// <returns>A collection of vehicles within the specified radius</returns>
    Task<IEnumerable<Vehicle>> GetVehiclesInRadiusAsync(Location centerLocation, double radiusKm, 
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets vehicles with low fuel levels
    /// </summary>
    /// <param name="fuelThreshold">The fuel level threshold percentage</param>
    /// <param name="cancellationToken">The cancellation token</param>
    /// <returns>A collection of vehicles with low fuel</returns>
    Task<IEnumerable<Vehicle>> GetVehiclesWithLowFuelAsync(decimal fuelThreshold = 20, 
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets vehicles that haven't reported telemetry within the specified time
    /// </summary>
    /// <param name="timeThreshold">The time threshold</param>
    /// <param name="cancellationToken">The cancellation token</param>
    /// <returns>A collection of vehicles with stale telemetry</returns>
    Task<IEnumerable<Vehicle>> GetVehiclesWithStaleTelemetryAsync(TimeSpan timeThreshold, 
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets vehicles by fuel type
    /// </summary>
    /// <param name="fuelType">The fuel type</param>
    /// <param name="cancellationToken">The cancellation token</param>
    /// <returns>A collection of vehicles with the specified fuel type</returns>
    Task<IEnumerable<Vehicle>> GetByFuelTypeAsync(string fuelType, CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets vehicles by brand and model
    /// </summary>
    /// <param name="brand">The vehicle brand</param>
    /// <param name="model">The vehicle model</param>
    /// <param name="cancellationToken">The cancellation token</param>
    /// <returns>A collection of vehicles matching the brand and model</returns>
    Task<IEnumerable<Vehicle>> GetByBrandAndModelAsync(string brand, string? model = null, 
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets vehicles by year range
    /// </summary>
    /// <param name="fromYear">The starting year</param>
    /// <param name="toYear">The ending year</param>
    /// <param name="cancellationToken">The cancellation token</param>
    /// <returns>A collection of vehicles within the year range</returns>
    Task<IEnumerable<Vehicle>> GetByYearRangeAsync(int fromYear, int toYear, 
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets vehicles with mileage above a threshold
    /// </summary>
    /// <param name="mileageThreshold">The mileage threshold</param>
    /// <param name="cancellationToken">The cancellation token</param>
    /// <returns>A collection of vehicles with high mileage</returns>
    Task<IEnumerable<Vehicle>> GetHighMileageVehiclesAsync(decimal mileageThreshold, 
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets vehicle statistics by status
    /// </summary>
    /// <param name="cancellationToken">The cancellation token</param>
    /// <returns>A dictionary with status counts</returns>
    Task<Dictionary<VehicleStatus, int>> GetVehicleStatisticsByStatusAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets vehicle statistics by type
    /// </summary>
    /// <param name="cancellationToken">The cancellation token</param>
    /// <returns>A dictionary with type counts</returns>
    Task<Dictionary<string, int>> GetVehicleStatisticsByTypeAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// Searches vehicles by multiple criteria
    /// </summary>
    /// <param name="searchCriteria">The search criteria</param>
    /// <param name="cancellationToken">The cancellation token</param>
    /// <returns>A collection of vehicles matching the criteria</returns>
    Task<IEnumerable<Vehicle>> SearchVehiclesAsync(VehicleSearchCriteria searchCriteria, 
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Checks if a license plate is already in use
    /// </summary>
    /// <param name="licensePlate">The license plate to check</param>
    /// <param name="excludeVehicleId">Vehicle ID to exclude from the check</param>
    /// <param name="cancellationToken">The cancellation token</param>
    /// <returns>True if the license plate is in use, false otherwise</returns>
    Task<bool> IsLicensePlateInUseAsync(string licensePlate, Guid? excludeVehicleId = null, 
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Checks if a VIN is already in use
    /// </summary>
    /// <param name="vin">The VIN to check</param>
    /// <param name="excludeVehicleId">Vehicle ID to exclude from the check</param>
    /// <param name="cancellationToken">The cancellation token</param>
    /// <returns>True if the VIN is in use, false otherwise</returns>
    Task<bool> IsVinInUseAsync(string vin, Guid? excludeVehicleId = null, 
        CancellationToken cancellationToken = default);
}

/// <summary>
/// Represents search criteria for vehicles
/// </summary>
public class VehicleSearchCriteria
{
    /// <summary>
    /// Gets or sets the vehicle name or license plate search term
    /// </summary>
    public string? SearchTerm { get; set; }

    /// <summary>
    /// Gets or sets the vehicle status filter
    /// </summary>
    public VehicleStatus? Status { get; set; }

    /// <summary>
    /// Gets or sets the vehicle type filter
    /// </summary>
    public string? VehicleType { get; set; }

    /// <summary>
    /// Gets or sets the brand filter
    /// </summary>
    public string? Brand { get; set; }

    /// <summary>
    /// Gets or sets the model filter
    /// </summary>
    public string? Model { get; set; }

    /// <summary>
    /// Gets or sets the fuel type filter
    /// </summary>
    public string? FuelType { get; set; }

    /// <summary>
    /// Gets or sets the minimum year filter
    /// </summary>
    public int? MinYear { get; set; }

    /// <summary>
    /// Gets or sets the maximum year filter
    /// </summary>
    public int? MaxYear { get; set; }

    /// <summary>
    /// Gets or sets the minimum mileage filter
    /// </summary>
    public decimal? MinMileage { get; set; }

    /// <summary>
    /// Gets or sets the maximum mileage filter
    /// </summary>
    public decimal? MaxMileage { get; set; }

    /// <summary>
    /// Gets or sets a value indicating whether to include only available vehicles
    /// </summary>
    public bool? AvailableOnly { get; set; }

    /// <summary>
    /// Gets or sets a value indicating whether to include only vehicles needing maintenance
    /// </summary>
    public bool? NeedsMaintenanceOnly { get; set; }
}
