# FleetXQ Product Requirements Document (PRD)

## Overview
FleetXQ is a production-grade fleet management system designed to handle real-time vehicle tracking, driver behavior analytics, fuel and maintenance management, and administrative role-based access.

This document captures the technical and functional requirements as derived from the company's assessment brief.

---

## Objectives
- Implement a full-stack fleet management system using .NET 8, EF Core (Database-First), SignalR, and a modern frontend (React/Angular).
- Ingest and process real-time telemetry data.
- Support role-based access and secure authentication.
- Provide actionable analytics and dashboards for admins and managers.

---

## Architecture Summary

**Backend**
- ASP.NET Core Web API (.NET 8)
- Clean Architecture (API, Application, Domain, Infrastructure layers)
- EF Core (Database-First)
- MediatR (CQRS optional)
- JWT Authentication
- SignalR for real-time data push

**Frontend**
- React (preferred) or Angular
- Tailwind CSS or equivalent UI framework
- Recharts or charting library for telemetry visualization
- Role-aware dashboards
- SignalR client for live telemetry

**Database**
- SQL Server 2019+
- Provided schema includes:
  - Users, Vehicles, Drivers, TelemetryData, Trips, Alerts, Assignments, FuelTransactions, MaintenanceRecords, Geofences
  - Views: `VehicleCurrentStatus`, `DailyVehicleSummary`, `VehicleUtilization`, `AlertSummary`
  - Stored Procedures: `GetVehicleLocations`, `GetVehiclePerformance`
- Sample data and simulation scripts included

---

## Functional Requirements

### User Management
- Login with email/username and password
- JWT token generation and validation
- Roles: Admin, Manager, Driver, User
- API access restricted by role

### Vehicle Tracking
- Store real-time telemetry data: speed, location, fuel, engine metrics
- SignalR hub for live updates
- Vehicle dashboard shows current status, metrics, location

### Driver Management
- Link users to driver profiles
- Display driver licenses, status, assignments, and emergency contacts
- Analyze driver behavior and performance

### Trips and Analytics
- Fetch recent trips for drivers or vehicles
- Show trip metrics: distance, duration, fuel usage
- Use `Trips` table and `VehiclePerformance` stored proc

### Alerts and Notifications
- Fetch, filter, and acknowledge alerts
- Real-time alert push via SignalR optional
- Severity levels: Low, Medium, High, Critical

### Maintenance and Fuel
- CRUD for maintenance logs and fuel transactions
- Display upcoming maintenance
- Track cost breakdowns

### Geofencing
- Define circular geofences
- Alerts when breached

### Dashboard
- Display counts, recent activity, and summaries
- Pull from views: `VehicleUtilization`, `AlertSummary`

---

## Non-Functional Requirements
- Full system must be screen recorded with narration
- AI usage must be documented and shown in workflow
- Testing required: xUnit + Moq for backend unit tests
- Documentation in markdown files (.md)
- Deliverables submitted with separate video files per phase

---

## Development Constraints
- Must use database-first approach (provided schema)
- Do not modify DB schema; map to entities as-is
- Must demonstrate use of SignalR for telemetry
- Do not rely on paid third-party libraries or services

---

## Deliverables
- `db.md`, `backend.md`, `frontend.md`
- Clean codebase with layered architecture
- Functional API and UI
- Test coverage for application layer
- Narrated recordings for each dev phase
- Final summary and architectural documentation

---

## Notes
- This is a simulated client-level project, not a traditional coding test.
- Estimated real effort: 40–60 hours
- Project contains reusable production-grade components
