using FleetXQ.Domain.Entities;
using FleetXQ.Domain.Enums;
using FleetXQ.Domain.Events;
using FleetXQ.Domain.ValueObjects;
using Xunit;

namespace FleetXQ.Domain.Tests.Events;

public class DomainEventTests
{
    [Fact]
    public void TelemetryDataReceivedEvent_WithValidParameters_ShouldCreateEvent()
    {
        // Arrange
        var vehicleId = Guid.NewGuid();
        var location = new Location(40.7128m, -74.0060m);
        var speed = new Speed(60);
        var fuelLevel = new FuelLevel(75);
        var mileage = 1000m;
        var timestamp = DateTime.UtcNow;

        // Act
        var domainEvent = new TelemetryDataReceivedEvent(vehicleId, location, speed, fuelLevel, mileage, timestamp);

        // Assert
        Assert.Equal(vehicleId, domainEvent.VehicleId);
        Assert.Equal(location, domainEvent.Location);
        Assert.Equal(speed, domainEvent.Speed);
        Assert.Equal(fuelLevel, domainEvent.FuelLevel);
        Assert.Equal(mileage, domainEvent.Mileage);
        Assert.Equal(timestamp, domainEvent.Timestamp);
        Assert.True(domainEvent.OccurredOn <= DateTime.UtcNow);
    }

    [Fact]
    public void TelemetryDataReceivedEvent_WithNullLocation_ShouldThrowArgumentNullException()
    {
        // Arrange
        var vehicleId = Guid.NewGuid();
        var speed = new Speed(60);

        // Act & Assert
        Assert.Throws<ArgumentNullException>(() => 
            new TelemetryDataReceivedEvent(vehicleId, null!, speed));
    }

    [Fact]
    public void AlertTriggeredEvent_WithValidParameters_ShouldCreateEvent()
    {
        // Arrange
        var alertId = Guid.NewGuid();
        var alertType = AlertType.Speed;
        var severity = AlertSeverity.High;
        var message = "Speed limit exceeded";
        var vehicleId = Guid.NewGuid();
        var driverId = Guid.NewGuid();
        var location = new Location(40.7128m, -74.0060m);

        // Act
        var domainEvent = new AlertTriggeredEvent(alertId, alertType, severity, message, vehicleId, driverId, location);

        // Assert
        Assert.Equal(alertId, domainEvent.AlertId);
        Assert.Equal(alertType, domainEvent.AlertType);
        Assert.Equal(severity, domainEvent.Severity);
        Assert.Equal(message, domainEvent.Message);
        Assert.Equal(vehicleId, domainEvent.VehicleId);
        Assert.Equal(driverId, domainEvent.DriverId);
        Assert.Equal(location, domainEvent.Location);
        Assert.NotNull(domainEvent.Metadata);
    }

    [Fact]
    public void AlertTriggeredEvent_WithCriticalSeverity_ShouldRequireImmediateAttention()
    {
        // Arrange
        var alertId = Guid.NewGuid();
        var domainEvent = new AlertTriggeredEvent(alertId, AlertType.Fuel, AlertSeverity.Critical, "Critical fuel level");

        // Act & Assert
        Assert.True(domainEvent.RequiresImmediateAttention);
    }

    [Fact]
    public void AlertTriggeredEvent_WithNonCriticalSeverity_ShouldNotRequireImmediateAttention()
    {
        // Arrange
        var alertId = Guid.NewGuid();
        var domainEvent = new AlertTriggeredEvent(alertId, AlertType.Speed, AlertSeverity.Medium, "Speed alert");

        // Act & Assert
        Assert.False(domainEvent.RequiresImmediateAttention);
    }

    [Fact]
    public void AlertTriggeredEvent_ForSpeedViolation_ShouldCreateCorrectEvent()
    {
        // Arrange
        var alertId = Guid.NewGuid();
        var vehicleId = Guid.NewGuid();
        var driverId = Guid.NewGuid();
        var currentSpeed = new Speed(100);
        var speedLimit = new Speed(80);
        var location = new Location(40.7128m, -74.0060m);

        // Act
        var domainEvent = AlertTriggeredEvent.ForSpeedViolation(alertId, vehicleId, driverId, currentSpeed, speedLimit, location);

        // Assert
        Assert.Equal(AlertType.Speed, domainEvent.AlertType);
        Assert.Equal(vehicleId, domainEvent.VehicleId);
        Assert.Equal(driverId, domainEvent.DriverId);
        Assert.Equal(location, domainEvent.Location);
        Assert.Contains("Speed violation", domainEvent.Message);
        Assert.Contains("25.0% over limit", domainEvent.Message);
        Assert.Contains("currentSpeed", domainEvent.Metadata.Keys);
        Assert.Contains("speedLimit", domainEvent.Metadata.Keys);
        Assert.Contains("percentageOver", domainEvent.Metadata.Keys);
    }

    [Fact]
    public void AlertTriggeredEvent_ForLowFuel_ShouldCreateCorrectEvent()
    {
        // Arrange
        var alertId = Guid.NewGuid();
        var vehicleId = Guid.NewGuid();
        var fuelLevel = new FuelLevel(5); // Critical level
        var location = new Location(40.7128m, -74.0060m);

        // Act
        var domainEvent = AlertTriggeredEvent.ForLowFuel(alertId, vehicleId, fuelLevel, location);

        // Assert
        Assert.Equal(AlertType.Fuel, domainEvent.AlertType);
        Assert.Equal(AlertSeverity.Critical, domainEvent.Severity);
        Assert.Equal(vehicleId, domainEvent.VehicleId);
        Assert.Null(domainEvent.DriverId);
        Assert.Equal(location, domainEvent.Location);
        Assert.Contains("Low fuel alert", domainEvent.Message);
        Assert.Contains("fuelPercentage", domainEvent.Metadata.Keys);
        Assert.Contains("fuelCategory", domainEvent.Metadata.Keys);
    }

    [Fact]
    public void VehicleStatusChangedEvent_WithValidParameters_ShouldCreateEvent()
    {
        // Arrange
        var vehicleId = Guid.NewGuid();
        var previousStatus = VehicleStatus.Active;
        var newStatus = VehicleStatus.Maintenance;
        var reason = "Scheduled maintenance";
        var changedBy = Guid.NewGuid();

        // Act
        var domainEvent = new VehicleStatusChangedEvent(vehicleId, previousStatus, newStatus, reason, changedBy);

        // Assert
        Assert.Equal(vehicleId, domainEvent.VehicleId);
        Assert.Equal(previousStatus, domainEvent.PreviousStatus);
        Assert.Equal(newStatus, domainEvent.NewStatus);
        Assert.Equal(reason, domainEvent.Reason);
        Assert.Equal(changedBy, domainEvent.ChangedBy);
    }

    [Fact]
    public void VehicleStatusChangedEvent_WithSameStatus_ShouldThrowArgumentException()
    {
        // Arrange
        var vehicleId = Guid.NewGuid();
        var status = VehicleStatus.Active;

        // Act & Assert
        Assert.Throws<ArgumentException>(() => 
            new VehicleStatusChangedEvent(vehicleId, status, status));
    }

    [Fact]
    public void VehicleStatusChangedEvent_FromActiveToMaintenance_ShouldBeDegradation()
    {
        // Arrange
        var vehicleId = Guid.NewGuid();
        var domainEvent = new VehicleStatusChangedEvent(vehicleId, VehicleStatus.Active, VehicleStatus.Maintenance);

        // Act & Assert
        Assert.True(domainEvent.IsDegradation);
        Assert.False(domainEvent.IsImprovement);
        Assert.True(domainEvent.BecameUnavailable);
        Assert.False(domainEvent.BecameAvailable);
    }

    [Fact]
    public void VehicleStatusChangedEvent_FromMaintenanceToActive_ShouldBeImprovement()
    {
        // Arrange
        var vehicleId = Guid.NewGuid();
        var domainEvent = new VehicleStatusChangedEvent(vehicleId, VehicleStatus.Maintenance, VehicleStatus.Active);

        // Act & Assert
        Assert.True(domainEvent.IsImprovement);
        Assert.False(domainEvent.IsDegradation);
        Assert.True(domainEvent.BecameAvailable);
        Assert.False(domainEvent.BecameUnavailable);
    }

    [Fact]
    public void VehicleStatusChangedEvent_ForActivation_ShouldCreateCorrectEvent()
    {
        // Arrange
        var vehicleId = Guid.NewGuid();
        var previousStatus = VehicleStatus.Offline;
        var changedBy = Guid.NewGuid();

        // Act
        var domainEvent = VehicleStatusChangedEvent.ForActivation(vehicleId, previousStatus, changedBy, "Vehicle repaired");

        // Assert
        Assert.Equal(VehicleStatus.Active, domainEvent.NewStatus);
        Assert.Equal(previousStatus, domainEvent.PreviousStatus);
        Assert.Equal(changedBy, domainEvent.ChangedBy);
        Assert.Contains("Vehicle activated", domainEvent.Reason!);
        Assert.Contains("activation", domainEvent.Context["changeType"].ToString()!);
    }

    [Fact]
    public void VehicleStatusChangedEvent_GetImpact_ShouldReturnCorrectImpact()
    {
        // Arrange & Act & Assert
        var vehicleId = Guid.NewGuid();

        // Positive impact
        var positiveEvent = new VehicleStatusChangedEvent(vehicleId, VehicleStatus.Maintenance, VehicleStatus.Active);
        Assert.Equal(StatusChangeImpact.Positive, positiveEvent.GetImpact());

        // Negative impact
        var negativeEvent = new VehicleStatusChangedEvent(vehicleId, VehicleStatus.Active, VehicleStatus.Maintenance);
        Assert.Equal(StatusChangeImpact.Negative, negativeEvent.GetImpact());

        // Critical impact
        var criticalEvent = new VehicleStatusChangedEvent(vehicleId, VehicleStatus.Active, VehicleStatus.Retired);
        Assert.Equal(StatusChangeImpact.Critical, criticalEvent.GetImpact());

        // Neutral impact
        var neutralEvent = new VehicleStatusChangedEvent(vehicleId, VehicleStatus.Maintenance, VehicleStatus.Offline);
        Assert.Equal(StatusChangeImpact.Neutral, neutralEvent.GetImpact());
    }

    [Fact]
    public void DriverAssignedEvent_WithValidParameters_ShouldCreateEvent()
    {
        // Arrange
        var assignmentId = Guid.NewGuid();
        var vehicleId = Guid.NewGuid();
        var driverId = Guid.NewGuid();
        var assignmentType = AssignmentType.Primary;
        var assignedDate = DateTime.UtcNow;
        var assignedBy = Guid.NewGuid();

        // Act
        var domainEvent = new DriverAssignedEvent(assignmentId, vehicleId, driverId, assignmentType, assignedDate, null, assignedBy);

        // Assert
        Assert.Equal(assignmentId, domainEvent.AssignmentId);
        Assert.Equal(vehicleId, domainEvent.VehicleId);
        Assert.Equal(driverId, domainEvent.DriverId);
        Assert.Equal(assignmentType, domainEvent.AssignmentType);
        Assert.Equal(assignedDate, domainEvent.AssignedDate);
        Assert.Equal(assignedBy, domainEvent.AssignedBy);
        Assert.False(domainEvent.IsReassignment);
        Assert.False(domainEvent.IsTemporary);
        Assert.False(domainEvent.IsAutomated);
    }

    [Fact]
    public void DriverAssignedEvent_ForTemporaryAssignment_ShouldCreateCorrectEvent()
    {
        // Arrange
        var assignmentId = Guid.NewGuid();
        var vehicleId = Guid.NewGuid();
        var driverId = Guid.NewGuid();
        var endDate = DateTime.UtcNow.AddHours(8);

        // Act
        var domainEvent = DriverAssignedEvent.ForTemporaryAssignment(assignmentId, vehicleId, driverId, endDate);

        // Assert
        Assert.Equal(AssignmentType.Temporary, domainEvent.AssignmentType);
        Assert.Equal(endDate, domainEvent.EndDate);
        Assert.True(domainEvent.IsTemporary);
        Assert.True(domainEvent.IsAutomated); // No assignedBy provided
        Assert.Contains("temporary", domainEvent.Context["assignmentCategory"].ToString()!);
    }

    [Fact]
    public void DriverAssignedEvent_GetPriority_ShouldReturnCorrectPriority()
    {
        // Arrange
        var assignmentId = Guid.NewGuid();
        var vehicleId = Guid.NewGuid();
        var driverId = Guid.NewGuid();

        // Act & Assert
        var primaryEvent = DriverAssignedEvent.ForPrimaryAssignment(assignmentId, vehicleId, driverId);
        Assert.Equal(AssignmentPriority.High, primaryEvent.GetPriority());

        var secondaryEvent = DriverAssignedEvent.ForSecondaryAssignment(assignmentId, vehicleId, driverId);
        Assert.Equal(AssignmentPriority.Medium, secondaryEvent.GetPriority());

        var temporaryEvent = DriverAssignedEvent.ForTemporaryAssignment(assignmentId, vehicleId, driverId, DateTime.UtcNow.AddHours(1));
        Assert.Equal(AssignmentPriority.Low, temporaryEvent.GetPriority());
    }

    [Fact]
    public void DriverAssignedEvent_ConflictsWith_ShouldDetectConflicts()
    {
        // Arrange
        var assignmentId1 = Guid.NewGuid();
        var assignmentId2 = Guid.NewGuid();
        var vehicleId = Guid.NewGuid();
        var driverId = Guid.NewGuid();
        var otherDriverId = Guid.NewGuid();

        var assignment1 = DriverAssignedEvent.ForPrimaryAssignment(assignmentId1, vehicleId, driverId);
        var assignment2 = DriverAssignedEvent.ForPrimaryAssignment(assignmentId2, vehicleId, otherDriverId);

        // Act & Assert
        Assert.True(assignment1.ConflictsWith(assignment2)); // Same vehicle, different drivers
        Assert.True(assignment2.ConflictsWith(assignment1));
    }
}
