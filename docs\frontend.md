
# react.md

## React Project Best Practices

### 1. Project Structure

```
/src
  /assets
  /components
  /features
    /auth
      AuthPage.jsx
      authSlice.js
    /dashboard
      DashboardPage.jsx
      dashboardSlice.js
  /hooks
  /layouts
  /routes
  /services
  /store
  /types
  /utils
  App.jsx
  main.jsx
```

### Guidelines

- **/components**: Reusable, generic UI components.
- **/features**: Domain-specific logic (Redux slices, pages).
- **/services**: API abstraction layer (e.g., Axios clients).
- **/store**: Central Redux configuration.
- **/utils**: Pure utility functions.
- **/hooks**: Custom React hooks.
- **/layouts**: Page layout components.
- **/routes**: Route definitions (e.g., React Router).
- **/types**: TypeScript interfaces/types (omit if using JS).

---

### 2. Naming Conventions

| Item               | Convention             |
|--------------------|------------------------|
| Components         | PascalCase             |
| Files              | PascalCase or kebab-case (consistent) |
| Variables          | camelCase              |
| Functions          | camelCase              |
| Custom Hooks       | usePrefix (e.g., `useAuth`) |
| Redux Slices       | featureSlice.js        |
| CSS Modules        | Component.module.css   |

---

### 3. Component Guidelines

- Keep components small and focused.
- Split into presentational and container components if complexity grows.
- Use function components and hooks; avoid class components.
- Use `React.memo` for performance optimization when necessary.

---

### 4. State Management

- Prefer local state (`useState`) when possible.
- Use `useReducer` for complex local state logic.
- Use Redux Toolkit for global state with slices.
- Normalize nested data before storing in Redux.

---

### 5. API and Services

- Centralize all API calls in `/services`.
- Use Axios with interceptors for authentication.

```js
// services/api.js
const api = axios.create({
  baseURL: import.meta.env.VITE_API_URL,
  headers: { 'Content-Type': 'application/json' }
});
```

---

### 6. Routing

- Use `react-router-dom` for routing.
- Structure route components under `/routes`.
- Implement route guards for authentication/authorization.

---

### 7. Hooks

- Prefix with `use` (e.g., `useFetch`, `useToggle`).
- Extract logic shared between components.

---

### 8. Styling

- Use CSS Modules or Tailwind CSS.
- Avoid global styles when possible.
- For theming, use context or Tailwind config.

---

### 9. Testing

- Use `Jest` and `React Testing Library`.
- Structure tests next to components or in `/__tests__/`.
- Use `msw` (Mock Service Worker) to mock API responses.

---

### 10. Code Quality

- Use ESLint + Prettier
- Enforce via `.eslintrc`, `.prettierrc`
- Enable strict mode in React

```js
<React.StrictMode>
  <App />
</React.StrictMode>
```

---

### 11. Environment Configuration

Use `.env` files for config:

```
VITE_API_URL=https://api.example.com
```

Access via `import.meta.env.VITE_API_URL` in Vite or `process.env.REACT_APP_API_URL` in CRA.

---

### 12. Deployment

- Use Vite or CRA build output (`dist` or `build`)
- Deploy via Netlify, Vercel, or Docker
- Ensure proper route handling for SPAs

---

### 13. Summary

| Concern          | Preferred Approach             |
|------------------|--------------------------------|
| Structure        | Feature-based folders          |
| State            | Redux Toolkit + local state    |
| API              | Centralized Axios instance     |
| Styling          | Tailwind / CSS Modules         |
| Naming           | Consistent, semantic names     |
| Routing          | `react-router-dom`             |
| Testing          | RTL + Jest + msw               |
| Code Format      | ESLint + Prettier              |
