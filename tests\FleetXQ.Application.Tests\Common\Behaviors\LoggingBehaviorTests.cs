using FleetXQ.Application.Common.Behaviors;
using FluentAssertions;
using MediatR;
using Microsoft.Extensions.Logging;
using Moq;

namespace FleetXQ.Application.Tests.Common.Behaviors;

public class LoggingBehaviorTests
{
    private readonly Mock<ILogger<LoggingBehavior<TestRequest, TestResponse>>> _mockLogger;
    private readonly Mock<RequestHandlerDelegate<TestResponse>> _mockNext;
    private readonly LoggingBehavior<TestRequest, TestResponse> _behavior;

    public LoggingBehaviorTests()
    {
        _mockLogger = new Mock<ILogger<LoggingBehavior<TestRequest, TestResponse>>>();
        _mockNext = new Mock<RequestHandlerDelegate<TestResponse>>();
        _behavior = new LoggingBehavior<TestRequest, TestResponse>(_mockLogger.Object);
    }

    [Fact]
    public async Task Handle_WithSuccessfulRequest_ShouldLogStartAndCompletion()
    {
        // Arrange
        var request = new TestRequest { Value = "test", Password = "secret123" };
        var expectedResponse = new TestResponse { Result = "success" };

        _mockNext.Setup(x => x()).ReturnsAsync(expectedResponse);

        // Act
        var result = await _behavior.Handle(request, _mockNext.Object, CancellationToken.None);

        // Assert
        result.Should().Be(expectedResponse);

        // Verify start logging
        VerifyLoggerCalled(LogLevel.Information, Times.Once, "Starting request");

        // Verify completion logging
        VerifyLoggerCalled(LogLevel.Information, Times.Exactly(2), "Completed request");

        _mockNext.Verify(x => x(), Times.Once);
    }

    [Fact]
    public async Task Handle_WithException_ShouldLogError()
    {
        // Arrange
        var request = new TestRequest { Value = "test" };
        var exception = new InvalidOperationException("Test exception");

        _mockNext.Setup(x => x()).ThrowsAsync(exception);

        // Act & Assert
        var thrownException = await Assert.ThrowsAsync<InvalidOperationException>(() =>
            _behavior.Handle(request, _mockNext.Object, CancellationToken.None));

        thrownException.Should().Be(exception);

        // Verify start logging
        VerifyLoggerCalled(LogLevel.Information, Times.Once, "Starting request");

        // Verify error logging
        VerifyLoggerCalled(LogLevel.Error, Times.Once, "failed");

        _mockNext.Verify(x => x(), Times.Once);
    }

    [Fact]
    public async Task Handle_WithDebugLogging_ShouldLogRequestAndResponseDetails()
    {
        // Arrange
        var request = new TestRequest { Value = "test", Password = "secret123" };
        var expectedResponse = new TestResponse { Result = "success", Token = "jwt_token" };

        _mockLogger.Setup(x => x.IsEnabled(LogLevel.Debug)).Returns(true);
        _mockNext.Setup(x => x()).ReturnsAsync(expectedResponse);

        // Act
        var result = await _behavior.Handle(request, _mockNext.Object, CancellationToken.None);

        // Assert
        result.Should().Be(expectedResponse);

        // Verify debug logging for request details
        VerifyLoggerCalled(LogLevel.Debug, Times.Once, "details");

        // Verify debug logging for response details
        VerifyLoggerCalled(LogLevel.Debug, Times.Exactly(2));
    }

    [Fact]
    public async Task Handle_WithoutDebugLogging_ShouldNotLogRequestAndResponseDetails()
    {
        // Arrange
        var request = new TestRequest { Value = "test", Password = "secret123" };
        var expectedResponse = new TestResponse { Result = "success" };

        _mockLogger.Setup(x => x.IsEnabled(LogLevel.Debug)).Returns(false);
        _mockNext.Setup(x => x()).ReturnsAsync(expectedResponse);

        // Act
        var result = await _behavior.Handle(request, _mockNext.Object, CancellationToken.None);

        // Assert
        result.Should().Be(expectedResponse);

        // Verify no debug logging
        VerifyLoggerCalled(LogLevel.Debug, Times.Never);
    }

    [Fact]
    public async Task Handle_ShouldMeasureExecutionTime()
    {
        // Arrange
        var request = new TestRequest { Value = "test" };
        var expectedResponse = new TestResponse { Result = "success" };

        _mockNext.Setup(x => x()).Returns(async () =>
        {
            await Task.Delay(100); // Simulate some processing time
            return expectedResponse;
        });

        // Act
        var result = await _behavior.Handle(request, _mockNext.Object, CancellationToken.None);

        // Assert
        result.Should().Be(expectedResponse);

        // Verify that completion log includes elapsed time
        _mockLogger.Verify(
            x => x.Log(
                LogLevel.Information,
                It.IsAny<EventId>(),
                It.Is<It.IsAnyType>((v, t) => v.ToString()!.Contains("ms")),
                It.IsAny<Exception>(),
                It.IsAny<Func<It.IsAnyType, Exception?, string>>()),
            Times.AtLeastOnce);
    }

    [Fact]
    public async Task Handle_WithSensitiveData_ShouldMaskPasswordInLogs()
    {
        // Arrange
        var request = new TestRequest { Value = "test", Password = "secret123" };
        var expectedResponse = new TestResponse { Result = "success" };

        _mockLogger.Setup(x => x.IsEnabled(LogLevel.Debug)).Returns(true);
        _mockNext.Setup(x => x()).ReturnsAsync(expectedResponse);

        // Act
        var result = await _behavior.Handle(request, _mockNext.Object, CancellationToken.None);

        // Assert
        result.Should().Be(expectedResponse);

        // Verify that sensitive data is masked in debug logs
        _mockLogger.Verify(
            x => x.Log(
                LogLevel.Debug,
                It.IsAny<EventId>(),
                It.Is<It.IsAnyType>((v, t) => v.ToString()!.Contains("[MASKED]")),
                It.IsAny<Exception>(),
                It.IsAny<Func<It.IsAnyType, Exception?, string>>()),
            Times.AtLeastOnce);
    }

    [Fact]
    public async Task Handle_WithTokenInResponse_ShouldMaskTokenInLogs()
    {
        // Arrange
        var request = new TestRequest { Value = "test" };
        var expectedResponse = new TestResponse { Result = "success", Token = "jwt_token_secret" };

        _mockLogger.Setup(x => x.IsEnabled(LogLevel.Debug)).Returns(true);
        _mockNext.Setup(x => x()).ReturnsAsync(expectedResponse);

        // Act
        var result = await _behavior.Handle(request, _mockNext.Object, CancellationToken.None);

        // Assert
        result.Should().Be(expectedResponse);

        // Verify that token is masked in debug logs
        _mockLogger.Verify(
            x => x.Log(
                LogLevel.Debug,
                It.IsAny<EventId>(),
                It.Is<It.IsAnyType>((v, t) => v.ToString()!.Contains("[MASKED]")),
                It.IsAny<Exception>(),
                It.IsAny<Func<It.IsAnyType, Exception?, string>>()),
            Times.AtLeastOnce);
    }

    [Fact]
    public async Task Handle_WithSerializationError_ShouldHandleGracefully()
    {
        // Arrange
        var request = new TestRequest { Value = "test" };
        var expectedResponse = new TestResponse { Result = "success" };

        _mockLogger.Setup(x => x.IsEnabled(LogLevel.Debug)).Returns(true);
        _mockNext.Setup(x => x()).ReturnsAsync(expectedResponse);

        // Act
        var result = await _behavior.Handle(request, _mockNext.Object, CancellationToken.None);

        // Assert
        result.Should().Be(expectedResponse);

        // The behavior should complete successfully even if serialization fails
        _mockNext.Verify(x => x(), Times.Once);
    }

    [Fact]
    public async Task Handle_WithCancellationToken_ShouldPassTokenToNext()
    {
        // Arrange
        var request = new TestRequest { Value = "test" };
        var expectedResponse = new TestResponse { Result = "success" };
        var cancellationToken = new CancellationTokenSource().Token;

        _mockNext.Setup(x => x()).ReturnsAsync(expectedResponse);

        // Act
        var result = await _behavior.Handle(request, _mockNext.Object, cancellationToken);

        // Assert
        result.Should().Be(expectedResponse);
        _mockNext.Verify(x => x(), Times.Once);
    }

    [Fact]
    public async Task Handle_ShouldGenerateUniqueRequestId()
    {
        // Arrange
        var request = new TestRequest { Value = "test" };
        var expectedResponse = new TestResponse { Result = "success" };

        _mockNext.Setup(x => x()).ReturnsAsync(expectedResponse);

        // Act
        await _behavior.Handle(request, _mockNext.Object, CancellationToken.None);

        // Assert
        // Verify that logs contain a request ID (GUID format)
        _mockLogger.Verify(
            x => x.Log(
                LogLevel.Information,
                It.IsAny<EventId>(),
                It.Is<It.IsAnyType>((v, t) => v.ToString()!.Contains("ID")),
                It.IsAny<Exception>(),
                It.IsAny<Func<It.IsAnyType, Exception?, string>>()),
            Times.AtLeastOnce);
    }

    private void VerifyLoggerCalled(LogLevel logLevel, Times times, string? messageContains = null)
    {
        _mockLogger.Verify(
            x => x.Log(
                logLevel,
                It.IsAny<EventId>(),
                messageContains != null 
                    ? It.Is<It.IsAnyType>((v, t) => v.ToString()!.Contains(messageContains))
                    : It.IsAny<It.IsAnyType>(),
                It.IsAny<Exception>(),
                It.IsAny<Func<It.IsAnyType, Exception?, string>>()),
            times);
    }

    // Test helper classes
    public class TestRequest : IRequest<TestResponse>
    {
        public string Value { get; set; } = string.Empty;
        public string Password { get; set; } = string.Empty;
    }

    public class TestResponse
    {
        public string Result { get; set; } = string.Empty;
        public string Token { get; set; } = string.Empty;
    }
}
