using FleetXQ.Application.Features.Telemetry.DTOs;
using MediatR;

namespace FleetXQ.Application.Features.Telemetry.Queries.GetLatestTelemetry;

/// <summary>
/// Query to get the latest telemetry data for a vehicle or all vehicles
/// </summary>
public sealed class GetLatestTelemetryQuery : IRequest<GetLatestTelemetryResult>
{
    /// <summary>
    /// Gets or sets the vehicle ID (optional - if not provided, returns latest telemetry for all vehicles)
    /// </summary>
    public Guid? VehicleId { get; set; }

    /// <summary>
    /// Gets or sets a value indicating whether to include only active vehicles
    /// </summary>
    public bool ActiveVehiclesOnly { get; set; } = true;

    /// <summary>
    /// Gets or sets the maximum age of telemetry data in hours
    /// </summary>
    public int MaxAgeHours { get; set; } = 24;

    /// <summary>
    /// Initializes a new instance of the <see cref="GetLatestTelemetryQuery"/> class
    /// </summary>
    public GetLatestTelemetryQuery()
    {
    }

    /// <summary>
    /// Initializes a new instance of the <see cref="GetLatestTelemetryQuery"/> class for a specific vehicle
    /// </summary>
    /// <param name="vehicleId">The vehicle ID</param>
    public GetLatestTelemetryQuery(Guid vehicleId)
    {
        VehicleId = vehicleId;
    }
}

/// <summary>
/// Result of getting latest telemetry data
/// </summary>
public sealed class GetLatestTelemetryResult
{
    /// <summary>
    /// Gets or sets the telemetry data for a single vehicle (when VehicleId is specified)
    /// </summary>
    public TelemetryDto? Telemetry { get; set; }

    /// <summary>
    /// Gets or sets the telemetry data for multiple vehicles (when VehicleId is not specified)
    /// </summary>
    public IEnumerable<TelemetryDto> TelemetryList { get; set; } = new List<TelemetryDto>();

    /// <summary>
    /// Gets or sets a value indicating whether the operation was successful
    /// </summary>
    public bool Success { get; set; }

    /// <summary>
    /// Gets or sets the error message if the operation failed
    /// </summary>
    public string? ErrorMessage { get; set; }

    /// <summary>
    /// Gets or sets the timestamp when the data was retrieved
    /// </summary>
    public DateTime RetrievedAt { get; set; }

    /// <summary>
    /// Creates a successful result for a single vehicle
    /// </summary>
    /// <param name="telemetry">The telemetry data</param>
    /// <returns>A successful result</returns>
    public static GetLatestTelemetryResult CreateSuccess(TelemetryDto telemetry)
    {
        return new GetLatestTelemetryResult
        {
            Telemetry = telemetry,
            Success = true,
            RetrievedAt = DateTime.UtcNow
        };
    }

    /// <summary>
    /// Creates a successful result for multiple vehicles
    /// </summary>
    /// <param name="telemetryList">The telemetry data list</param>
    /// <returns>A successful result</returns>
    public static GetLatestTelemetryResult SuccessMultiple(IEnumerable<TelemetryDto> telemetryList)
    {
        return new GetLatestTelemetryResult
        {
            TelemetryList = telemetryList,
            Success = true,
            RetrievedAt = DateTime.UtcNow
        };
    }

    /// <summary>
    /// Creates a not found result
    /// </summary>
    /// <returns>A not found result</returns>
    public static GetLatestTelemetryResult NotFound()
    {
        return new GetLatestTelemetryResult
        {
            Success = false,
            ErrorMessage = "No telemetry data found",
            RetrievedAt = DateTime.UtcNow
        };
    }

    /// <summary>
    /// Creates a failed result
    /// </summary>
    /// <param name="errorMessage">The error message</param>
    /// <returns>A failed result</returns>
    public static GetLatestTelemetryResult Failed(string errorMessage)
    {
        return new GetLatestTelemetryResult
        {
            Success = false,
            ErrorMessage = errorMessage,
            RetrievedAt = DateTime.UtcNow
        };
    }
}
