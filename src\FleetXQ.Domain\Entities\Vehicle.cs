using FleetXQ.Domain.Common;
using FleetXQ.Domain.Enums;
using FleetXQ.Domain.ValueObjects;
using FleetXQ.Domain.Events;

namespace FleetXQ.Domain.Entities;

/// <summary>
/// Represents a vehicle in the fleet management system
/// </summary>
public class Vehicle : BaseEntity
{
    /// <summary>
    /// Gets or sets the vehicle name
    /// </summary>
    public string VehicleName { get; private set; } = string.Empty;

    /// <summary>
    /// Gets or sets the license plate number
    /// </summary>
    public string LicensePlate { get; private set; } = string.Empty;

    /// <summary>
    /// Gets or sets the Vehicle Identification Number (VIN)
    /// </summary>
    public string? VIN { get; private set; }

    /// <summary>
    /// Gets or sets the vehicle type
    /// </summary>
    public string VehicleType { get; private set; } = string.Empty;

    /// <summary>
    /// Gets or sets the vehicle brand
    /// </summary>
    public string? Brand { get; private set; }

    /// <summary>
    /// Gets or sets the vehicle model
    /// </summary>
    public string? Model { get; private set; }

    /// <summary>
    /// Gets or sets the vehicle year
    /// </summary>
    public int? Year { get; private set; }

    /// <summary>
    /// Gets or sets the vehicle color
    /// </summary>
    public string? Color { get; private set; }

    /// <summary>
    /// Gets or sets the fuel tank capacity in liters
    /// </summary>
    public decimal? FuelTankCapacity { get; private set; }

    /// <summary>
    /// Gets or sets the fuel type
    /// </summary>
    public string FuelType { get; private set; } = "Gasoline";

    /// <summary>
    /// Gets or sets the vehicle status
    /// </summary>
    public VehicleStatus Status { get; private set; }

    /// <summary>
    /// Gets or sets the last maintenance date
    /// </summary>
    public DateTime? LastMaintenanceDate { get; private set; }

    /// <summary>
    /// Gets or sets the next maintenance date
    /// </summary>
    public DateTime? NextMaintenanceDate { get; private set; }

    /// <summary>
    /// Gets or sets the purchase date
    /// </summary>
    public DateTime? PurchaseDate { get; private set; }

    /// <summary>
    /// Gets or sets the purchase price
    /// </summary>
    public decimal? PurchasePrice { get; private set; }

    /// <summary>
    /// Gets or sets the current mileage
    /// </summary>
    public decimal CurrentMileage { get; private set; }

    /// <summary>
    /// Gets the current location of the vehicle
    /// </summary>
    public Location? CurrentLocation { get; private set; }

    /// <summary>
    /// Gets the current speed of the vehicle
    /// </summary>
    public Speed CurrentSpeed { get; private set; } = Speed.Zero;

    /// <summary>
    /// Gets the current fuel level of the vehicle
    /// </summary>
    public FuelLevel? CurrentFuelLevel { get; private set; }

    /// <summary>
    /// Gets the last telemetry update timestamp
    /// </summary>
    public DateTime? LastTelemetryUpdate { get; private set; }

    /// <summary>
    /// Gets a value indicating whether the vehicle is currently moving
    /// </summary>
    public bool IsMoving => !CurrentSpeed.IsStationary;

    /// <summary>
    /// Gets a value indicating whether the vehicle needs maintenance
    /// </summary>
    public bool NeedsMaintenance => NextMaintenanceDate.HasValue && NextMaintenanceDate.Value <= DateTime.UtcNow;

    /// <summary>
    /// Gets a value indicating whether the vehicle is available for assignment
    /// </summary>
    public bool IsAvailable => Status == VehicleStatus.Active && !NeedsMaintenance;

    /// <summary>
    /// Private constructor for Entity Framework
    /// </summary>
    private Vehicle() { }

    /// <summary>
    /// Initializes a new instance of the <see cref="Vehicle"/> class
    /// </summary>
    /// <param name="vehicleName">The vehicle name</param>
    /// <param name="licensePlate">The license plate number</param>
    /// <param name="vehicleType">The vehicle type</param>
    /// <param name="fuelType">The fuel type</param>
    public Vehicle(string vehicleName, string licensePlate, string vehicleType, string fuelType = "Gasoline")
    {
        if (string.IsNullOrWhiteSpace(vehicleName))
            throw new ArgumentException("Vehicle name cannot be empty", nameof(vehicleName));
        
        if (string.IsNullOrWhiteSpace(licensePlate))
            throw new ArgumentException("License plate cannot be empty", nameof(licensePlate));
        
        if (string.IsNullOrWhiteSpace(vehicleType))
            throw new ArgumentException("Vehicle type cannot be empty", nameof(vehicleType));

        VehicleName = vehicleName;
        LicensePlate = licensePlate;
        VehicleType = vehicleType;
        FuelType = fuelType;
        Status = VehicleStatus.Active;
        CurrentMileage = 0;
    }

    /// <summary>
    /// Updates the vehicle's basic information
    /// </summary>
    /// <param name="vehicleName">The vehicle name</param>
    /// <param name="brand">The vehicle brand</param>
    /// <param name="model">The vehicle model</param>
    /// <param name="year">The vehicle year</param>
    /// <param name="color">The vehicle color</param>
    public void UpdateBasicInfo(string vehicleName, string? brand = null, string? model = null, int? year = null, string? color = null)
    {
        if (string.IsNullOrWhiteSpace(vehicleName))
            throw new ArgumentException("Vehicle name cannot be empty", nameof(vehicleName));

        VehicleName = vehicleName;
        Brand = brand;
        Model = model;
        Year = year;
        Color = color;
    }

    /// <summary>
    /// Updates the vehicle's fuel tank capacity
    /// </summary>
    /// <param name="capacity">The fuel tank capacity in liters</param>
    public void UpdateFuelTankCapacity(decimal capacity)
    {
        if (capacity <= 0)
            throw new ArgumentOutOfRangeException(nameof(capacity), "Fuel tank capacity must be positive");

        FuelTankCapacity = capacity;
    }

    /// <summary>
    /// Changes the vehicle status
    /// </summary>
    /// <param name="newStatus">The new status</param>
    /// <param name="reason">The reason for the status change</param>
    public void ChangeStatus(VehicleStatus newStatus, string? reason = null)
    {
        if (Status == newStatus)
            return;

        var previousStatus = Status;
        Status = newStatus;

        AddDomainEvent(new VehicleStatusChangedEvent(Id, previousStatus, newStatus, reason));
    }

    /// <summary>
    /// Updates the vehicle's telemetry data
    /// </summary>
    /// <param name="location">The current location</param>
    /// <param name="speed">The current speed</param>
    /// <param name="fuelLevel">The current fuel level</param>
    /// <param name="mileage">The current mileage</param>
    public void UpdateTelemetry(Location location, Speed speed, FuelLevel? fuelLevel = null, decimal? mileage = null)
    {
        CurrentLocation = location ?? throw new ArgumentNullException(nameof(location));
        CurrentSpeed = speed ?? throw new ArgumentNullException(nameof(speed));
        
        if (fuelLevel != null)
            CurrentFuelLevel = fuelLevel;
        
        if (mileage.HasValue && mileage.Value >= CurrentMileage)
            CurrentMileage = mileage.Value;

        LastTelemetryUpdate = DateTime.UtcNow;

        AddDomainEvent(new TelemetryDataReceivedEvent(Id, location, speed, fuelLevel, mileage));
    }

    /// <summary>
    /// Schedules maintenance for the vehicle
    /// </summary>
    /// <param name="nextMaintenanceDate">The next maintenance date</param>
    public void ScheduleMaintenance(DateTime nextMaintenanceDate)
    {
        if (nextMaintenanceDate <= DateTime.UtcNow)
            throw new ArgumentException("Next maintenance date must be in the future", nameof(nextMaintenanceDate));

        NextMaintenanceDate = nextMaintenanceDate;
    }

    /// <summary>
    /// Records completed maintenance
    /// </summary>
    /// <param name="maintenanceDate">The maintenance completion date</param>
    /// <param name="nextMaintenanceDate">The next scheduled maintenance date</param>
    public void RecordMaintenanceCompleted(DateTime maintenanceDate, DateTime? nextMaintenanceDate = null)
    {
        LastMaintenanceDate = maintenanceDate;
        NextMaintenanceDate = nextMaintenanceDate;

        // If vehicle was in maintenance status, change it back to active
        if (Status == VehicleStatus.Maintenance)
        {
            ChangeStatus(VehicleStatus.Active, "Maintenance completed");
        }
    }

    /// <summary>
    /// Checks if the vehicle requires immediate attention based on fuel level
    /// </summary>
    /// <returns>True if fuel level is critical, false otherwise</returns>
    public bool RequiresImmediateAttention()
    {
        return CurrentFuelLevel?.IsCritical == true;
    }

    /// <summary>
    /// Gets the estimated range based on current fuel level and average consumption
    /// </summary>
    /// <param name="averageConsumptionPer100Km">Average fuel consumption per 100km</param>
    /// <returns>Estimated range in kilometers, or null if fuel level is unknown</returns>
    public decimal? GetEstimatedRange(decimal averageConsumptionPer100Km)
    {
        if (CurrentFuelLevel == null || FuelTankCapacity == null || averageConsumptionPer100Km <= 0)
            return null;

        var currentFuelLiters = CurrentFuelLevel.ToLiters(FuelTankCapacity.Value);
        return (currentFuelLiters / averageConsumptionPer100Km) * 100;
    }
}
