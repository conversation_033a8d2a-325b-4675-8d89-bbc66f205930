using AutoMapper;
using FleetXQ.Application.Features.Alerts.DTOs;
using FleetXQ.Application.Features.Authentication.DTOs;
using FleetXQ.Application.Features.Drivers.DTOs;
using FleetXQ.Application.Features.Telemetry.DTOs;
using FleetXQ.Application.Features.Vehicles.DTOs;
using FleetXQ.Domain.Entities;
using FleetXQ.Domain.ValueObjects;

namespace FleetXQ.Application.Common.Mappings;

/// <summary>
/// AutoMapper profile for mapping between domain entities and DTOs
/// </summary>
public class MappingProfile : Profile
{
    /// <summary>
    /// Initializes a new instance of the <see cref="MappingProfile"/> class
    /// </summary>
    public MappingProfile()
    {
        CreateVehicleMappings();
        CreateTelemetryMappings();
        CreateUserMappings();
        CreateDriverMappings();
        CreateAlertMappings();
        CreateValueObjectMappings();
    }

    /// <summary>
    /// Creates vehicle-related mappings
    /// </summary>
    private void CreateVehicleMappings()
    {
        // Vehicle entity to DTOs
        CreateMap<Vehicle, VehicleDto>()
            .ForMember(dest => dest.CurrentSpeedKmh, opt => opt.MapFrom(src => src.CurrentSpeed.KilometersPerHour))
            .ForMember(dest => dest.CurrentFuelLevelPercentage, opt => opt.MapFrom(src => src.CurrentFuelLevel != null ? src.CurrentFuelLevel.Percentage : (decimal?)null))
            .ForMember(dest => dest.CurrentLocation, opt => opt.MapFrom(src => src.CurrentLocation))
            .ForMember(dest => dest.IsAvailable, opt => opt.MapFrom(src => src.IsAvailable))
            .ForMember(dest => dest.IsMoving, opt => opt.MapFrom(src => src.IsMoving))
            .ForMember(dest => dest.NeedsMaintenance, opt => opt.MapFrom(src => src.NeedsMaintenance));

        CreateMap<Vehicle, VehicleListDto>()
            .ForMember(dest => dest.CurrentFuelLevelPercentage, opt => opt.MapFrom(src => src.CurrentFuelLevel != null ? src.CurrentFuelLevel.Percentage : (decimal?)null))
            .ForMember(dest => dest.CurrentLocation, opt => opt.MapFrom(src => src.CurrentLocation))
            .ForMember(dest => dest.IsAvailable, opt => opt.MapFrom(src => src.IsAvailable))
            .ForMember(dest => dest.IsMoving, opt => opt.MapFrom(src => src.IsMoving))
            .ForMember(dest => dest.NeedsMaintenance, opt => opt.MapFrom(src => src.NeedsMaintenance));
    }

    /// <summary>
    /// Creates telemetry-related mappings
    /// </summary>
    private void CreateTelemetryMappings()
    {
        // Vehicle to TelemetryDto (for current telemetry data)
        CreateMap<Vehicle, TelemetryDto>()
            .ForMember(dest => dest.VehicleId, opt => opt.MapFrom(src => src.Id))
            .ForMember(dest => dest.Location, opt => opt.MapFrom(src => src.CurrentLocation))
            .ForMember(dest => dest.SpeedKmh, opt => opt.MapFrom(src => src.CurrentSpeed.KilometersPerHour))
            .ForMember(dest => dest.SpeedMph, opt => opt.MapFrom(src => src.CurrentSpeed.MilesPerHour))
            .ForMember(dest => dest.FuelLevelPercentage, opt => opt.MapFrom(src => src.CurrentFuelLevel != null ? src.CurrentFuelLevel.Percentage : (decimal?)null))
            .ForMember(dest => dest.IsMoving, opt => opt.MapFrom(src => src.IsMoving))
            .ForMember(dest => dest.Timestamp, opt => opt.MapFrom(src => src.LastTelemetryUpdate ?? DateTime.UtcNow));
    }

    /// <summary>
    /// Creates user-related mappings
    /// </summary>
    private void CreateUserMappings()
    {
        // User entity to DTOs
        CreateMap<User, UserProfileDto>()
            .ForMember(dest => dest.FullName, opt => opt.MapFrom(src => src.FullName))
            .ForMember(dest => dest.IsLockedOut, opt => opt.MapFrom(src => src.IsLockedOut))
            .ForMember(dest => dest.Permissions, opt => opt.Ignore()); // Set manually in handlers
    }

    /// <summary>
    /// Creates driver-related mappings
    /// </summary>
    private void CreateDriverMappings()
    {
        // Driver entity to DTOs
        CreateMap<Driver, DriverDto>()
            .ForMember(dest => dest.FullName, opt => opt.MapFrom(src => src.FullName))
            .ForMember(dest => dest.IsAvailable, opt => opt.MapFrom(src => src.IsAvailable))
            .ForMember(dest => dest.IsLicenseValid, opt => opt.MapFrom(src => src.IsLicenseValid))
            .ForMember(dest => dest.AssignedVehicleId, opt => opt.MapFrom(src => src.AssignedVehicleId))
            .ForMember(dest => dest.AssignedVehicleName, opt => opt.Ignore()); // Would be populated from join or separate query

        CreateMap<Driver, DriverListDto>()
            .ForMember(dest => dest.FullName, opt => opt.MapFrom(src => src.FullName))
            .ForMember(dest => dest.IsAvailable, opt => opt.MapFrom(src => src.IsAvailable))
            .ForMember(dest => dest.IsLicenseValid, opt => opt.MapFrom(src => src.IsLicenseValid))
            .ForMember(dest => dest.AssignedVehicleName, opt => opt.Ignore()); // Would be populated from join or separate query
    }

    /// <summary>
    /// Creates alert-related mappings
    /// </summary>
    private void CreateAlertMappings()
    {
        // Alert entity to DTOs
        CreateMap<Alert, AlertDto>()
            .ForMember(dest => dest.VehicleName, opt => opt.Ignore()) // Would be populated from join or separate query
            .ForMember(dest => dest.DriverName, opt => opt.Ignore()) // Would be populated from join or separate query
            .ForMember(dest => dest.AcknowledgedBy, opt => opt.Ignore()) // Would be populated from user lookup
            .ForMember(dest => dest.ResolvedBy, opt => opt.Ignore()) // Would be populated from user lookup
            .ForMember(dest => dest.RequiresImmediateAttention, opt => opt.MapFrom(src => src.RequiresImmediateAttention));

        CreateMap<Alert, AlertListDto>()
            .ForMember(dest => dest.VehicleName, opt => opt.Ignore()) // Would be populated from join or separate query
            .ForMember(dest => dest.DriverName, opt => opt.Ignore()) // Would be populated from join or separate query
            .ForMember(dest => dest.RequiresImmediateAttention, opt => opt.MapFrom(src => src.RequiresImmediateAttention));
    }

    /// <summary>
    /// Creates value object mappings
    /// </summary>
    private void CreateValueObjectMappings()
    {
        // Location value object to DTO
        CreateMap<Location, LocationDto>()
            .ForMember(dest => dest.Address, opt => opt.Ignore()); // Address would come from external service
    }
}
