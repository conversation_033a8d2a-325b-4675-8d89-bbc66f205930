using FleetXQ.Domain.Common;
using FleetXQ.Domain.ValueObjects;

namespace FleetXQ.Domain.Events;

/// <summary>
/// Domain event raised when telemetry data is received from a vehicle
/// </summary>
public sealed class TelemetryDataReceivedEvent : BaseDomainEvent
{
    /// <summary>
    /// Gets the vehicle ID
    /// </summary>
    public Guid VehicleId { get; }

    /// <summary>
    /// Gets the location data
    /// </summary>
    public Location Location { get; }

    /// <summary>
    /// Gets the speed data
    /// </summary>
    public Speed Speed { get; }

    /// <summary>
    /// Gets the fuel level data
    /// </summary>
    public FuelLevel? FuelLevel { get; }

    /// <summary>
    /// Gets the mileage data
    /// </summary>
    public decimal? Mileage { get; }

    /// <summary>
    /// Gets the timestamp when the telemetry data was recorded
    /// </summary>
    public DateTime Timestamp { get; }

    /// <summary>
    /// Initializes a new instance of the <see cref="TelemetryDataReceivedEvent"/> class
    /// </summary>
    /// <param name="vehicleId">The vehicle ID</param>
    /// <param name="location">The location data</param>
    /// <param name="speed">The speed data</param>
    /// <param name="fuelLevel">The fuel level data</param>
    /// <param name="mileage">The mileage data</param>
    /// <param name="timestamp">The timestamp when the data was recorded</param>
    public TelemetryDataReceivedEvent(Guid vehicleId, Location location, Speed speed, 
        FuelLevel? fuelLevel = null, decimal? mileage = null, DateTime? timestamp = null)
    {
        VehicleId = vehicleId;
        Location = location ?? throw new ArgumentNullException(nameof(location));
        Speed = speed ?? throw new ArgumentNullException(nameof(speed));
        FuelLevel = fuelLevel;
        Mileage = mileage;
        Timestamp = timestamp ?? DateTime.UtcNow;
    }
}
