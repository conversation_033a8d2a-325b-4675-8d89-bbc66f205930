namespace FleetXQ.Application.Features.Vehicles.DTOs;

/// <summary>
/// Data transfer object for location information
/// </summary>
public class LocationDto
{
    /// <summary>
    /// Gets or sets the latitude coordinate
    /// </summary>
    public decimal Latitude { get; set; }

    /// <summary>
    /// Gets or sets the longitude coordinate
    /// </summary>
    public decimal Longitude { get; set; }

    /// <summary>
    /// Gets or sets the address (if available)
    /// </summary>
    public string? Address { get; set; }
}
