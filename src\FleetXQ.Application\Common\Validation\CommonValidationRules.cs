using FluentValidation;

namespace FleetXQ.Application.Common.Validation;

/// <summary>
/// Common validation rules and extensions for FluentValidation
/// </summary>
public static class CommonValidationRules
{
    /// <summary>
    /// Validates that a GUID is not empty
    /// </summary>
    /// <typeparam name="T">The type being validated</typeparam>
    /// <param name="ruleBuilder">The rule builder</param>
    /// <returns>The rule builder options</returns>
    public static IRuleBuilderOptions<T, Guid> NotEmptyGuid<T>(this IRuleBuilder<T, Guid> ruleBuilder)
    {
        return ruleBuilder
            .NotEqual(Guid.Empty)
            .WithMessage("'{PropertyName}' must not be empty");
    }

    /// <summary>
    /// Validates that a nullable GUID is not empty when it has a value
    /// </summary>
    /// <typeparam name="T">The type being validated</typeparam>
    /// <param name="ruleBuilder">The rule builder</param>
    /// <returns>The rule builder options</returns>
    public static IRuleBuilderOptions<T, Guid?> NotEmptyGuid<T>(this IRuleBuilder<T, Guid?> ruleBuilder)
    {
        return ruleBuilder
            .Must(guid => !guid.HasValue || guid.Value != Guid.Empty)
            .WithMessage("'{PropertyName}' must not be empty when provided");
    }

    /// <summary>
    /// Validates an email address format
    /// </summary>
    /// <typeparam name="T">The type being validated</typeparam>
    /// <param name="ruleBuilder">The rule builder</param>
    /// <returns>The rule builder options</returns>
    public static IRuleBuilderOptions<T, string> ValidEmail<T>(this IRuleBuilder<T, string> ruleBuilder)
    {
        return ruleBuilder
            .EmailAddress()
            .WithMessage("'{PropertyName}' must be a valid email address");
    }

    /// <summary>
    /// Validates a phone number format
    /// </summary>
    /// <typeparam name="T">The type being validated</typeparam>
    /// <param name="ruleBuilder">The rule builder</param>
    /// <returns>The rule builder options</returns>
    public static IRuleBuilderOptions<T, string> ValidPhoneNumber<T>(this IRuleBuilder<T, string> ruleBuilder)
    {
        return ruleBuilder
            .Matches(@"^[\+]?[1-9][\d]{0,15}$")
            .WithMessage("'{PropertyName}' must be a valid phone number");
    }

    /// <summary>
    /// Validates a license plate format
    /// </summary>
    /// <typeparam name="T">The type being validated</typeparam>
    /// <param name="ruleBuilder">The rule builder</param>
    /// <returns>The rule builder options</returns>
    public static IRuleBuilderOptions<T, string> ValidLicensePlate<T>(this IRuleBuilder<T, string> ruleBuilder)
    {
        return ruleBuilder
            .Matches(@"^[A-Z0-9\-]+$")
            .WithMessage("'{PropertyName}' can only contain uppercase letters, numbers, and hyphens")
            .Length(2, 20)
            .WithMessage("'{PropertyName}' must be between 2 and 20 characters");
    }

    /// <summary>
    /// Validates a VIN (Vehicle Identification Number) format
    /// </summary>
    /// <typeparam name="T">The type being validated</typeparam>
    /// <param name="ruleBuilder">The rule builder</param>
    /// <returns>The rule builder options</returns>
    public static IRuleBuilderOptions<T, string> ValidVIN<T>(this IRuleBuilder<T, string> ruleBuilder)
    {
        return ruleBuilder
            .Length(17)
            .WithMessage("'{PropertyName}' must be exactly 17 characters")
            .Matches(@"^[A-HJ-NPR-Z0-9]+$")
            .WithMessage("'{PropertyName}' contains invalid characters (I, O, Q not allowed)");
    }

    /// <summary>
    /// Validates a latitude coordinate
    /// </summary>
    /// <typeparam name="T">The type being validated</typeparam>
    /// <param name="ruleBuilder">The rule builder</param>
    /// <returns>The rule builder options</returns>
    public static IRuleBuilderOptions<T, decimal> ValidLatitude<T>(this IRuleBuilder<T, decimal> ruleBuilder)
    {
        return ruleBuilder
            .InclusiveBetween(-90, 90)
            .WithMessage("'{PropertyName}' must be between -90 and 90 degrees");
    }

    /// <summary>
    /// Validates a longitude coordinate
    /// </summary>
    /// <typeparam name="T">The type being validated</typeparam>
    /// <param name="ruleBuilder">The rule builder</param>
    /// <returns>The rule builder options</returns>
    public static IRuleBuilderOptions<T, decimal> ValidLongitude<T>(this IRuleBuilder<T, decimal> ruleBuilder)
    {
        return ruleBuilder
            .InclusiveBetween(-180, 180)
            .WithMessage("'{PropertyName}' must be between -180 and 180 degrees");
    }

    /// <summary>
    /// Validates a speed value
    /// </summary>
    /// <typeparam name="T">The type being validated</typeparam>
    /// <param name="ruleBuilder">The rule builder</param>
    /// <param name="maxSpeed">The maximum allowed speed (default: 500 km/h)</param>
    /// <returns>The rule builder options</returns>
    public static IRuleBuilderOptions<T, decimal> ValidSpeed<T>(this IRuleBuilder<T, decimal> ruleBuilder, decimal maxSpeed = 500)
    {
        return ruleBuilder
            .GreaterThanOrEqualTo(0)
            .WithMessage("'{PropertyName}' cannot be negative")
            .LessThanOrEqualTo(maxSpeed)
            .WithMessage($"'{{PropertyName}}' cannot exceed {maxSpeed} km/h");
    }

    /// <summary>
    /// Validates a fuel level percentage
    /// </summary>
    /// <typeparam name="T">The type being validated</typeparam>
    /// <param name="ruleBuilder">The rule builder</param>
    /// <returns>The rule builder options</returns>
    public static IRuleBuilderOptions<T, decimal> ValidFuelLevelPercentage<T>(this IRuleBuilder<T, decimal> ruleBuilder)
    {
        return ruleBuilder
            .InclusiveBetween(0, 100)
            .WithMessage("'{PropertyName}' must be between 0 and 100 percent");
    }

    /// <summary>
    /// Validates a mileage value
    /// </summary>
    /// <typeparam name="T">The type being validated</typeparam>
    /// <param name="ruleBuilder">The rule builder</param>
    /// <param name="maxMileage">The maximum allowed mileage (default: 10,000,000)</param>
    /// <returns>The rule builder options</returns>
    public static IRuleBuilderOptions<T, decimal> ValidMileage<T>(this IRuleBuilder<T, decimal> ruleBuilder, decimal maxMileage = 10_000_000)
    {
        return ruleBuilder
            .GreaterThanOrEqualTo(0)
            .WithMessage("'{PropertyName}' cannot be negative")
            .LessThanOrEqualTo(maxMileage)
            .WithMessage($"'{{PropertyName}}' cannot exceed {maxMileage:N0}");
    }

    /// <summary>
    /// Validates a date is not in the future
    /// </summary>
    /// <typeparam name="T">The type being validated</typeparam>
    /// <param name="ruleBuilder">The rule builder</param>
    /// <returns>The rule builder options</returns>
    public static IRuleBuilderOptions<T, DateTime> NotInFuture<T>(this IRuleBuilder<T, DateTime> ruleBuilder)
    {
        return ruleBuilder
            .LessThanOrEqualTo(DateTime.UtcNow)
            .WithMessage("'{PropertyName}' cannot be in the future");
    }

    /// <summary>
    /// Validates a date is not too far in the past
    /// </summary>
    /// <typeparam name="T">The type being validated</typeparam>
    /// <param name="ruleBuilder">The rule builder</param>
    /// <param name="maxDaysInPast">Maximum days in the past (default: 30)</param>
    /// <returns>The rule builder options</returns>
    public static IRuleBuilderOptions<T, DateTime> NotTooFarInPast<T>(this IRuleBuilder<T, DateTime> ruleBuilder, int maxDaysInPast = 30)
    {
        return ruleBuilder
            .GreaterThan(DateTime.UtcNow.AddDays(-maxDaysInPast))
            .WithMessage($"'{{PropertyName}}' cannot be more than {maxDaysInPast} days in the past");
    }

    /// <summary>
    /// Validates that a string contains only alphanumeric characters and common symbols
    /// </summary>
    /// <typeparam name="T">The type being validated</typeparam>
    /// <param name="ruleBuilder">The rule builder</param>
    /// <returns>The rule builder options</returns>
    public static IRuleBuilderOptions<T, string> AlphanumericWithSymbols<T>(this IRuleBuilder<T, string> ruleBuilder)
    {
        return ruleBuilder
            .Matches(@"^[a-zA-Z0-9\s\-_\.]+$")
            .WithMessage("'{PropertyName}' can only contain letters, numbers, spaces, hyphens, underscores, and periods");
    }

    /// <summary>
    /// Validates that a string is a valid JSON format
    /// </summary>
    /// <typeparam name="T">The type being validated</typeparam>
    /// <param name="ruleBuilder">The rule builder</param>
    /// <returns>The rule builder options</returns>
    public static IRuleBuilderOptions<T, string> ValidJson<T>(this IRuleBuilder<T, string> ruleBuilder)
    {
        return ruleBuilder
            .Must(BeValidJson)
            .WithMessage("'{PropertyName}' must be valid JSON");
    }

    /// <summary>
    /// Validates if a string is valid JSON
    /// </summary>
    /// <param name="json">The JSON string to validate</param>
    /// <returns>True if valid JSON, false otherwise</returns>
    private static bool BeValidJson(string? json)
    {
        if (string.IsNullOrWhiteSpace(json))
            return true;

        try
        {
            System.Text.Json.JsonDocument.Parse(json);
            return true;
        }
        catch
        {
            return false;
        }
    }
}
