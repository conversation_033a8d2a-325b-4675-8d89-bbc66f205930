using FleetXQ.Domain.Entities;
using FleetXQ.Domain.Enums;
using FleetXQ.Domain.ValueObjects;
using FleetXQ.Domain.Events;
using Xunit;

namespace FleetXQ.Domain.Tests.Entities;

public class AlertTests
{
    [Fact]
    public void Constructor_WithValidParameters_ShouldCreateAlert()
    {
        // Arrange
        var alertType = AlertType.Speed;
        var severity = AlertSeverity.High;
        var message = "Speed limit exceeded";
        var vehicleId = Guid.NewGuid();
        var driverId = Guid.NewGuid();
        var location = new Location(40.7128m, -74.0060m);

        // Act
        var alert = new Alert(alertType, severity, message, vehicleId, driverId, location);

        // Assert
        Assert.Equal(alertType, alert.AlertType);
        Assert.Equal(severity, alert.Severity);
        Assert.Equal(message, alert.Message);
        Assert.Equal(vehicleId, alert.VehicleId);
        Assert.Equal(driverId, alert.DriverId);
        Assert.Equal(location, alert.Location);
        Assert.Equal(AlertStatus.Active, alert.Status);
        Assert.True(alert.IsActive);
        Assert.False(alert.IsAcknowledged);
        Assert.False(alert.IsResolved);
        Assert.Single(alert.DomainEvents);
        Assert.IsType<AlertTriggeredEvent>(alert.DomainEvents.First());
    }

    [Fact]
    public void Constructor_WithEmptyMessage_ShouldThrowArgumentException()
    {
        // Act & Assert
        Assert.Throws<ArgumentException>(() => new Alert(AlertType.Speed, AlertSeverity.High, ""));
    }

    [Fact]
    public void CreateSpeedAlert_WithValidParameters_ShouldCreateSpeedAlert()
    {
        // Arrange
        var vehicleId = Guid.NewGuid();
        var driverId = Guid.NewGuid();
        var currentSpeed = new Speed(100);
        var speedLimit = new Speed(80);
        var location = new Location(40.7128m, -74.0060m);

        // Act
        var alert = Alert.CreateSpeedAlert(vehicleId, driverId, currentSpeed, speedLimit, location);

        // Assert
        Assert.Equal(AlertType.Speed, alert.AlertType);
        Assert.Equal(vehicleId, alert.VehicleId);
        Assert.Equal(driverId, alert.DriverId);
        Assert.Equal(location, alert.Location);
        Assert.Contains("Speed violation detected", alert.Message);
        Assert.Contains("25.0% over limit", alert.Message);
    }

    [Fact]
    public void CreateFuelAlert_WithCriticalFuelLevel_ShouldCreateCriticalAlert()
    {
        // Arrange
        var vehicleId = Guid.NewGuid();
        var fuelLevel = new FuelLevel(5); // Critical level
        var location = new Location(40.7128m, -74.0060m);

        // Act
        var alert = Alert.CreateFuelAlert(vehicleId, fuelLevel, location);

        // Assert
        Assert.Equal(AlertType.Fuel, alert.AlertType);
        Assert.Equal(AlertSeverity.Critical, alert.Severity);
        Assert.Equal(vehicleId, alert.VehicleId);
        Assert.Null(alert.DriverId);
        Assert.Equal(location, alert.Location);
        Assert.Contains("Fuel level alert", alert.Message);
    }

    [Fact]
    public void CreateMaintenanceAlert_WithOverdueMaintenance_ShouldCreateCriticalAlert()
    {
        // Arrange
        var vehicleId = Guid.NewGuid();
        var maintenanceType = "Oil Change";
        var dueDate = DateTime.UtcNow.AddDays(-5); // Overdue

        // Act
        var alert = Alert.CreateMaintenanceAlert(vehicleId, maintenanceType, dueDate);

        // Assert
        Assert.Equal(AlertType.Maintenance, alert.AlertType);
        Assert.Equal(AlertSeverity.Critical, alert.Severity);
        Assert.Equal(vehicleId, alert.VehicleId);
        Assert.Contains("Maintenance overdue", alert.Message);
        Assert.Contains("Oil Change", alert.Message);
    }

    [Fact]
    public void CreateHarshDrivingAlert_WithValidParameters_ShouldCreateAlert()
    {
        // Arrange
        var vehicleId = Guid.NewGuid();
        var driverId = Guid.NewGuid();
        var eventType = "Hard Braking";
        var location = new Location(40.7128m, -74.0060m);

        // Act
        var alert = Alert.CreateHarshDrivingAlert(vehicleId, driverId, eventType, location);

        // Assert
        Assert.Equal(AlertType.HarshDriving, alert.AlertType);
        Assert.Equal(AlertSeverity.Medium, alert.Severity);
        Assert.Equal(vehicleId, alert.VehicleId);
        Assert.Equal(driverId, alert.DriverId);
        Assert.Equal(location, alert.Location);
        Assert.Contains("Harsh driving detected", alert.Message);
        Assert.Contains("Hard Braking", alert.Message);
    }

    [Fact]
    public void Acknowledge_WithValidUser_ShouldAcknowledgeAlert()
    {
        // Arrange
        var alert = new Alert(AlertType.Speed, AlertSeverity.High, "Test alert");
        var userId = Guid.NewGuid();
        var notes = "Acknowledged by supervisor";

        // Act
        alert.Acknowledge(userId, notes);

        // Assert
        Assert.Equal(AlertStatus.Acknowledged, alert.Status);
        Assert.True(alert.IsAcknowledged);
        Assert.False(alert.IsResolved);
        Assert.Equal(userId, alert.AcknowledgedBy);
        Assert.Equal(notes, alert.AcknowledgmentNotes);
        Assert.NotNull(alert.AcknowledgedDate);
    }

    [Fact]
    public void Acknowledge_WithNonActiveAlert_ShouldThrowInvalidOperationException()
    {
        // Arrange
        var alert = new Alert(AlertType.Speed, AlertSeverity.High, "Test alert");
        var userId = Guid.NewGuid();
        alert.Acknowledge(userId);

        // Act & Assert
        Assert.Throws<InvalidOperationException>(() => alert.Acknowledge(userId));
    }

    [Fact]
    public void Resolve_WithValidUser_ShouldResolveAlert()
    {
        // Arrange
        var alert = new Alert(AlertType.Speed, AlertSeverity.High, "Test alert");
        var userId = Guid.NewGuid();
        var notes = "Issue resolved";

        // Act
        alert.Resolve(userId, notes);

        // Assert
        Assert.Equal(AlertStatus.Resolved, alert.Status);
        Assert.True(alert.IsResolved);
        Assert.True(alert.IsAcknowledged); // Should be auto-acknowledged
        Assert.Equal(userId, alert.ResolvedBy);
        Assert.Equal(notes, alert.ResolutionNotes);
        Assert.NotNull(alert.ResolvedDate);
        Assert.NotNull(alert.AcknowledgedDate);
        Assert.Equal(userId, alert.AcknowledgedBy);
    }

    [Fact]
    public void Resolve_WithAlreadyResolvedAlert_ShouldThrowInvalidOperationException()
    {
        // Arrange
        var alert = new Alert(AlertType.Speed, AlertSeverity.High, "Test alert");
        var userId = Guid.NewGuid();
        alert.Resolve(userId);

        // Act & Assert
        Assert.Throws<InvalidOperationException>(() => alert.Resolve(userId));
    }

    [Fact]
    public void UpdateMessage_WithValidMessage_ShouldUpdateMessage()
    {
        // Arrange
        var alert = new Alert(AlertType.Speed, AlertSeverity.High, "Old message");
        var newMessage = "Updated message";

        // Act
        alert.UpdateMessage(newMessage);

        // Assert
        Assert.Equal(newMessage, alert.Message);
    }

    [Fact]
    public void UpdateMessage_WithEmptyMessage_ShouldThrowArgumentException()
    {
        // Arrange
        var alert = new Alert(AlertType.Speed, AlertSeverity.High, "Test alert");

        // Act & Assert
        Assert.Throws<ArgumentException>(() => alert.UpdateMessage(""));
    }

    [Fact]
    public void UpdateSeverity_WithValidSeverity_ShouldUpdateSeverityAndExpiry()
    {
        // Arrange
        var alert = new Alert(AlertType.Speed, AlertSeverity.Low, "Test alert");
        var originalExpiry = alert.ExpiryDate;

        // Act
        alert.UpdateSeverity(AlertSeverity.Critical);

        // Assert
        Assert.Equal(AlertSeverity.Critical, alert.Severity);
        Assert.NotEqual(originalExpiry, alert.ExpiryDate);
    }

    [Fact]
    public void UpdateSeverity_WithResolvedAlert_ShouldThrowInvalidOperationException()
    {
        // Arrange
        var alert = new Alert(AlertType.Speed, AlertSeverity.High, "Test alert");
        alert.Resolve(Guid.NewGuid());

        // Act & Assert
        Assert.Throws<InvalidOperationException>(() => alert.UpdateSeverity(AlertSeverity.Critical));
    }

    [Fact]
    public void RequiresImmediateAttention_WithCriticalSeverityAndActiveStatus_ShouldReturnTrue()
    {
        // Arrange
        var alert = new Alert(AlertType.Speed, AlertSeverity.Critical, "Critical alert");

        // Act & Assert
        Assert.True(alert.RequiresImmediateAttention);
    }

    [Fact]
    public void RequiresImmediateAttention_WithHighSeverity_ShouldReturnFalse()
    {
        // Arrange
        var alert = new Alert(AlertType.Speed, AlertSeverity.High, "High alert");

        // Act & Assert
        Assert.False(alert.RequiresImmediateAttention);
    }

    [Fact]
    public void AgeInHours_ShouldCalculateCorrectAge()
    {
        // Arrange
        var alert = new Alert(AlertType.Speed, AlertSeverity.High, "Test alert");
        
        // Act
        var age = alert.AgeInHours;

        // Assert
        Assert.True(age >= 0 && age < 1); // Should be very recent
    }

    [Fact]
    public void AcknowledgmentTimeMinutes_WithAcknowledgedAlert_ShouldReturnTime()
    {
        // Arrange
        var alert = new Alert(AlertType.Speed, AlertSeverity.High, "Test alert");
        var userId = Guid.NewGuid();

        // Act
        alert.Acknowledge(userId);
        var acknowledgmentTime = alert.AcknowledgmentTimeMinutes;

        // Assert
        Assert.NotNull(acknowledgmentTime);
        Assert.True(acknowledgmentTime >= 0);
    }

    [Fact]
    public void ResolutionTimeMinutes_WithResolvedAlert_ShouldReturnTime()
    {
        // Arrange
        var alert = new Alert(AlertType.Speed, AlertSeverity.High, "Test alert");
        var userId = Guid.NewGuid();

        // Act
        alert.Resolve(userId);
        var resolutionTime = alert.ResolutionTimeMinutes;

        // Assert
        Assert.NotNull(resolutionTime);
        Assert.True(resolutionTime >= 0);
    }

    [Fact]
    public void ExtendExpiry_WithValidHours_ShouldExtendExpiryDate()
    {
        // Arrange
        var alert = new Alert(AlertType.Speed, AlertSeverity.High, "Test alert");
        var originalExpiry = alert.ExpiryDate;

        // Act
        alert.ExtendExpiry(2);

        // Assert
        Assert.NotNull(alert.ExpiryDate);
        Assert.True(alert.ExpiryDate > originalExpiry);
    }

    [Fact]
    public void ExtendExpiry_WithResolvedAlert_ShouldThrowInvalidOperationException()
    {
        // Arrange
        var alert = new Alert(AlertType.Speed, AlertSeverity.High, "Test alert");
        alert.Resolve(Guid.NewGuid());

        // Act & Assert
        Assert.Throws<InvalidOperationException>(() => alert.ExtendExpiry(2));
    }

    [Fact]
    public void SetMetadata_WithValidMetadata_ShouldSetMetadata()
    {
        // Arrange
        var alert = new Alert(AlertType.Speed, AlertSeverity.High, "Test alert");
        var metadata = "{\"key\": \"value\"}";

        // Act
        alert.SetMetadata(metadata);

        // Assert
        Assert.Equal(metadata, alert.Metadata);
    }

    [Fact]
    public void ShouldAutoResolve_WithExpiredActiveAlert_ShouldReturnTrue()
    {
        // Arrange
        var alert = new Alert(AlertType.Speed, AlertSeverity.High, "Test alert");
        // Manually set expiry to past (this would normally be done by the system)
        var pastExpiry = DateTime.UtcNow.AddHours(-1);
        
        // Use reflection to set the private field for testing
        var expiryField = typeof(Alert).GetField("<ExpiryDate>k__BackingField", 
            System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
        expiryField?.SetValue(alert, pastExpiry);

        // Act & Assert
        Assert.True(alert.ShouldAutoResolve());
    }
}
