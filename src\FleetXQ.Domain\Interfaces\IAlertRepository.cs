using FleetXQ.Domain.Entities;
using FleetXQ.Domain.Enums;
using FleetXQ.Domain.ValueObjects;

namespace FleetXQ.Domain.Interfaces;

/// <summary>
/// Repository interface for Alert aggregate root
/// </summary>
public interface IAlertRepository : IRepository<Alert>
{
    /// <summary>
    /// Gets alerts by vehicle ID
    /// </summary>
    /// <param name="vehicleId">The vehicle ID</param>
    /// <param name="cancellationToken">The cancellation token</param>
    /// <returns>A collection of alerts for the specified vehicle</returns>
    Task<IEnumerable<Alert>> GetByVehicleIdAsync(Guid vehicleId, CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets alerts by driver ID
    /// </summary>
    /// <param name="driverId">The driver ID</param>
    /// <param name="cancellationToken">The cancellation token</param>
    /// <returns>A collection of alerts for the specified driver</returns>
    Task<IEnumerable<Alert>> GetByDriverIdAsync(Guid driverId, CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets alerts by type
    /// </summary>
    /// <param name="alertType">The alert type</param>
    /// <param name="cancellationToken">The cancellation token</param>
    /// <returns>A collection of alerts of the specified type</returns>
    Task<IEnumerable<Alert>> GetByTypeAsync(AlertType alertType, CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets alerts by severity
    /// </summary>
    /// <param name="severity">The alert severity</param>
    /// <param name="cancellationToken">The cancellation token</param>
    /// <returns>A collection of alerts with the specified severity</returns>
    Task<IEnumerable<Alert>> GetBySeverityAsync(AlertSeverity severity, CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets alerts by status
    /// </summary>
    /// <param name="status">The alert status</param>
    /// <param name="cancellationToken">The cancellation token</param>
    /// <returns>A collection of alerts with the specified status</returns>
    Task<IEnumerable<Alert>> GetByStatusAsync(AlertStatus status, CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets active alerts
    /// </summary>
    /// <param name="cancellationToken">The cancellation token</param>
    /// <returns>A collection of active alerts</returns>
    Task<IEnumerable<Alert>> GetActiveAlertsAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets critical alerts
    /// </summary>
    /// <param name="cancellationToken">The cancellation token</param>
    /// <returns>A collection of critical alerts</returns>
    Task<IEnumerable<Alert>> GetCriticalAlertsAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets unacknowledged alerts
    /// </summary>
    /// <param name="cancellationToken">The cancellation token</param>
    /// <returns>A collection of unacknowledged alerts</returns>
    Task<IEnumerable<Alert>> GetUnacknowledgedAlertsAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets expired alerts
    /// </summary>
    /// <param name="cancellationToken">The cancellation token</param>
    /// <returns>A collection of expired alerts</returns>
    Task<IEnumerable<Alert>> GetExpiredAlertsAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets alerts created within a date range
    /// </summary>
    /// <param name="fromDate">The start date</param>
    /// <param name="toDate">The end date</param>
    /// <param name="cancellationToken">The cancellation token</param>
    /// <returns>A collection of alerts created within the date range</returns>
    Task<IEnumerable<Alert>> GetAlertsCreatedBetweenAsync(DateTime fromDate, DateTime toDate, 
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets alerts within a location radius
    /// </summary>
    /// <param name="centerLocation">The center location</param>
    /// <param name="radiusKm">The radius in kilometers</param>
    /// <param name="cancellationToken">The cancellation token</param>
    /// <returns>A collection of alerts within the specified radius</returns>
    Task<IEnumerable<Alert>> GetAlertsInRadiusAsync(Location centerLocation, double radiusKm, 
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets alerts acknowledged by a specific user
    /// </summary>
    /// <param name="userId">The user ID</param>
    /// <param name="cancellationToken">The cancellation token</param>
    /// <returns>A collection of alerts acknowledged by the user</returns>
    Task<IEnumerable<Alert>> GetAlertsAcknowledgedByUserAsync(Guid userId, CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets alerts resolved by a specific user
    /// </summary>
    /// <param name="userId">The user ID</param>
    /// <param name="cancellationToken">The cancellation token</param>
    /// <returns>A collection of alerts resolved by the user</returns>
    Task<IEnumerable<Alert>> GetAlertsResolvedByUserAsync(Guid userId, CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets alerts that require immediate attention
    /// </summary>
    /// <param name="cancellationToken">The cancellation token</param>
    /// <returns>A collection of alerts requiring immediate attention</returns>
    Task<IEnumerable<Alert>> GetAlertsRequiringImmediateAttentionAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets alerts older than specified hours
    /// </summary>
    /// <param name="hoursThreshold">The hours threshold</param>
    /// <param name="cancellationToken">The cancellation token</param>
    /// <returns>A collection of old alerts</returns>
    Task<IEnumerable<Alert>> GetOldAlertsAsync(int hoursThreshold = 24, CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets alert statistics by type
    /// </summary>
    /// <param name="cancellationToken">The cancellation token</param>
    /// <returns>A dictionary with alert type counts</returns>
    Task<Dictionary<AlertType, int>> GetAlertStatisticsByTypeAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets alert statistics by severity
    /// </summary>
    /// <param name="cancellationToken">The cancellation token</param>
    /// <returns>A dictionary with alert severity counts</returns>
    Task<Dictionary<AlertSeverity, int>> GetAlertStatisticsBySeverityAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets alert statistics by status
    /// </summary>
    /// <param name="cancellationToken">The cancellation token</param>
    /// <returns>A dictionary with alert status counts</returns>
    Task<Dictionary<AlertStatus, int>> GetAlertStatisticsByStatusAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets alert response time statistics
    /// </summary>
    /// <param name="cancellationToken">The cancellation token</param>
    /// <returns>Alert response time statistics</returns>
    Task<AlertResponseTimeStatistics> GetAlertResponseTimeStatisticsAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// Searches alerts by multiple criteria
    /// </summary>
    /// <param name="searchCriteria">The search criteria</param>
    /// <param name="cancellationToken">The cancellation token</param>
    /// <returns>A collection of alerts matching the criteria</returns>
    Task<IEnumerable<Alert>> SearchAlertsAsync(AlertSearchCriteria searchCriteria, 
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets alerts for dashboard display
    /// </summary>
    /// <param name="limit">The maximum number of alerts to return</param>
    /// <param name="cancellationToken">The cancellation token</param>
    /// <returns>A collection of alerts for dashboard display</returns>
    Task<IEnumerable<Alert>> GetDashboardAlertsAsync(int limit = 10, CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets alert trends for a specific period
    /// </summary>
    /// <param name="fromDate">The start date</param>
    /// <param name="toDate">The end date</param>
    /// <param name="groupBy">The grouping period (day, week, month)</param>
    /// <param name="cancellationToken">The cancellation token</param>
    /// <returns>Alert trend data</returns>
    Task<IEnumerable<AlertTrendData>> GetAlertTrendsAsync(DateTime fromDate, DateTime toDate, 
        string groupBy = "day", CancellationToken cancellationToken = default);

    /// <summary>
    /// Bulk acknowledges alerts
    /// </summary>
    /// <param name="alertIds">The alert IDs to acknowledge</param>
    /// <param name="acknowledgedBy">The user acknowledging the alerts</param>
    /// <param name="notes">Optional acknowledgment notes</param>
    /// <param name="cancellationToken">The cancellation token</param>
    /// <returns>The number of alerts acknowledged</returns>
    Task<int> BulkAcknowledgeAlertsAsync(IEnumerable<Guid> alertIds, Guid acknowledgedBy, 
        string? notes = null, CancellationToken cancellationToken = default);

    /// <summary>
    /// Bulk resolves alerts
    /// </summary>
    /// <param name="alertIds">The alert IDs to resolve</param>
    /// <param name="resolvedBy">The user resolving the alerts</param>
    /// <param name="notes">Optional resolution notes</param>
    /// <param name="cancellationToken">The cancellation token</param>
    /// <returns>The number of alerts resolved</returns>
    Task<int> BulkResolveAlertsAsync(IEnumerable<Guid> alertIds, Guid resolvedBy, 
        string? notes = null, CancellationToken cancellationToken = default);
}

/// <summary>
/// Represents search criteria for alerts
/// </summary>
public class AlertSearchCriteria
{
    /// <summary>
    /// Gets or sets the search term for message content
    /// </summary>
    public string? SearchTerm { get; set; }

    /// <summary>
    /// Gets or sets the alert type filter
    /// </summary>
    public AlertType? AlertType { get; set; }

    /// <summary>
    /// Gets or sets the alert severity filter
    /// </summary>
    public AlertSeverity? Severity { get; set; }

    /// <summary>
    /// Gets or sets the alert status filter
    /// </summary>
    public AlertStatus? Status { get; set; }

    /// <summary>
    /// Gets or sets the vehicle ID filter
    /// </summary>
    public Guid? VehicleId { get; set; }

    /// <summary>
    /// Gets or sets the driver ID filter
    /// </summary>
    public Guid? DriverId { get; set; }

    /// <summary>
    /// Gets or sets the created date from filter
    /// </summary>
    public DateTime? CreatedFrom { get; set; }

    /// <summary>
    /// Gets or sets the created date to filter
    /// </summary>
    public DateTime? CreatedTo { get; set; }

    /// <summary>
    /// Gets or sets the acknowledged by user filter
    /// </summary>
    public Guid? AcknowledgedBy { get; set; }

    /// <summary>
    /// Gets or sets the resolved by user filter
    /// </summary>
    public Guid? ResolvedBy { get; set; }

    /// <summary>
    /// Gets or sets a value indicating whether to include only active alerts
    /// </summary>
    public bool? ActiveOnly { get; set; }

    /// <summary>
    /// Gets or sets a value indicating whether to include only critical alerts
    /// </summary>
    public bool? CriticalOnly { get; set; }

    /// <summary>
    /// Gets or sets a value indicating whether to include only unacknowledged alerts
    /// </summary>
    public bool? UnacknowledgedOnly { get; set; }

    /// <summary>
    /// Gets or sets the location filter
    /// </summary>
    public Location? Location { get; set; }

    /// <summary>
    /// Gets or sets the radius for location filter in kilometers
    /// </summary>
    public double? RadiusKm { get; set; }
}

/// <summary>
/// Represents alert response time statistics
/// </summary>
public class AlertResponseTimeStatistics
{
    /// <summary>
    /// Gets or sets the average acknowledgment time in minutes
    /// </summary>
    public double AverageAcknowledgmentTimeMinutes { get; set; }

    /// <summary>
    /// Gets or sets the average resolution time in minutes
    /// </summary>
    public double AverageResolutionTimeMinutes { get; set; }

    /// <summary>
    /// Gets or sets the median acknowledgment time in minutes
    /// </summary>
    public double MedianAcknowledgmentTimeMinutes { get; set; }

    /// <summary>
    /// Gets or sets the median resolution time in minutes
    /// </summary>
    public double MedianResolutionTimeMinutes { get; set; }

    /// <summary>
    /// Gets or sets the fastest acknowledgment time in minutes
    /// </summary>
    public double FastestAcknowledgmentTimeMinutes { get; set; }

    /// <summary>
    /// Gets or sets the fastest resolution time in minutes
    /// </summary>
    public double FastestResolutionTimeMinutes { get; set; }

    /// <summary>
    /// Gets or sets the slowest acknowledgment time in minutes
    /// </summary>
    public double SlowestAcknowledgmentTimeMinutes { get; set; }

    /// <summary>
    /// Gets or sets the slowest resolution time in minutes
    /// </summary>
    public double SlowestResolutionTimeMinutes { get; set; }
}

/// <summary>
/// Represents alert trend data for a specific time period
/// </summary>
public class AlertTrendData
{
    /// <summary>
    /// Gets or sets the period (date or date range)
    /// </summary>
    public string Period { get; set; } = string.Empty;

    /// <summary>
    /// Gets or sets the total number of alerts
    /// </summary>
    public int TotalAlerts { get; set; }

    /// <summary>
    /// Gets or sets the number of critical alerts
    /// </summary>
    public int CriticalAlerts { get; set; }

    /// <summary>
    /// Gets or sets the number of high severity alerts
    /// </summary>
    public int HighSeverityAlerts { get; set; }

    /// <summary>
    /// Gets or sets the number of resolved alerts
    /// </summary>
    public int ResolvedAlerts { get; set; }

    /// <summary>
    /// Gets or sets the average resolution time in minutes
    /// </summary>
    public double AverageResolutionTimeMinutes { get; set; }
}
