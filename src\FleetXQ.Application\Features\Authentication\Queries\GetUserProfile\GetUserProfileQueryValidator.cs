using FluentValidation;

namespace FleetXQ.Application.Features.Authentication.Queries.GetUserProfile;

/// <summary>
/// Validator for GetUserProfileQuery
/// </summary>
public sealed class GetUserProfileQueryValidator : AbstractValidator<GetUserProfileQuery>
{
    /// <summary>
    /// Initializes a new instance of the <see cref="GetUserProfileQueryValidator"/> class
    /// </summary>
    public GetUserProfileQueryValidator()
    {
        RuleFor(x => x.UserId)
            .NotEmpty()
            .WithMessage("User ID is required");

        RuleFor(x => x.RequestingUserId)
            .NotEmpty()
            .WithMessage("Requesting user ID is required");
    }
}
