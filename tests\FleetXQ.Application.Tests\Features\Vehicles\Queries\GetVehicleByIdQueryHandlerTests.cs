using FleetXQ.Application.Features.Vehicles.DTOs;
using FleetXQ.Application.Features.Vehicles.Queries.GetVehicleById;
using FleetXQ.Application.Tests.Common;
using FleetXQ.Domain.Entities;
using FleetXQ.Domain.Enums;
using FleetXQ.Domain.ValueObjects;
using FluentAssertions;
using Moq;

namespace FleetXQ.Application.Tests.Features.Vehicles.Queries;

public class GetVehicleByIdQueryHandlerTests : QueryHandlerTestBase<GetVehicleByIdQueryHandler>
{
    private readonly GetVehicleByIdQueryHandler _handler;

    public GetVehicleByIdQueryHandlerTests()
    {
        _handler = new GetVehicleByIdQueryHandler(MockVehicleRepository.Object, Mapper, MockLogger.Object);
    }

    [Fact]
    public async Task Handle_WithExistingVehicle_ShouldReturnVehicleDto()
    {
        // Arrange
        var vehicleId = Guid.NewGuid();
        var query = new GetVehicleByIdQuery(vehicleId);

        var vehicle = new Vehicle("Test Vehicle", "ABC-123", "Car", "Gasoline");
        vehicle.UpdateBasicInfo("Test Vehicle", "Toyota", "Camry", 2023, "Blue");
        vehicle.UpdateVIN("1HGBH41JXMN109186");
        vehicle.UpdateFuelTankCapacity(60m);

        // Set up telemetry data
        var location = new Location(40.7128m, -74.0060m);
        var speed = new Speed(65m);
        var fuelLevel = new FuelLevel(75m);
        vehicle.UpdateTelemetry(location, speed, fuelLevel, 50000m);

        MockVehicleRepository.Setup(x => x.GetByIdAsync(vehicleId, It.IsAny<CancellationToken>()))
            .ReturnsAsync(vehicle);

        // Act
        var result = await _handler.Handle(query, CreateCancellationToken());

        // Assert
        result.Should().NotBeNull();
        result.Found.Should().BeTrue();
        result.Vehicle.Should().NotBeNull();
        result.ErrorMessage.Should().BeNull();

        var vehicleDto = result.Vehicle!;
        vehicleDto.Id.Should().Be(vehicle.Id);
        vehicleDto.VehicleName.Should().Be("Test Vehicle");
        vehicleDto.LicensePlate.Should().Be("ABC-123");
        vehicleDto.VehicleType.Should().Be("Car");
        vehicleDto.Brand.Should().Be("Toyota");
        vehicleDto.Model.Should().Be("Camry");
        vehicleDto.Year.Should().Be(2023);
        vehicleDto.Color.Should().Be("Blue");
        vehicleDto.VIN.Should().Be("1HGBH41JXMN109186");
        vehicleDto.FuelType.Should().Be("Gasoline");
        vehicleDto.FuelTankCapacity.Should().Be(60m);
        vehicleDto.Status.Should().Be(VehicleStatus.Active);
        vehicleDto.CurrentMileage.Should().Be(50000m);
        vehicleDto.CurrentSpeedKmh.Should().Be(65m);
        vehicleDto.CurrentFuelLevelPercentage.Should().Be(75m);
        vehicleDto.CurrentLocation.Should().NotBeNull();
        vehicleDto.CurrentLocation!.Latitude.Should().Be(40.7128m);
        vehicleDto.CurrentLocation.Longitude.Should().Be(-74.0060m);
        vehicleDto.IsMoving.Should().BeTrue();
        vehicleDto.IsAvailable.Should().BeTrue();

        VerifyRepositoryGetByIdCalled(MockVehicleRepository, vehicleId);
        VerifyInformationLogged(Times.AtLeastOnce);
    }

    [Fact]
    public async Task Handle_WithNonExistentVehicle_ShouldReturnNotFound()
    {
        // Arrange
        var vehicleId = Guid.NewGuid();
        var query = new GetVehicleByIdQuery(vehicleId);

        MockVehicleRepository.Setup(x => x.GetByIdAsync(vehicleId, It.IsAny<CancellationToken>()))
            .ReturnsAsync((Vehicle?)null);

        // Act
        var result = await _handler.Handle(query, CreateCancellationToken());

        // Assert
        result.Should().NotBeNull();
        result.Found.Should().BeFalse();
        result.Vehicle.Should().BeNull();
        result.ErrorMessage.Should().BeNull();

        VerifyRepositoryGetByIdCalled(MockVehicleRepository, vehicleId);
        VerifyInformationLogged();
    }

    [Fact]
    public async Task Handle_WithRepositoryException_ShouldReturnFailure()
    {
        // Arrange
        var vehicleId = Guid.NewGuid();
        var query = new GetVehicleByIdQuery(vehicleId);

        MockVehicleRepository.Setup(x => x.GetByIdAsync(vehicleId, It.IsAny<CancellationToken>()))
            .ThrowsAsync(new InvalidOperationException("Database error"));

        // Act
        var result = await _handler.Handle(query, CreateCancellationToken());

        // Assert
        result.Should().NotBeNull();
        result.Found.Should().BeFalse();
        result.Vehicle.Should().BeNull();
        result.ErrorMessage.Should().Contain("error occurred");

        VerifyErrorLogged();
    }

    [Fact]
    public async Task Handle_WithMinimalVehicleData_ShouldMapCorrectly()
    {
        // Arrange
        var vehicleId = Guid.NewGuid();
        var query = new GetVehicleByIdQuery(vehicleId);

        var vehicle = new Vehicle("Minimal Vehicle", "MIN-001", "Truck");

        MockVehicleRepository.Setup(x => x.GetByIdAsync(vehicleId, It.IsAny<CancellationToken>()))
            .ReturnsAsync(vehicle);

        // Act
        var result = await _handler.Handle(query, CreateCancellationToken());

        // Assert
        result.Should().NotBeNull();
        result.Found.Should().BeTrue();
        result.Vehicle.Should().NotBeNull();

        var vehicleDto = result.Vehicle!;
        vehicleDto.VehicleName.Should().Be("Minimal Vehicle");
        vehicleDto.LicensePlate.Should().Be("MIN-001");
        vehicleDto.VehicleType.Should().Be("Truck");
        vehicleDto.Brand.Should().BeNull();
        vehicleDto.Model.Should().BeNull();
        vehicleDto.Year.Should().BeNull();
        vehicleDto.Color.Should().BeNull();
        vehicleDto.VIN.Should().BeNull();
        vehicleDto.FuelTankCapacity.Should().BeNull();
        vehicleDto.CurrentFuelLevelPercentage.Should().BeNull();
        vehicleDto.CurrentLocation.Should().BeNull();
        vehicleDto.IsMoving.Should().BeFalse();
    }

    [Fact]
    public async Task Handle_AutoMapperMapping_ShouldWorkCorrectly()
    {
        // Arrange
        var vehicleId = Guid.NewGuid();
        var query = new GetVehicleByIdQuery(vehicleId);

        var vehicle = new Vehicle("Mapping Test", "MAP-001", "SUV", "Diesel");
        vehicle.UpdateBasicInfo("Mapping Test", "Ford", "Explorer", 2022, "Red");

        MockVehicleRepository.Setup(x => x.GetByIdAsync(vehicleId, It.IsAny<CancellationToken>()))
            .ReturnsAsync(vehicle);

        // Act
        var result = await _handler.Handle(query, CreateCancellationToken());

        // Assert
        result.Should().NotBeNull();
        result.Vehicle.Should().NotBeNull();

        // Verify AutoMapper correctly mapped all properties
        var dto = result.Vehicle!;
        dto.Id.Should().Be(vehicle.Id);
        dto.VehicleName.Should().Be(vehicle.VehicleName);
        dto.LicensePlate.Should().Be(vehicle.LicensePlate);
        dto.VehicleType.Should().Be(vehicle.VehicleType);
        dto.Brand.Should().Be(vehicle.Brand);
        dto.Model.Should().Be(vehicle.Model);
        dto.Year.Should().Be(vehicle.Year);
        dto.Color.Should().Be(vehicle.Color);
        dto.FuelType.Should().Be(vehicle.FuelType);
        dto.Status.Should().Be(vehicle.Status);
        dto.CurrentMileage.Should().Be(vehicle.CurrentMileage);
        dto.CreatedAt.Should().Be(vehicle.CreatedAt);
        dto.UpdatedAt.Should().Be(vehicle.UpdatedAt);
    }
}
