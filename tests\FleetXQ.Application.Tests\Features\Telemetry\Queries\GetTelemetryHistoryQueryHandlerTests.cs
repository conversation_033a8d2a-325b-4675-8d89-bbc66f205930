using FleetXQ.Application.Features.Telemetry.Queries.GetTelemetryHistory;
using FleetXQ.Application.Tests.Common;
using FleetXQ.Domain.Entities;
using FluentAssertions;
using Moq;

namespace FleetXQ.Application.Tests.Features.Telemetry.Queries;

public class GetTelemetryHistoryQueryHandlerTests : QueryHandlerTestBase<GetTelemetryHistoryQueryHandler>
{
    private readonly GetTelemetryHistoryQueryHandler _handler;

    public GetTelemetryHistoryQueryHandlerTests()
    {
        _handler = new GetTelemetryHistoryQueryHandler(MockVehicleRepository.Object, Mapper, MockLogger.Object);
    }

    [Fact]
    public async Task Handle_WithExistingVehicle_ShouldReturnTelemetryHistory()
    {
        // Arrange
        var vehicleId = Guid.NewGuid();
        var startDate = DateTime.UtcNow.AddDays(-7);
        var endDate = DateTime.UtcNow;
        var query = new GetTelemetryHistoryQuery(vehicleId, startDate, endDate);

        var vehicle = new Vehicle("Test Vehicle", "ABC-123", "Car");
        MockVehicleRepository.Setup(x => x.GetByIdAsync(vehicleId, It.IsAny<CancellationToken>()))
            .ReturnsAsync(vehicle);

        // Act
        var result = await _handler.Handle(query, CreateCancellationToken());

        // Assert
        result.Should().NotBeNull();
        result.Success.Should().BeTrue();
        result.History.Should().NotBeNull();
        result.History!.VehicleId.Should().Be(vehicleId);
        result.History.StartDate.Should().Be(startDate);
        result.History.EndDate.Should().Be(endDate);
        result.ErrorMessage.Should().BeNull();

        VerifyRepositoryGetByIdCalled(MockVehicleRepository, vehicleId);
        VerifyInformationLogged(Times.AtLeastOnce);
    }

    [Fact]
    public async Task Handle_WithNonExistentVehicle_ShouldReturnNotFound()
    {
        // Arrange
        var vehicleId = Guid.NewGuid();
        var startDate = DateTime.UtcNow.AddDays(-7);
        var endDate = DateTime.UtcNow;
        var query = new GetTelemetryHistoryQuery(vehicleId, startDate, endDate);

        MockVehicleRepository.Setup(x => x.GetByIdAsync(vehicleId, It.IsAny<CancellationToken>()))
            .ReturnsAsync((Vehicle?)null);

        // Act
        var result = await _handler.Handle(query, CreateCancellationToken());

        // Assert
        result.Should().NotBeNull();
        result.Success.Should().BeFalse();
        result.Found.Should().BeFalse();
        result.History.Should().BeNull();
        result.ErrorMessage.Should().Contain("not found");

        VerifyRepositoryGetByIdCalled(MockVehicleRepository, vehicleId);
        VerifyWarningLogged();
    }

    [Fact]
    public async Task Handle_WithValidDateRange_ShouldReturnHistoryWithCorrectDates()
    {
        // Arrange
        var vehicleId = Guid.NewGuid();
        var startDate = DateTime.UtcNow.AddDays(-30);
        var endDate = DateTime.UtcNow.AddDays(-1);
        var query = new GetTelemetryHistoryQuery(vehicleId, startDate, endDate)
        {
            PageNumber = 1,
            PageSize = 100
        };

        var vehicle = new Vehicle("Test Vehicle", "ABC-123", "Car");
        MockVehicleRepository.Setup(x => x.GetByIdAsync(vehicleId, It.IsAny<CancellationToken>()))
            .ReturnsAsync(vehicle);

        // Act
        var result = await _handler.Handle(query, CreateCancellationToken());

        // Assert
        result.Should().NotBeNull();
        result.Success.Should().BeTrue();
        result.History.Should().NotBeNull();
        result.History!.StartDate.Should().Be(startDate);
        result.History.EndDate.Should().Be(endDate);
        result.PageNumber.Should().Be(1);
        result.PageSize.Should().Be(100);
    }

    [Fact]
    public async Task Handle_WithPagination_ShouldReturnCorrectPageInfo()
    {
        // Arrange
        var vehicleId = Guid.NewGuid();
        var startDate = DateTime.UtcNow.AddDays(-7);
        var endDate = DateTime.UtcNow;
        var query = new GetTelemetryHistoryQuery(vehicleId, startDate, endDate)
        {
            PageNumber = 2,
            PageSize = 50
        };

        var vehicle = new Vehicle("Test Vehicle", "ABC-123", "Car");
        MockVehicleRepository.Setup(x => x.GetByIdAsync(vehicleId, It.IsAny<CancellationToken>()))
            .ReturnsAsync(vehicle);

        // Act
        var result = await _handler.Handle(query, CreateCancellationToken());

        // Assert
        result.Should().NotBeNull();
        result.Success.Should().BeTrue();
        result.PageNumber.Should().Be(2);
        result.PageSize.Should().Be(50);
        result.TotalPages.Should().BeGreaterOrEqualTo(0);
        result.HasNextPage.Should().BeFalse(); // Since we're returning mock data with 0 records
        result.HasPreviousPage.Should().BeTrue();
    }

    [Fact]
    public async Task Handle_WithIntervalMinutes_ShouldPassIntervalToRepository()
    {
        // Arrange
        var vehicleId = Guid.NewGuid();
        var startDate = DateTime.UtcNow.AddDays(-1);
        var endDate = DateTime.UtcNow;
        var query = new GetTelemetryHistoryQuery(vehicleId, startDate, endDate)
        {
            IntervalMinutes = 15,
            SignificantChangesOnly = true
        };

        var vehicle = new Vehicle("Test Vehicle", "ABC-123", "Car");
        MockVehicleRepository.Setup(x => x.GetByIdAsync(vehicleId, It.IsAny<CancellationToken>()))
            .ReturnsAsync(vehicle);

        // Act
        var result = await _handler.Handle(query, CreateCancellationToken());

        // Assert
        result.Should().NotBeNull();
        result.Success.Should().BeTrue();
        // Note: In the current implementation, interval and significant changes are not fully implemented
        // This test verifies that the parameters are accepted and don't cause errors
    }

    [Fact]
    public async Task Handle_WithRepositoryException_ShouldReturnFailure()
    {
        // Arrange
        var vehicleId = Guid.NewGuid();
        var startDate = DateTime.UtcNow.AddDays(-7);
        var endDate = DateTime.UtcNow;
        var query = new GetTelemetryHistoryQuery(vehicleId, startDate, endDate);

        MockVehicleRepository.Setup(x => x.GetByIdAsync(vehicleId, It.IsAny<CancellationToken>()))
            .ThrowsAsync(new InvalidOperationException("Database error"));

        // Act
        var result = await _handler.Handle(query, CreateCancellationToken());

        // Assert
        result.Should().NotBeNull();
        result.Success.Should().BeFalse();
        result.History.Should().BeNull();
        result.ErrorMessage.Should().Contain("error occurred");

        VerifyErrorLogged();
    }

    [Fact]
    public async Task Handle_WithEmptyQuery_ShouldUseDefaults()
    {
        // Arrange
        var vehicleId = Guid.NewGuid();
        var query = new GetTelemetryHistoryQuery
        {
            VehicleId = vehicleId,
            StartDate = DateTime.UtcNow.AddDays(-1),
            EndDate = DateTime.UtcNow
        };

        var vehicle = new Vehicle("Test Vehicle", "ABC-123", "Car");
        MockVehicleRepository.Setup(x => x.GetByIdAsync(vehicleId, It.IsAny<CancellationToken>()))
            .ReturnsAsync(vehicle);

        // Act
        var result = await _handler.Handle(query, CreateCancellationToken());

        // Assert
        result.Should().NotBeNull();
        result.Success.Should().BeTrue();
        result.PageNumber.Should().Be(1); // Default page number
        result.PageSize.Should().Be(100); // Default page size
    }

    [Fact]
    public async Task Handle_WithLargeDateRange_ShouldProcessSuccessfully()
    {
        // Arrange
        var vehicleId = Guid.NewGuid();
        var startDate = DateTime.UtcNow.AddDays(-90); // Maximum allowed range
        var endDate = DateTime.UtcNow;
        var query = new GetTelemetryHistoryQuery(vehicleId, startDate, endDate);

        var vehicle = new Vehicle("Test Vehicle", "ABC-123", "Car");
        MockVehicleRepository.Setup(x => x.GetByIdAsync(vehicleId, It.IsAny<CancellationToken>()))
            .ReturnsAsync(vehicle);

        // Act
        var result = await _handler.Handle(query, CreateCancellationToken());

        // Assert
        result.Should().NotBeNull();
        result.Success.Should().BeTrue();
        result.History.Should().NotBeNull();
        result.History!.StartDate.Should().Be(startDate);
        result.History.EndDate.Should().Be(endDate);
    }

    [Theory]
    [InlineData(1, 10)]
    [InlineData(5, 50)]
    [InlineData(10, 100)]
    public async Task Handle_WithDifferentPageSizes_ShouldReturnCorrectPagination(int pageNumber, int pageSize)
    {
        // Arrange
        var vehicleId = Guid.NewGuid();
        var startDate = DateTime.UtcNow.AddDays(-7);
        var endDate = DateTime.UtcNow;
        var query = new GetTelemetryHistoryQuery(vehicleId, startDate, endDate)
        {
            PageNumber = pageNumber,
            PageSize = pageSize
        };

        var vehicle = new Vehicle("Test Vehicle", "ABC-123", "Car");
        MockVehicleRepository.Setup(x => x.GetByIdAsync(vehicleId, It.IsAny<CancellationToken>()))
            .ReturnsAsync(vehicle);

        // Act
        var result = await _handler.Handle(query, CreateCancellationToken());

        // Assert
        result.Should().NotBeNull();
        result.Success.Should().BeTrue();
        result.PageNumber.Should().Be(pageNumber);
        result.PageSize.Should().Be(pageSize);
        result.TotalCount.Should().BeGreaterOrEqualTo(0);
    }

    [Fact]
    public async Task Handle_PlaceholderImplementation_ShouldReturnEmptyRecords()
    {
        // Arrange
        var vehicleId = Guid.NewGuid();
        var startDate = DateTime.UtcNow.AddDays(-7);
        var endDate = DateTime.UtcNow;
        var query = new GetTelemetryHistoryQuery(vehicleId, startDate, endDate);

        var vehicle = new Vehicle("Test Vehicle", "ABC-123", "Car");
        MockVehicleRepository.Setup(x => x.GetByIdAsync(vehicleId, It.IsAny<CancellationToken>()))
            .ReturnsAsync(vehicle);

        // Act
        var result = await _handler.Handle(query, CreateCancellationToken());

        // Assert
        result.Should().NotBeNull();
        result.Success.Should().BeTrue();
        result.History.Should().NotBeNull();
        result.History!.Records.Should().BeEmpty(); // Current implementation returns empty records
        result.History.TotalCount.Should().Be(0);

        // This test documents the current placeholder implementation
        // In a real implementation, this would return actual telemetry history data
    }
}
