using FleetXQ.Domain.Interfaces;
using MediatR;
using Microsoft.Extensions.Logging;

namespace FleetXQ.Application.Features.Vehicles.Commands.UpdateVehicleStatus;

/// <summary>
/// Handler for UpdateVehicleStatusCommand
/// </summary>
public sealed class UpdateVehicleStatusCommandHandler : IRequestHandler<UpdateVehicleStatusCommand, UpdateVehicleStatusResult>
{
    private readonly IVehicleRepository _vehicleRepository;
    private readonly ILogger<UpdateVehicleStatusCommandHandler> _logger;

    /// <summary>
    /// Initializes a new instance of the <see cref="UpdateVehicleStatusCommandHandler"/> class
    /// </summary>
    /// <param name="vehicleRepository">The vehicle repository</param>
    /// <param name="logger">The logger</param>
    public UpdateVehicleStatusCommandHandler(
        IVehicleRepository vehicleRepository,
        ILogger<UpdateVehicleStatusCommandHandler> logger)
    {
        _vehicleRepository = vehicleRepository ?? throw new ArgumentNullException(nameof(vehicleRepository));
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
    }

    /// <summary>
    /// Handles the UpdateVehicleStatusCommand
    /// </summary>
    /// <param name="request">The command request</param>
    /// <param name="cancellationToken">The cancellation token</param>
    /// <returns>The command result</returns>
    public async Task<UpdateVehicleStatusResult> Handle(UpdateVehicleStatusCommand request, CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogInformation("Updating status for vehicle {VehicleId} to {Status}", request.VehicleId, request.Status);

            // Get the vehicle
            var vehicle = await _vehicleRepository.GetByIdAsync(request.VehicleId, cancellationToken);
            if (vehicle == null)
            {
                _logger.LogWarning("Vehicle with ID {VehicleId} not found", request.VehicleId);
                return UpdateVehicleStatusResult.Failed(request.VehicleId, "Vehicle not found");
            }

            var previousStatus = vehicle.Status;

            // Check if status is already the same
            if (vehicle.Status == request.Status)
            {
                _logger.LogInformation("Vehicle {VehicleId} already has status {Status}", request.VehicleId, request.Status);
                return UpdateVehicleStatusResult.Successful(request.VehicleId, previousStatus, request.Status);
            }

            // Change the vehicle status
            vehicle.ChangeStatus(request.Status, request.Reason, request.ChangedBy, request.Context);

            // Update the vehicle in the repository
            await _vehicleRepository.UpdateAsync(vehicle, cancellationToken);

            _logger.LogInformation("Successfully updated vehicle {VehicleId} status from {PreviousStatus} to {NewStatus}", 
                request.VehicleId, previousStatus, request.Status);

            return UpdateVehicleStatusResult.Successful(request.VehicleId, previousStatus, request.Status);
        }
        catch (ArgumentException ex)
        {
            _logger.LogWarning(ex, "Invalid argument when updating vehicle {VehicleId} status", request.VehicleId);
            return UpdateVehicleStatusResult.Failed(request.VehicleId, ex.Message);
        }
        catch (InvalidOperationException ex)
        {
            _logger.LogWarning(ex, "Invalid operation when updating vehicle {VehicleId} status", request.VehicleId);
            return UpdateVehicleStatusResult.Failed(request.VehicleId, ex.Message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating status for vehicle {VehicleId}", request.VehicleId);
            return UpdateVehicleStatusResult.Failed(request.VehicleId, $"An error occurred while updating the vehicle status: {ex.Message}");
        }
    }
}
