using FleetXQ.Domain.Entities;
using FleetXQ.Domain.Enums;
using FleetXQ.Domain.ValueObjects;

namespace FleetXQ.Domain.Services;

/// <summary>
/// Implementation of vehicle assignment domain service
/// </summary>
public class VehicleAssignmentService : IVehicleAssignmentService
{
    /// <summary>
    /// Determines if a driver can be assigned to a vehicle
    /// </summary>
    /// <param name="driver">The driver to check</param>
    /// <param name="vehicle">The vehicle to check</param>
    /// <returns>Assignment validation result</returns>
    public AssignmentValidationResult CanAssignDriverToVehicle(Driver driver, Vehicle vehicle)
    {
        if (driver == null)
            return AssignmentValidationResult.Failure("Driver cannot be null");

        if (vehicle == null)
            return AssignmentValidationResult.Failure("Vehicle cannot be null");

        var result = new AssignmentValidationResult();

        // Check if driver is available
        if (!driver.IsAvailable)
        {
            result.AddError($"Driver {driver.FullName} is not available (Status: {driver.Status})");
        }

        // Check if vehicle is available
        if (!vehicle.IsAvailable)
        {
            result.AddError($"Vehicle {vehicle.VehicleName} is not available (Status: {vehicle.Status})");
        }

        // Check license compatibility
        if (!driver.IsLicenseCompatibleWith(vehicle.VehicleType))
        {
            result.AddError($"Driver license class {driver.LicenseClass} is not compatible with vehicle type {vehicle.VehicleType}");
        }

        // Check license expiry
        if (driver.IsLicenseExpired)
        {
            result.AddError($"Driver license has expired on {driver.LicenseExpiryDate:yyyy-MM-dd}");
        }

        // Add warnings for potential issues
        if (driver.IsLicenseExpiringSoon)
        {
            result.AddWarning($"Driver license expires soon on {driver.LicenseExpiryDate:yyyy-MM-dd}");
        }

        if (vehicle.NeedsMaintenance)
        {
            result.AddWarning($"Vehicle is due for maintenance on {vehicle.NextMaintenanceDate:yyyy-MM-dd}");
        }

        if (vehicle.RequiresImmediateAttention())
        {
            result.AddWarning("Vehicle requires immediate attention due to critical fuel level");
        }

        // Calculate compatibility score if assignment is valid
        if (result.IsValid)
        {
            result.CompatibilityScore = CalculateCompatibilityScore(driver, vehicle);
        }

        return result;
    }

    /// <summary>
    /// Finds the best available driver for a vehicle
    /// </summary>
    /// <param name="vehicle">The vehicle that needs a driver</param>
    /// <param name="availableDrivers">The list of available drivers</param>
    /// <param name="location">The current location (optional)</param>
    /// <returns>The best matching driver, or null if none suitable</returns>
    public Driver? FindBestDriverForVehicle(Vehicle vehicle, IEnumerable<Driver> availableDrivers, Location? location = null)
    {
        if (vehicle == null || availableDrivers == null)
            return null;

        var compatibleDrivers = availableDrivers
            .Where(driver => CanAssignDriverToVehicle(driver, vehicle).IsValid)
            .Select(driver => new
            {
                Driver = driver,
                Score = CalculateCompatibilityScore(driver, vehicle)
            })
            .OrderByDescending(x => x.Score)
            .ToList();

        return compatibleDrivers.FirstOrDefault()?.Driver;
    }

    /// <summary>
    /// Finds the best available vehicle for a driver
    /// </summary>
    /// <param name="driver">The driver that needs a vehicle</param>
    /// <param name="availableVehicles">The list of available vehicles</param>
    /// <param name="location">The current location (optional)</param>
    /// <returns>The best matching vehicle, or null if none suitable</returns>
    public Vehicle? FindBestVehicleForDriver(Driver driver, IEnumerable<Vehicle> availableVehicles, Location? location = null)
    {
        if (driver == null || availableVehicles == null)
            return null;

        var compatibleVehicles = availableVehicles
            .Where(vehicle => CanAssignDriverToVehicle(driver, vehicle).IsValid)
            .Select(vehicle => new
            {
                Vehicle = vehicle,
                Score = CalculateCompatibilityScore(driver, vehicle)
            })
            .OrderByDescending(x => x.Score)
            .ToList();

        return compatibleVehicles.FirstOrDefault()?.Vehicle;
    }

    /// <summary>
    /// Calculates the compatibility score between a driver and vehicle
    /// </summary>
    /// <param name="driver">The driver</param>
    /// <param name="vehicle">The vehicle</param>
    /// <returns>Compatibility score (0-100)</returns>
    public int CalculateCompatibilityScore(Driver driver, Vehicle vehicle)
    {
        if (driver == null || vehicle == null)
            return 0;

        var score = 100;

        // License compatibility (critical factor)
        if (!driver.IsLicenseCompatibleWith(vehicle.VehicleType))
            return 0; // No compatibility if license doesn't match

        // Experience factor based on years of service
        if (driver.YearsOfService.HasValue)
        {
            var experienceBonus = Math.Min(driver.YearsOfService.Value * 2, 10); // Max 10 points
            score += (int)experienceBonus;
        }

        // Age factor (prefer experienced but not too old drivers)
        if (driver.Age.HasValue)
        {
            var age = driver.Age.Value;
            if (age >= 25 && age <= 55)
                score += 5; // Optimal age range
            else if (age < 25)
                score -= 5; // Young driver penalty
            else if (age > 65)
                score -= 10; // Senior driver penalty
        }

        // License expiry penalty
        if (driver.IsLicenseExpiringSoon)
            score -= 15;

        // Vehicle condition factors
        if (vehicle.NeedsMaintenance)
            score -= 10;

        if (vehicle.RequiresImmediateAttention())
            score -= 20;

        // Vehicle type specific bonuses
        var vehicleTypeBonus = vehicle.VehicleType.ToLower() switch
        {
            "truck" when driver.LicenseClass?.Contains("CDL") == true => 10,
            "motorcycle" when driver.LicenseClass?.Contains("M") == true => 10,
            "electric" => 5, // Bonus for electric vehicles (environmental)
            _ => 0
        };
        score += vehicleTypeBonus;

        // Ensure score is within valid range
        return Math.Max(0, Math.Min(100, score));
    }

    /// <summary>
    /// Validates if an assignment change is allowed
    /// </summary>
    /// <param name="currentDriver">The current driver</param>
    /// <param name="newDriver">The new driver</param>
    /// <param name="vehicle">The vehicle</param>
    /// <param name="reason">The reason for the change</param>
    /// <returns>Validation result</returns>
    public AssignmentValidationResult ValidateAssignmentChange(Driver? currentDriver, Driver newDriver, 
        Vehicle vehicle, string reason)
    {
        // First check if new driver can be assigned
        var basicValidation = CanAssignDriverToVehicle(newDriver, vehicle);
        if (!basicValidation.IsValid)
            return basicValidation;

        var result = new AssignmentValidationResult
        {
            IsValid = true,
            CompatibilityScore = basicValidation.CompatibilityScore
        };

        // Additional validations for assignment changes
        if (currentDriver != null)
        {
            // Check if current driver is in the middle of a trip
            // This would require checking with trip repository, but we'll add a warning
            result.AddWarning("Ensure current driver is not in the middle of an active trip");

            // Compare compatibility scores
            var currentScore = CalculateCompatibilityScore(currentDriver, vehicle);
            var newScore = CalculateCompatibilityScore(newDriver, vehicle);

            if (newScore < currentScore)
            {
                result.AddWarning($"New driver compatibility score ({newScore}) is lower than current driver ({currentScore})");
            }
        }

        // Validate reason if provided
        if (string.IsNullOrWhiteSpace(reason))
        {
            result.AddWarning("Assignment change reason not provided");
        }

        return result;
    }

    /// <summary>
    /// Gets assignment recommendations for optimal fleet utilization
    /// </summary>
    /// <param name="drivers">Available drivers</param>
    /// <param name="vehicles">Available vehicles</param>
    /// <returns>List of assignment recommendations</returns>
    public IEnumerable<AssignmentRecommendation> GetAssignmentRecommendations(
        IEnumerable<Driver> drivers, IEnumerable<Vehicle> vehicles)
    {
        if (drivers == null || vehicles == null)
            return Enumerable.Empty<AssignmentRecommendation>();

        var recommendations = new List<AssignmentRecommendation>();
        var availableDrivers = drivers.Where(d => d.IsAvailable).ToList();
        var availableVehicles = vehicles.Where(v => v.IsAvailable).ToList();

        // Create all possible combinations and score them
        var combinations = from driver in availableDrivers
                          from vehicle in availableVehicles
                          let validation = CanAssignDriverToVehicle(driver, vehicle)
                          where validation.IsValid
                          select new
                          {
                              Driver = driver,
                              Vehicle = vehicle,
                              Score = validation.CompatibilityScore,
                              Validation = validation
                          };

        // Group by vehicle and select best driver for each
        var bestMatches = combinations
            .GroupBy(c => c.Vehicle.Id)
            .Select(g => g.OrderByDescending(c => c.Score).First())
            .OrderByDescending(c => c.Score);

        foreach (var match in bestMatches)
        {
            var recommendation = new AssignmentRecommendation
            {
                Driver = match.Driver,
                Vehicle = match.Vehicle,
                CompatibilityScore = match.Score,
                Priority = DeterminePriority(match.Vehicle, match.Driver),
                Reason = GenerateRecommendationReason(match.Driver, match.Vehicle, match.Score)
            };

            // Add benefits
            recommendation.Benefits.AddRange(GenerateBenefits(match.Driver, match.Vehicle));

            recommendations.Add(recommendation);
        }

        return recommendations.OrderByDescending(r => r.Priority).ThenByDescending(r => r.CompatibilityScore);
    }

    /// <summary>
    /// Determines the priority of an assignment recommendation
    /// </summary>
    /// <param name="vehicle">The vehicle</param>
    /// <param name="driver">The driver</param>
    /// <returns>The recommendation priority</returns>
    private static RecommendationPriority DeterminePriority(Vehicle vehicle, Driver driver)
    {
        if (vehicle.RequiresImmediateAttention())
            return RecommendationPriority.Critical;

        if (vehicle.NeedsMaintenance || driver.IsLicenseExpiringSoon)
            return RecommendationPriority.High;

        if (vehicle.VehicleType == "Electric" || driver.YearsOfService > 5)
            return RecommendationPriority.Medium;

        return RecommendationPriority.Low;
    }

    /// <summary>
    /// Generates a recommendation reason
    /// </summary>
    /// <param name="driver">The driver</param>
    /// <param name="vehicle">The vehicle</param>
    /// <param name="score">The compatibility score</param>
    /// <returns>The recommendation reason</returns>
    private static string GenerateRecommendationReason(Driver driver, Vehicle vehicle, int score)
    {
        var reasons = new List<string>();

        if (score >= 90)
            reasons.Add("Excellent compatibility match");
        else if (score >= 75)
            reasons.Add("Good compatibility match");
        else
            reasons.Add("Acceptable compatibility match");

        if (driver.YearsOfService > 5)
            reasons.Add("experienced driver");

        if (vehicle.VehicleType == "Electric")
            reasons.Add("eco-friendly vehicle");

        return string.Join(", ", reasons);
    }

    /// <summary>
    /// Generates benefits for an assignment
    /// </summary>
    /// <param name="driver">The driver</param>
    /// <param name="vehicle">The vehicle</param>
    /// <returns>List of benefits</returns>
    private static List<string> GenerateBenefits(Driver driver, Vehicle vehicle)
    {
        var benefits = new List<string>();

        if (driver.YearsOfService > 5)
            benefits.Add("Experienced driver reduces risk");

        if (vehicle.VehicleType == "Electric")
            benefits.Add("Reduced environmental impact");

        if (driver.LicenseClass?.Contains("CDL") == true && vehicle.VehicleType == "Truck")
            benefits.Add("Specialized license for vehicle type");

        if (!driver.IsLicenseExpiringSoon)
            benefits.Add("Valid license with good expiry date");

        return benefits;
    }
}
