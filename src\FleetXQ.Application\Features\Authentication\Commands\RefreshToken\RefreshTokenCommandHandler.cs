using FleetXQ.Application.Interfaces;
using FleetXQ.Domain.Interfaces;
using MediatR;
using Microsoft.Extensions.Logging;

namespace FleetXQ.Application.Features.Authentication.Commands.RefreshToken;

/// <summary>
/// Handler for RefreshTokenCommand
/// </summary>
public sealed class RefreshTokenCommandHandler : IRequestHandler<RefreshTokenCommand, RefreshTokenResult>
{
    private readonly IUserRepository _userRepository;
    private readonly ITokenService _tokenService;
    private readonly ILogger<RefreshTokenCommandHandler> _logger;

    /// <summary>
    /// Initializes a new instance of the <see cref="RefreshTokenCommandHandler"/> class
    /// </summary>
    /// <param name="userRepository">The user repository</param>
    /// <param name="tokenService">The token service</param>
    /// <param name="logger">The logger</param>
    public RefreshTokenCommandHandler(
        IUserRepository userRepository,
        ITokenService tokenService,
        ILogger<RefreshTokenCommandHandler> logger)
    {
        _userRepository = userRepository ?? throw new ArgumentNullException(nameof(userRepository));
        _tokenService = tokenService ?? throw new ArgumentNullException(nameof(tokenService));
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
    }

    /// <summary>
    /// Handles the RefreshTokenCommand
    /// </summary>
    /// <param name="request">The command request</param>
    /// <param name="cancellationToken">The cancellation token</param>
    /// <returns>The command result</returns>
    public async Task<RefreshTokenResult> Handle(RefreshTokenCommand request, CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogInformation("Token refresh attempt");

            // Validate the access token (even if expired, we need to extract user info)
            var userId = _tokenService.GetUserIdFromToken(request.AccessToken);
            if (userId == null)
            {
                _logger.LogWarning("Token refresh failed - invalid access token");
                return RefreshTokenResult.Failed("Invalid access token");
            }

            // Get the user
            var user = await _userRepository.GetByIdAsync(userId.Value, cancellationToken);
            if (user == null)
            {
                _logger.LogWarning("Token refresh failed - user not found: {UserId}", userId);
                return RefreshTokenResult.Failed("User not found");
            }

            // Check if account is active
            if (!user.IsActive)
            {
                _logger.LogWarning("Token refresh failed - account inactive: {UserId}", user.Id);
                return RefreshTokenResult.Failed("Account is inactive");
            }

            // Check if account is locked
            if (user.IsLockedOut)
            {
                _logger.LogWarning("Token refresh failed - account locked: {UserId}", user.Id);
                return RefreshTokenResult.Failed("Account is locked");
            }

            // In a real implementation, you would:
            // 1. Store refresh tokens in a database with expiry dates
            // 2. Validate the refresh token against the stored tokens
            // 3. Implement token rotation (invalidate old refresh token, issue new one)
            // 4. Track token usage for security monitoring
            
            // For this implementation, we'll do basic validation
            if (string.IsNullOrWhiteSpace(request.RefreshToken))
            {
                _logger.LogWarning("Token refresh failed - empty refresh token");
                return RefreshTokenResult.Failed("Invalid refresh token");
            }

            // Generate new tokens
            var newAccessToken = _tokenService.GenerateAccessToken(user);
            var newRefreshToken = _tokenService.GenerateRefreshToken();
            var tokenExpiry = DateTime.UtcNow.Add(_tokenService.GetAccessTokenExpiry());

            // Create user info
            var userInfo = new UserTokenInfo
            {
                Id = user.Id,
                Username = user.Username,
                Role = user.Role.ToString()
            };

            _logger.LogInformation("Token refresh successful for user: {UserId}", user.Id);

            return RefreshTokenResult.Successful(newAccessToken, newRefreshToken, tokenExpiry, userInfo);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during token refresh");
            return RefreshTokenResult.Failed("An error occurred during token refresh");
        }
    }
}
