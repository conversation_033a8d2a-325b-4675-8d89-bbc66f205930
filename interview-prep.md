# Fleet XQ Technical Interview - Preparation Guide

## Overview

This technical interview evaluates your capacity to reflect, design, and use modern tools to develop high-quality software. You'll build a complete Fleet Management application progressively, using AI tools throughout the process.

AI tools are encouraged and expected — we want to see how you leverage them effectively while maintaining technical excellence.

## 🎯 What We're Evaluating

- **Technical Skills**: Core programming and system design  
- **AI Integration**: Effective use of modern development tools  
- **Problem-Solving**: How you approach and break down challenges  
- **Code Quality**: Architecture decisions and maintainable solutions  
- **Communication**: Explaining technical decisions clearly  

## 📚 Technical Topics & Technologies

### Database & SQL (Phase 1)

- **Topics**: Complex queries, JOINs, aggregations, window functions, performance optimization  
- **Focus**: Fleet management data analysis, telemetry queries, time-series data  

### Backend Development (Phase 2-3)

- **Technologies**: .NET Core 8.0+, Entity Framework Core or NHibernate, SQL Server  
- **Topics**: RESTful APIs, CRUD operations, error handling, LINQ queries, ORM patterns  
- **Focus**: Building scalable fleet management APIs  

### Frontend Development (Phase 4)

- **Technology Options**: React, Vue.js, Angular, or Vanilla JavaScript  
- **Topics**: Modern JavaScript, API integration, responsive design, component architecture  
- **Focus**: Interactive fleet dashboards and user interfaces  

### Data Visualization (Phase 5)

- **Technology Options**: Chart.js, D3.js, Recharts, or preferred library  
- **Topics**: Charts, real-time updates, interactive features  
- **Focus**: Fleet analytics dashboards with multiple chart types  

## 🤖 AI Tools Integration

### Expected Usage

- **Code Generation**: Boilerplate, project structure, API endpoints  
- **Syntax Help**: Language-specific patterns and best practices  
- **Problem Solving**: Debugging assistance and optimization suggestions  
- **Testing**: Unit test generation and testing strategies  

### Tools You Can Use

- GitHub Copilot  
- Cursor  
- ChatGPT  
- Claude  
- Visual Studio IntelliCode  
- Any AI development assistant you prefer  

### What We Look For

- **Strategic Usage**: Using AI for appropriate tasks while maintaining control  
- **Critical Evaluation**: Reviewing and improving AI-generated code  
- **Effective Prompting**: Clear, specific requests to AI tools  
- **Quality Validation**: Testing and verifying AI suggestions  

## 🛠 Required Setup

### Must Have

- .NET Core 8.0+ SDK  
- Visual Studio 2022 or VS Code with C# extension  
- SQL Server Express + SSMS  
- Your preferred AI development tools  

### Your Choice

- Frontend framework (React/Vue/Angular/Vanilla JS)  
- Charting library (Chart.js/D3.js/other)  
- Additional AI tools and extensions  

## 📈 Interview Structure (1.5–2.5 hours)

1. **Database Foundation (20 min)** – SQL queries and data analysis  
2. **Core API Development (25 min)** – Build REST APIs with .NET Core  
3. **API Expansion (20 min)** – Scale development using AI tools  
4. **Frontend Integration (30 min)** – Build UI with API integration  
5. **Data Visualization (25 min)** – Create analytics dashboard  
6. **Advanced Features (Optional)** – Security, testing, optimization  

Each phase builds on the previous one. Interview may stop early or extend based on performance.

## 💡 How to Prepare

### Technical Review

- **SQL**: Complex queries, JOINs, window functions, performance tuning  
- **.NET Core**: Web API development, Entity Framework or NHibernate, LINQ  
- **Frontend**: Your chosen framework, async/await, API consumption  
- **Charting**: Basic chart creation with your preferred library  

### AI Tool Preparation

- Test your preferred AI development tools  
- Practice effective prompting techniques  
- Ensure tools are logged in and working  
- Have backup options ready  

### Practice Project

Build a small application that combines:

- Database with multiple related tables  
- .NET Core API with CRUD operations using EF Core or NHibernate  
- Frontend that consumes the API  
- At least one chart or visualization  

## ❓ Key Questions Answered

**Q: How much should I use AI tools?**  
**A**: Use them strategically throughout. We want to see effective AI collaboration, not replacement of your thinking.

**Q: What if I'm not expert in all technologies?**  
**A**: Focus on your strengths. We value learning ability and problem-solving over knowing everything.

**Q: Is this a test to trip me up?**  
**A**: No, it's a collaborative coding session. Ask questions, think aloud, and show your process.

## 🎯 Success Tips

- Start Simple: Get basic functionality working first  
- Use AI Strategically: For boilerplate, syntax help, and exploration  
- Validate Everything: Always review and test AI-generated code  
- Communicate Clearly: Explain your approach and decisions  
- Ask Questions: Clarify requirements when needed  