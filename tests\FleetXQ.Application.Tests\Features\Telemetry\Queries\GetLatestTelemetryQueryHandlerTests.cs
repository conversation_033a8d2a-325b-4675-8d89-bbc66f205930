using FleetXQ.Application.Features.Telemetry.Queries.GetLatestTelemetry;
using FleetXQ.Application.Tests.Common;
using FleetXQ.Domain.Entities;
using FleetXQ.Domain.Enums;
using FleetXQ.Domain.ValueObjects;
using FluentAssertions;
using Moq;

namespace FleetXQ.Application.Tests.Features.Telemetry.Queries;

public class GetLatestTelemetryQueryHandlerTests : QueryHandlerTestBase<GetLatestTelemetryQueryHandler>
{
    private readonly GetLatestTelemetryQueryHandler _handler;

    public GetLatestTelemetryQueryHandlerTests()
    {
        _handler = new GetLatestTelemetryQueryHandler(MockVehicleRepository.Object, Mapper, MockLogger.Object);
    }

    [Fact]
    public async Task Handle_WithSpecificVehicleId_ShouldReturnTelemetryForVehicle()
    {
        // Arrange
        var vehicleId = Guid.NewGuid();
        var query = new GetLatestTelemetryQuery(vehicleId);

        var vehicle = CreateVehicleWithTelemetry("Test Vehicle", "ABC-123");
        MockVehicleRepository.Setup(x => x.GetByIdAsync(vehicleId, It.IsAny<CancellationToken>()))
            .ReturnsAsync(vehicle);

        // Act
        var result = await _handler.Handle(query, CreateCancellationToken());

        // Assert
        result.Should().NotBeNull();
        result.Success.Should().BeTrue();
        result.Telemetry.Should().NotBeNull();
        result.TelemetryList.Should().BeEmpty();
        result.ErrorMessage.Should().BeNull();

        var telemetry = result.Telemetry!;
        telemetry.VehicleId.Should().Be(vehicle.Id);
        telemetry.VehicleName.Should().Be("Test Vehicle");
        telemetry.LicensePlate.Should().Be("ABC-123");
        telemetry.SpeedKmh.Should().Be(65m);
        telemetry.SpeedMph.Should().BeApproximately(40.39m, 0.1m); // 65 km/h ≈ 40.39 mph
        telemetry.FuelLevelPercentage.Should().Be(75m);
        telemetry.CurrentMileage.Should().Be(50000m);
        telemetry.IsMoving.Should().BeTrue();
        telemetry.Location.Should().NotBeNull();
        telemetry.Location.Latitude.Should().Be(40.7128m);
        telemetry.Location.Longitude.Should().Be(-74.0060m);

        VerifyRepositoryGetByIdCalled(MockVehicleRepository, vehicleId);
        VerifyInformationLogged(Times.AtLeastOnce);
    }

    [Fact]
    public async Task Handle_WithNonExistentVehicle_ShouldReturnNotFound()
    {
        // Arrange
        var vehicleId = Guid.NewGuid();
        var query = new GetLatestTelemetryQuery(vehicleId);

        MockVehicleRepository.Setup(x => x.GetByIdAsync(vehicleId, It.IsAny<CancellationToken>()))
            .ReturnsAsync((Vehicle?)null);

        // Act
        var result = await _handler.Handle(query, CreateCancellationToken());

        // Assert
        result.Should().NotBeNull();
        result.Success.Should().BeFalse();
        result.Telemetry.Should().BeNull();
        result.ErrorMessage.Should().Contain("No telemetry data found");

        VerifyRepositoryGetByIdCalled(MockVehicleRepository, vehicleId);
        VerifyWarningLogged();
    }

    [Fact]
    public async Task Handle_WithOldTelemetryData_ShouldReturnNotFound()
    {
        // Arrange
        var vehicleId = Guid.NewGuid();
        var query = new GetLatestTelemetryQuery(vehicleId) { MaxAgeHours = 1 };

        var vehicle = new Vehicle("Test Vehicle", "ABC-123", "Car");
        // Set telemetry update time to 2 hours ago (older than max age)
        var oldTelemetryTime = DateTime.UtcNow.AddHours(-2);
        vehicle.GetType().GetProperty("LastTelemetryUpdate")?.SetValue(vehicle, oldTelemetryTime);

        MockVehicleRepository.Setup(x => x.GetByIdAsync(vehicleId, It.IsAny<CancellationToken>()))
            .ReturnsAsync(vehicle);

        // Act
        var result = await _handler.Handle(query, CreateCancellationToken());

        // Assert
        result.Should().NotBeNull();
        result.Success.Should().BeFalse();
        result.Telemetry.Should().BeNull();
        result.ErrorMessage.Should().Contain("No telemetry data found");

        VerifyInformationLogged();
    }

    [Fact]
    public async Task Handle_WithoutVehicleId_ShouldReturnAllVehiclesTelemetry()
    {
        // Arrange
        var query = new GetLatestTelemetryQuery { ActiveVehiclesOnly = true };

        var vehicles = new List<Vehicle>
        {
            CreateVehicleWithTelemetry("Vehicle A", "AAA-001"),
            CreateVehicleWithTelemetry("Vehicle B", "BBB-002"),
            CreateVehicleWithTelemetry("Vehicle C", "CCC-003")
        };

        MockVehicleRepository.Setup(x => x.GetByStatusAsync(VehicleStatus.Active, It.IsAny<CancellationToken>()))
            .ReturnsAsync(vehicles);

        // Act
        var result = await _handler.Handle(query, CreateCancellationToken());

        // Assert
        result.Should().NotBeNull();
        result.Success.Should().BeTrue();
        result.Telemetry.Should().BeNull();
        result.TelemetryList.Should().HaveCount(3);
        result.ErrorMessage.Should().BeNull();

        result.TelemetryList.Should().OnlyContain(t => t.VehicleName.StartsWith("Vehicle"));
        MockVehicleRepository.Verify(x => x.GetByStatusAsync(VehicleStatus.Active, It.IsAny<CancellationToken>()), Times.Once);
        VerifyInformationLogged(Times.AtLeastOnce);
    }

    [Fact]
    public async Task Handle_WithAllVehicles_ShouldReturnAllVehiclesTelemetry()
    {
        // Arrange
        var query = new GetLatestTelemetryQuery { ActiveVehiclesOnly = false };

        var vehicles = new List<Vehicle>
        {
            CreateVehicleWithTelemetry("Vehicle A", "AAA-001"),
            CreateVehicleWithTelemetry("Vehicle B", "BBB-002")
        };

        // Set one vehicle to maintenance status
        vehicles[1].ChangeStatus(VehicleStatus.Maintenance, "Scheduled maintenance", Guid.NewGuid());

        MockVehicleRepository.Setup(x => x.GetAllAsync(It.IsAny<CancellationToken>()))
            .ReturnsAsync(vehicles);

        // Act
        var result = await _handler.Handle(query, CreateCancellationToken());

        // Assert
        result.Should().NotBeNull();
        result.Success.Should().BeTrue();
        result.TelemetryList.Should().HaveCount(2);

        MockVehicleRepository.Verify(x => x.GetAllAsync(It.IsAny<CancellationToken>()), Times.Once);
    }

    [Fact]
    public async Task Handle_WithNoRecentTelemetry_ShouldReturnEmptyList()
    {
        // Arrange
        var query = new GetLatestTelemetryQuery { MaxAgeHours = 1 };

        var vehicles = new List<Vehicle>
        {
            new("Vehicle A", "AAA-001", "Car"),
            new("Vehicle B", "BBB-002", "Car")
        };

        // No telemetry data set, so LastTelemetryUpdate will be null

        MockVehicleRepository.Setup(x => x.GetByStatusAsync(VehicleStatus.Active, It.IsAny<CancellationToken>()))
            .ReturnsAsync(vehicles);

        // Act
        var result = await _handler.Handle(query, CreateCancellationToken());

        // Assert
        result.Should().NotBeNull();
        result.Success.Should().BeTrue();
        result.TelemetryList.Should().BeEmpty();

        VerifyInformationLogged();
    }

    [Fact]
    public async Task Handle_WithRepositoryException_ShouldReturnFailure()
    {
        // Arrange
        var vehicleId = Guid.NewGuid();
        var query = new GetLatestTelemetryQuery(vehicleId);

        MockVehicleRepository.Setup(x => x.GetByIdAsync(vehicleId, It.IsAny<CancellationToken>()))
            .ThrowsAsync(new InvalidOperationException("Database error"));

        // Act
        var result = await _handler.Handle(query, CreateCancellationToken());

        // Assert
        result.Should().NotBeNull();
        result.Success.Should().BeFalse();
        result.Telemetry.Should().BeNull();
        result.ErrorMessage.Should().Contain("error occurred");

        VerifyErrorLogged();
    }

    [Fact]
    public async Task Handle_WithVehicleWithoutLocation_ShouldReturnTelemetryWithEmptyLocation()
    {
        // Arrange
        var vehicleId = Guid.NewGuid();
        var query = new GetLatestTelemetryQuery(vehicleId);

        var vehicle = new Vehicle("Test Vehicle", "ABC-123", "Car");
        // Update telemetry without location
        var speed = new Speed(30m);
        var fuelLevel = new FuelLevel(50m);
        vehicle.UpdateTelemetry(null, speed, fuelLevel, 25000m);

        MockVehicleRepository.Setup(x => x.GetByIdAsync(vehicleId, It.IsAny<CancellationToken>()))
            .ReturnsAsync(vehicle);

        // Act
        var result = await _handler.Handle(query, CreateCancellationToken());

        // Assert
        result.Should().NotBeNull();
        result.Success.Should().BeTrue();
        result.Telemetry.Should().NotBeNull();

        var telemetry = result.Telemetry!;
        telemetry.SpeedKmh.Should().Be(30m);
        telemetry.FuelLevelPercentage.Should().Be(50m);
        telemetry.CurrentMileage.Should().Be(25000m);
        telemetry.Location.Should().NotBeNull();
        telemetry.Location.Latitude.Should().Be(0m);
        telemetry.Location.Longitude.Should().Be(0m);
    }

    private static Vehicle CreateVehicleWithTelemetry(string name, string licensePlate)
    {
        var vehicle = new Vehicle(name, licensePlate, "Car");
        
        // Set up telemetry data
        var location = new Location(40.7128m, -74.0060m);
        var speed = new Speed(65m);
        var fuelLevel = new FuelLevel(75m);
        vehicle.UpdateTelemetry(location, speed, fuelLevel, 50000m);

        return vehicle;
    }
}
