using FleetXQ.Domain.Entities;
using FleetXQ.Domain.Enums;
using FleetXQ.Domain.Services;
using FleetXQ.Domain.ValueObjects;
using Xunit;

namespace FleetXQ.Domain.Tests.Services;

public class VehicleAssignmentServiceTests
{
    private readonly VehicleAssignmentService _service;

    public VehicleAssignmentServiceTests()
    {
        _service = new VehicleAssignmentService();
    }

    [Fact]
    public void CanAssignDriverToVehicle_WithValidDriverAndVehicle_ShouldReturnValidResult()
    {
        // Arrange
        var driver = CreateValidDriver();
        var vehicle = CreateValidVehicle();

        // Act
        var result = _service.CanAssignDriverToVehicle(driver, vehicle);

        // Assert
        Assert.True(result.IsValid);
        Assert.Empty(result.Messages);
        Assert.True(result.CompatibilityScore > 0);
    }

    [Fact]
    public void CanAssignDriverToVehicle_WithNullDriver_ShouldReturnInvalidResult()
    {
        // Arrange
        var vehicle = CreateValidVehicle();

        // Act
        var result = _service.CanAssignDriverToVehicle(null!, vehicle);

        // Assert
        Assert.False(result.IsValid);
        Assert.Contains("Driver cannot be null", result.Messages);
    }

    [Fact]
    public void CanAssignDriverToVehicle_WithNullVehicle_ShouldReturnInvalidResult()
    {
        // Arrange
        var driver = CreateValidDriver();

        // Act
        var result = _service.CanAssignDriverToVehicle(driver, null!);

        // Assert
        Assert.False(result.IsValid);
        Assert.Contains("Vehicle cannot be null", result.Messages);
    }

    [Fact]
    public void CanAssignDriverToVehicle_WithInactiveDriver_ShouldReturnInvalidResult()
    {
        // Arrange
        var driver = CreateValidDriver();
        driver.ChangeStatus(DriverStatus.Inactive);
        var vehicle = CreateValidVehicle();

        // Act
        var result = _service.CanAssignDriverToVehicle(driver, vehicle);

        // Assert
        Assert.False(result.IsValid);
        Assert.Contains("is not available", result.Messages.First());
    }

    [Fact]
    public void CanAssignDriverToVehicle_WithUnavailableVehicle_ShouldReturnInvalidResult()
    {
        // Arrange
        var driver = CreateValidDriver();
        var vehicle = CreateValidVehicle();
        vehicle.ChangeStatus(VehicleStatus.Maintenance);

        // Act
        var result = _service.CanAssignDriverToVehicle(driver, vehicle);

        // Assert
        Assert.False(result.IsValid);
        Assert.Contains("is not available", result.Messages.First());
    }

    [Fact]
    public void CanAssignDriverToVehicle_WithIncompatibleLicense_ShouldReturnInvalidResult()
    {
        // Arrange
        var driver = CreateValidDriver();
        driver.UpdateLicenseInfo("D123", "Class C", DateTime.UtcNow.AddYears(1));
        var vehicle = CreateValidVehicle("Truck Vehicle", "ABC-123", "Truck");

        // Act
        var result = _service.CanAssignDriverToVehicle(driver, vehicle);

        // Assert
        Assert.False(result.IsValid);
        Assert.Contains("is not compatible with vehicle type", result.Messages.First());
    }

    [Fact]
    public void CanAssignDriverToVehicle_WithExpiredLicense_ShouldReturnInvalidResult()
    {
        // Arrange
        var driver = CreateValidDriver();
        driver.UpdateLicenseInfo("D123", "Class B", DateTime.UtcNow.AddDays(-1)); // Expired
        var vehicle = CreateValidVehicle();

        // Act
        var result = _service.CanAssignDriverToVehicle(driver, vehicle);

        // Assert
        Assert.False(result.IsValid);
        Assert.Contains("license has expired", result.Messages.First());
    }

    [Fact]
    public void CanAssignDriverToVehicle_WithLicenseExpiringSoon_ShouldReturnValidResultWithWarning()
    {
        // Arrange
        var driver = CreateValidDriver();
        driver.UpdateLicenseInfo("D123", "Class B", DateTime.UtcNow.AddDays(15)); // Expiring soon
        var vehicle = CreateValidVehicle();

        // Act
        var result = _service.CanAssignDriverToVehicle(driver, vehicle);

        // Assert
        Assert.True(result.IsValid);
        Assert.Contains("license expires soon", result.Warnings.First());
    }

    [Fact]
    public void FindBestDriverForVehicle_WithCompatibleDrivers_ShouldReturnBestMatch()
    {
        // Arrange
        var vehicle = CreateValidVehicle();
        var drivers = new List<Driver>
        {
            CreateDriverWithExperience("John", "Doe", 2),
            CreateDriverWithExperience("Jane", "Smith", 5),
            CreateDriverWithExperience("Bob", "Johnson", 1)
        };

        // Act
        var bestDriver = _service.FindBestDriverForVehicle(vehicle, drivers);

        // Assert
        Assert.NotNull(bestDriver);
        Assert.Equal("Jane Smith", bestDriver.FullName); // Most experienced should be best
    }

    [Fact]
    public void FindBestDriverForVehicle_WithNoCompatibleDrivers_ShouldReturnNull()
    {
        // Arrange
        var vehicle = CreateValidVehicle("Truck", "ABC-123", "Truck");
        var drivers = new List<Driver>
        {
            CreateDriverWithLicense("John", "Doe", "Class C"), // Not compatible with truck
            CreateDriverWithLicense("Jane", "Smith", "Class C")
        };

        // Act
        var bestDriver = _service.FindBestDriverForVehicle(vehicle, drivers);

        // Assert
        Assert.Null(bestDriver);
    }

    [Fact]
    public void CalculateCompatibilityScore_WithExperiencedDriver_ShouldReturnHighScore()
    {
        // Arrange
        var driver = CreateDriverWithExperience("John", "Doe", 10);
        var vehicle = CreateValidVehicle();

        // Act
        var score = _service.CalculateCompatibilityScore(driver, vehicle);

        // Assert
        Assert.True(score > 100); // Should get experience bonus
    }

    [Fact]
    public void CalculateCompatibilityScore_WithOptimalAgeDriver_ShouldGetAgeBonus()
    {
        // Arrange
        var driver = CreateValidDriver();
        driver.SetDateOfBirth(DateTime.UtcNow.AddYears(-35)); // 35 years old - optimal range
        var vehicle = CreateValidVehicle();

        // Act
        var score = _service.CalculateCompatibilityScore(driver, vehicle);

        // Assert
        Assert.True(score >= 105); // Should get age bonus
    }

    [Fact]
    public void CalculateCompatibilityScore_WithYoungDriver_ShouldGetPenalty()
    {
        // Arrange
        var driver = CreateValidDriver();
        driver.SetDateOfBirth(DateTime.UtcNow.AddYears(-20)); // 20 years old - young
        var vehicle = CreateValidVehicle();

        // Act
        var score = _service.CalculateCompatibilityScore(driver, vehicle);

        // Assert
        Assert.True(score < 100); // Should get age penalty
    }

    [Fact]
    public void CalculateCompatibilityScore_WithCDLDriverAndTruck_ShouldGetVehicleTypeBonus()
    {
        // Arrange
        var driver = CreateDriverWithLicense("John", "Doe", "CDL Class A");
        var vehicle = CreateValidVehicle("Truck", "ABC-123", "Truck");

        // Act
        var score = _service.CalculateCompatibilityScore(driver, vehicle);

        // Assert
        Assert.True(score >= 110); // Should get vehicle type bonus
    }

    [Fact]
    public void GetAssignmentRecommendations_WithMultipleDriversAndVehicles_ShouldReturnRecommendations()
    {
        // Arrange
        var drivers = new List<Driver>
        {
            CreateDriverWithExperience("John", "Doe", 5),
            CreateDriverWithExperience("Jane", "Smith", 3)
        };

        var vehicles = new List<Vehicle>
        {
            CreateValidVehicle("Vehicle 1", "ABC-123", "Car"),
            CreateValidVehicle("Vehicle 2", "DEF-456", "Van")
        };

        // Act
        var recommendations = _service.GetAssignmentRecommendations(drivers, vehicles);

        // Assert
        Assert.NotEmpty(recommendations);
        Assert.All(recommendations, r => Assert.True(r.CompatibilityScore > 0));
        Assert.All(recommendations, r => Assert.NotNull(r.Driver));
        Assert.All(recommendations, r => Assert.NotNull(r.Vehicle));
    }

    [Fact]
    public void ValidateAssignmentChange_WithBetterDriver_ShouldReturnValidResult()
    {
        // Arrange
        var currentDriver = CreateDriverWithExperience("Current", "Driver", 2);
        var newDriver = CreateDriverWithExperience("New", "Driver", 5);
        var vehicle = CreateValidVehicle();
        var reason = "Assigning more experienced driver";

        // Act
        var result = _service.ValidateAssignmentChange(currentDriver, newDriver, vehicle, reason);

        // Assert
        Assert.True(result.IsValid);
    }

    [Fact]
    public void ValidateAssignmentChange_WithWorseDriver_ShouldReturnValidResultWithWarning()
    {
        // Arrange
        var currentDriver = CreateDriverWithExperience("Current", "Driver", 5);
        var newDriver = CreateDriverWithExperience("New", "Driver", 2);
        var vehicle = CreateValidVehicle();
        var reason = "Emergency assignment";

        // Act
        var result = _service.ValidateAssignmentChange(currentDriver, newDriver, vehicle, reason);

        // Assert
        Assert.True(result.IsValid);
        Assert.Contains("compatibility score", result.Warnings.First());
    }

    private Driver CreateValidDriver()
    {
        var driver = new Driver("John", "Doe", "<EMAIL>");
        driver.UpdateLicenseInfo("D123456", "Class B", DateTime.UtcNow.AddYears(2));
        return driver;
    }

    private Driver CreateDriverWithExperience(string firstName, string lastName, int yearsOfService)
    {
        var driver = new Driver(firstName, lastName);
        driver.UpdateLicenseInfo("D123456", "Class B", DateTime.UtcNow.AddYears(2));
        driver.UpdateEmploymentInfo($"EMP{firstName}", DateTime.UtcNow.AddYears(-yearsOfService));
        return driver;
    }

    private Driver CreateDriverWithLicense(string firstName, string lastName, string licenseClass)
    {
        var driver = new Driver(firstName, lastName);
        driver.UpdateLicenseInfo("D123456", licenseClass, DateTime.UtcNow.AddYears(2));
        return driver;
    }

    private Vehicle CreateValidVehicle(string name = "Test Vehicle", string licensePlate = "ABC-123", string vehicleType = "Car")
    {
        return new Vehicle(name, licensePlate, vehicleType);
    }
}
