using AutoMapper;
using FleetXQ.Application.Features.Telemetry.DTOs;
using FleetXQ.Domain.Enums;
using FleetXQ.Domain.Interfaces;
using MediatR;
using Microsoft.Extensions.Logging;

namespace FleetXQ.Application.Features.Telemetry.Queries.GetLatestTelemetry;

/// <summary>
/// Handler for GetLatestTelemetryQuery
/// </summary>
public sealed class GetLatestTelemetryQueryHandler : IRequestHandler<GetLatestTelemetryQuery, GetLatestTelemetryResult>
{
    private readonly IVehicleRepository _vehicleRepository;
    private readonly IMapper _mapper;
    private readonly ILogger<GetLatestTelemetryQueryHandler> _logger;

    /// <summary>
    /// Initializes a new instance of the <see cref="GetLatestTelemetryQueryHandler"/> class
    /// </summary>
    /// <param name="vehicleRepository">The vehicle repository</param>
    /// <param name="mapper">The AutoMapper instance</param>
    /// <param name="logger">The logger</param>
    public GetLatestTelemetryQueryHandler(
        IVehicleRepository vehicleRepository,
        IMapper mapper,
        ILogger<GetLatestTelemetryQueryHandler> logger)
    {
        _vehicleRepository = vehicleRepository ?? throw new ArgumentNullException(nameof(vehicleRepository));
        _mapper = mapper ?? throw new ArgumentNullException(nameof(mapper));
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
    }

    /// <summary>
    /// Handles the GetLatestTelemetryQuery
    /// </summary>
    /// <param name="request">The query request</param>
    /// <param name="cancellationToken">The cancellation token</param>
    /// <returns>The query result</returns>
    public async Task<GetLatestTelemetryResult> Handle(GetLatestTelemetryQuery request, CancellationToken cancellationToken)
    {
        try
        {
            if (request.VehicleId.HasValue)
            {
                return await GetLatestTelemetryForVehicle(request.VehicleId.Value, request.MaxAgeHours, cancellationToken);
            }
            else
            {
                return await GetLatestTelemetryForAllVehicles(request.ActiveVehiclesOnly, request.MaxAgeHours, cancellationToken);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting latest telemetry data");
            return GetLatestTelemetryResult.Failed($"An error occurred while retrieving telemetry data: {ex.Message}");
        }
    }

    /// <summary>
    /// Gets latest telemetry for a specific vehicle
    /// </summary>
    /// <param name="vehicleId">The vehicle ID</param>
    /// <param name="maxAgeHours">The maximum age in hours</param>
    /// <param name="cancellationToken">The cancellation token</param>
    /// <returns>The telemetry result</returns>
    private async Task<GetLatestTelemetryResult> GetLatestTelemetryForVehicle(Guid vehicleId, int maxAgeHours, CancellationToken cancellationToken)
    {
        _logger.LogInformation("Getting latest telemetry for vehicle {VehicleId}", vehicleId);

        var vehicle = await _vehicleRepository.GetByIdAsync(vehicleId, cancellationToken);
        if (vehicle == null)
        {
            _logger.LogWarning("Vehicle with ID {VehicleId} not found", vehicleId);
            return GetLatestTelemetryResult.NotFound();
        }

        // Check if telemetry data is within the acceptable age
        var cutoffTime = DateTime.UtcNow.AddHours(-maxAgeHours);
        if (vehicle.LastTelemetryUpdate == null || vehicle.LastTelemetryUpdate < cutoffTime)
        {
            _logger.LogInformation("No recent telemetry data found for vehicle {VehicleId}", vehicleId);
            return GetLatestTelemetryResult.NotFound();
        }

        var telemetryDto = CreateTelemetryDto(vehicle);
        
        _logger.LogInformation("Successfully retrieved latest telemetry for vehicle {VehicleId}", vehicleId);
        return GetLatestTelemetryResult.CreateSuccess(telemetryDto);
    }

    /// <summary>
    /// Gets latest telemetry for all vehicles
    /// </summary>
    /// <param name="activeVehiclesOnly">Whether to include only active vehicles</param>
    /// <param name="maxAgeHours">The maximum age in hours</param>
    /// <param name="cancellationToken">The cancellation token</param>
    /// <returns>The telemetry result</returns>
    private async Task<GetLatestTelemetryResult> GetLatestTelemetryForAllVehicles(bool activeVehiclesOnly, int maxAgeHours, CancellationToken cancellationToken)
    {
        _logger.LogInformation("Getting latest telemetry for all vehicles (ActiveOnly: {ActiveOnly})", activeVehiclesOnly);

        IEnumerable<Domain.Entities.Vehicle> vehicles;
        
        if (activeVehiclesOnly)
        {
            vehicles = await _vehicleRepository.GetByStatusAsync(VehicleStatus.Active, cancellationToken);
        }
        else
        {
            vehicles = await _vehicleRepository.GetAllAsync(cancellationToken);
        }

        var cutoffTime = DateTime.UtcNow.AddHours(-maxAgeHours);
        
        // Filter vehicles with recent telemetry data
        var vehiclesWithTelemetry = vehicles
            .Where(v => v.LastTelemetryUpdate != null && v.LastTelemetryUpdate >= cutoffTime)
            .ToList();

        if (!vehiclesWithTelemetry.Any())
        {
            _logger.LogInformation("No vehicles with recent telemetry data found");
            return GetLatestTelemetryResult.SuccessMultiple(new List<TelemetryDto>());
        }

        var telemetryDtos = vehiclesWithTelemetry.Select(CreateTelemetryDto).ToList();
        
        _logger.LogInformation("Successfully retrieved latest telemetry for {Count} vehicles", telemetryDtos.Count);
        return GetLatestTelemetryResult.SuccessMultiple(telemetryDtos);
    }

    /// <summary>
    /// Creates a TelemetryDto from a Vehicle entity
    /// </summary>
    /// <param name="vehicle">The vehicle entity</param>
    /// <returns>The telemetry DTO</returns>
    private static TelemetryDto CreateTelemetryDto(Domain.Entities.Vehicle vehicle)
    {
        return new TelemetryDto
        {
            VehicleId = vehicle.Id,
            VehicleName = vehicle.VehicleName,
            LicensePlate = vehicle.LicensePlate,
            Location = vehicle.CurrentLocation != null 
                ? new FleetXQ.Application.Features.Vehicles.DTOs.LocationDto
                {
                    Latitude = vehicle.CurrentLocation.Latitude,
                    Longitude = vehicle.CurrentLocation.Longitude
                }
                : new FleetXQ.Application.Features.Vehicles.DTOs.LocationDto(),
            SpeedKmh = vehicle.CurrentSpeed.KilometersPerHour,
            SpeedMph = vehicle.CurrentSpeed.MilesPerHour,
            FuelLevelPercentage = vehicle.CurrentFuelLevel?.Percentage,
            CurrentMileage = vehicle.CurrentMileage,
            IsMoving = vehicle.IsMoving,
            Timestamp = vehicle.LastTelemetryUpdate ?? DateTime.UtcNow
        };
    }
}
