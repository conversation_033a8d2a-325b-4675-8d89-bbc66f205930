using FleetXQ.Api.Controllers;
using FleetXQ.Api.Models;
using FleetXQ.Api.Tests.Common;
using FleetXQ.Application.Features.Authentication.Commands.LoginUser;
using FleetXQ.Application.Features.Authentication.Commands.RefreshToken;
using FleetXQ.Application.Features.Authentication.Queries.GetUserProfile;
using Microsoft.AspNetCore.Mvc;

namespace FleetXQ.Api.Tests.Controllers;

/// <summary>
/// Unit tests for AuthController
/// </summary>
public class AuthControllerTests : TestBase
{
    private readonly AuthController _controller;

    public AuthControllerTests()
    {
        _controller = CreateController<AuthController>();
    }

    [Fact]
    public async Task Login_WithValidCredentials_ShouldReturnSuccessResponse()
    {
        // Arrange
        var command = new LoginUserCommand
        {
            UsernameOrEmail = "<EMAIL>",
            Password = "password123",
            RememberMe = false
        };

        var expectedResult = LoginUserResult.Successful(
            "access-token",
            "refresh-token",
            DateTime.UtcNow.AddHours(1),
            new UserInfo
            {
                Id = Guid.NewGuid(),
                Username = "testuser",
                Email = "<EMAIL>",
                FullName = "Test User",
                Role = "User",
                IsEmailConfirmed = true
            }
        );

        MockMediator.Setup(x => x.Send(It.IsAny<LoginUserCommand>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(expectedResult);

        // Act
        var result = await _controller.Login(command);

        // Assert
        var response = AssertSuccessResult(result);
        response.Data.Should().NotBeNull();
        response.Data!.Success.Should().BeTrue();
        response.Data.AccessToken.Should().NotBeNullOrEmpty();
        response.Data.RefreshToken.Should().NotBeNullOrEmpty();
        response.Data.User.Should().NotBeNull();

        MockMediator.Verify(x => x.Send(It.IsAny<LoginUserCommand>(), It.IsAny<CancellationToken>()), Times.Once);
    }

    [Fact]
    public async Task Login_WithInvalidCredentials_ShouldReturnBadRequest()
    {
        // Arrange
        var command = new LoginUserCommand
        {
            UsernameOrEmail = "<EMAIL>",
            Password = "wrongpassword"
        };

        var expectedResult = LoginUserResult.Failed("Invalid username/email or password", 4);

        MockMediator.Setup(x => x.Send(It.IsAny<LoginUserCommand>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(expectedResult);

        // Act
        var result = await _controller.Login(command);

        // Assert
        var response = AssertBadRequestResult(result);
        response.Message.Should().Contain("Invalid username/email or password");
        response.Errors.Should().NotBeNull();

        MockMediator.Verify(x => x.Send(It.IsAny<LoginUserCommand>(), It.IsAny<CancellationToken>()), Times.Once);
    }

    [Fact]
    public async Task Login_WithLockedAccount_ShouldReturnLockedStatus()
    {
        // Arrange
        var command = new LoginUserCommand
        {
            UsernameOrEmail = "<EMAIL>",
            Password = "password123"
        };

        var lockoutEnd = DateTime.UtcNow.AddMinutes(30);
        var expectedResult = LoginUserResult.AccountLocked(lockoutEnd);

        MockMediator.Setup(x => x.Send(It.IsAny<LoginUserCommand>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(expectedResult);

        // Act
        var result = await _controller.Login(command);

        // Assert
        result.Should().NotBeNull();
        var statusResult = result.Result.Should().BeOfType<ObjectResult>().Subject;
        statusResult.StatusCode.Should().Be(423);

        var apiResponse = statusResult.Value.Should().BeOfType<ApiResponse<object>>().Subject;
        apiResponse.Success.Should().BeFalse();
        apiResponse.Message.Should().Contain("locked");

        MockMediator.Verify(x => x.Send(It.IsAny<LoginUserCommand>(), It.IsAny<CancellationToken>()), Times.Once);
    }

    [Fact]
    public async Task RefreshToken_WithValidToken_ShouldReturnNewTokens()
    {
        // Arrange
        var command = new RefreshTokenCommand
        {
            AccessToken = "expired-access-token",
            RefreshToken = "valid-refresh-token"
        };

        var expectedResult = RefreshTokenResult.Successful(
            "new-access-token",
            "new-refresh-token",
            DateTime.UtcNow.AddHours(1),
            new UserTokenInfo
            {
                Id = Guid.NewGuid(),
                Username = "testuser",
                Role = "User"
            }
        );

        MockMediator.Setup(x => x.Send(It.IsAny<RefreshTokenCommand>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(expectedResult);

        // Act
        var result = await _controller.RefreshToken(command);

        // Assert
        var response = AssertSuccessResult(result);
        response.Data.Should().NotBeNull();
        response.Data!.Success.Should().BeTrue();
        response.Data.AccessToken.Should().Be("new-access-token");
        response.Data.RefreshToken.Should().Be("new-refresh-token");

        MockMediator.Verify(x => x.Send(It.IsAny<RefreshTokenCommand>(), It.IsAny<CancellationToken>()), Times.Once);
    }

    [Fact]
    public async Task RefreshToken_WithInvalidToken_ShouldReturnUnauthorized()
    {
        // Arrange
        var command = new RefreshTokenCommand
        {
            AccessToken = "expired-access-token",
            RefreshToken = "invalid-refresh-token"
        };

        var expectedResult = RefreshTokenResult.Failed("Invalid refresh token");

        MockMediator.Setup(x => x.Send(It.IsAny<RefreshTokenCommand>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(expectedResult);

        // Act
        var result = await _controller.RefreshToken(command);

        // Assert
        var response = AssertUnauthorizedResult(result);
        response.Message.Should().Contain("Token refresh failed");

        MockMediator.Verify(x => x.Send(It.IsAny<RefreshTokenCommand>(), It.IsAny<CancellationToken>()), Times.Once);
    }

    [Fact]
    public async Task GetProfile_WithAuthenticatedUser_ShouldReturnProfile()
    {
        // Arrange
        var userId = Guid.NewGuid();
        var authenticatedController = CreateAuthenticatedController<AuthController>(userId, "testuser", "User");

        var expectedResult = GetUserProfileResult.Success(new FleetXQ.Application.Features.Authentication.DTOs.UserProfileDto
        {
            Id = userId,
            Username = "testuser",
            Email = "<EMAIL>",
            FullName = "Test User",
            Role = FleetXQ.Domain.Enums.UserRole.User,
            IsActive = true,
            IsEmailConfirmed = true,
            CreatedAt = DateTime.UtcNow.AddDays(-30),
            UpdatedAt = DateTime.UtcNow
        });

        MockMediator.Setup(x => x.Send(It.IsAny<GetUserProfileQuery>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(expectedResult);

        // Act
        var result = await authenticatedController.GetProfile();

        // Assert
        var response = AssertSuccessResult(result);
        response.Data.Should().NotBeNull();
        response.Data!.Success.Should().BeTrue();
        response.Data.UserProfile.Should().NotBeNull();
        response.Data.UserProfile!.Id.Should().Be(userId);

        MockMediator.Verify(x => x.Send(It.Is<GetUserProfileQuery>(q => 
            q.UserId == userId && q.RequestingUserId == userId && q.IncludeSensitiveInfo == true), 
            It.IsAny<CancellationToken>()), Times.Once);
    }

    [Fact]
    public async Task GetProfile_WithoutAuthentication_ShouldReturnUnauthorized()
    {
        // Arrange
        var unauthenticatedController = CreateController<AuthController>();

        // Act
        var result = await unauthenticatedController.GetProfile();

        // Assert
        var response = AssertUnauthorizedResult(result);
        response.Message.Should().Contain("User ID not found in token");

        MockMediator.Verify(x => x.Send(It.IsAny<GetUserProfileQuery>(), It.IsAny<CancellationToken>()), Times.Never);
    }

    [Fact]
    public async Task GetUserProfile_WithAdminRole_ShouldReturnOtherUserProfile()
    {
        // Arrange
        var adminUserId = Guid.NewGuid();
        var targetUserId = Guid.NewGuid();
        var adminController = CreateAuthenticatedController<AuthController>(adminUserId, "admin", "Admin");

        var expectedResult = GetUserProfileResult.Success(new FleetXQ.Application.Features.Authentication.DTOs.UserProfileDto
        {
            Id = targetUserId,
            Username = "targetuser",
            Email = "<EMAIL>",
            FullName = "Target User",
            Role = FleetXQ.Domain.Enums.UserRole.User,
            IsActive = true,
            IsEmailConfirmed = true,
            CreatedAt = DateTime.UtcNow.AddDays(-15),
            UpdatedAt = DateTime.UtcNow
        });

        MockMediator.Setup(x => x.Send(It.IsAny<GetUserProfileQuery>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(expectedResult);

        // Act
        var result = await adminController.GetUserProfile(targetUserId);

        // Assert
        var response = AssertSuccessResult(result);
        response.Data.Should().NotBeNull();
        response.Data!.Success.Should().BeTrue();
        response.Data.UserProfile.Should().NotBeNull();
        response.Data.UserProfile!.Id.Should().Be(targetUserId);

        MockMediator.Verify(x => x.Send(It.Is<GetUserProfileQuery>(q => 
            q.UserId == targetUserId && q.RequestingUserId == adminUserId && q.IncludeSensitiveInfo == true), 
            It.IsAny<CancellationToken>()), Times.Once);
    }

    [Fact]
    public void Logout_ShouldReturnSuccessMessage()
    {
        // Arrange
        var authenticatedController = CreateAuthenticatedController<AuthController>(Guid.NewGuid(), "testuser", "User");

        // Act
        var result = authenticatedController.Logout();

        // Assert
        var response = AssertSuccessResult(result);
        response.Message.Should().Be("Logout successful");
    }
}
