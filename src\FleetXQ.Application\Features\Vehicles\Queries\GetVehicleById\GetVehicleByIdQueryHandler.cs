using AutoMapper;
using FleetXQ.Application.Features.Vehicles.DTOs;
using FleetXQ.Domain.Interfaces;
using MediatR;
using Microsoft.Extensions.Logging;

namespace FleetXQ.Application.Features.Vehicles.Queries.GetVehicleById;

/// <summary>
/// Handler for GetVehicleByIdQuery
/// </summary>
public sealed class GetVehicleByIdQueryHandler : IRequestHandler<GetVehicleByIdQuery, GetVehicleByIdResult>
{
    private readonly IVehicleRepository _vehicleRepository;
    private readonly IMapper _mapper;
    private readonly ILogger<GetVehicleByIdQueryHandler> _logger;

    /// <summary>
    /// Initializes a new instance of the <see cref="GetVehicleByIdQueryHandler"/> class
    /// </summary>
    /// <param name="vehicleRepository">The vehicle repository</param>
    /// <param name="mapper">The AutoMapper instance</param>
    /// <param name="logger">The logger</param>
    public GetVehicleByIdQueryHandler(
        IVehicleRepository vehicleRepository,
        IMapper mapper,
        ILogger<GetVehicleByIdQueryHandler> logger)
    {
        _vehicleRepository = vehicleRepository ?? throw new ArgumentNullException(nameof(vehicleRepository));
        _mapper = mapper ?? throw new ArgumentNullException(nameof(mapper));
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
    }

    /// <summary>
    /// Handles the GetVehicleByIdQuery
    /// </summary>
    /// <param name="request">The query request</param>
    /// <param name="cancellationToken">The cancellation token</param>
    /// <returns>The query result</returns>
    public async Task<GetVehicleByIdResult> Handle(GetVehicleByIdQuery request, CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogInformation("Getting vehicle by ID: {VehicleId}", request.VehicleId);

            var vehicle = await _vehicleRepository.GetByIdAsync(request.VehicleId, cancellationToken);
            
            if (vehicle == null)
            {
                _logger.LogInformation("Vehicle with ID {VehicleId} not found", request.VehicleId);
                return GetVehicleByIdResult.NotFound();
            }

            var vehicleDto = _mapper.Map<VehicleDto>(vehicle);
            
            _logger.LogInformation("Successfully retrieved vehicle {VehicleId}", request.VehicleId);
            
            return GetVehicleByIdResult.Success(vehicleDto);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting vehicle by ID: {VehicleId}", request.VehicleId);
            return GetVehicleByIdResult.Failed($"An error occurred while retrieving the vehicle: {ex.Message}");
        }
    }
}
