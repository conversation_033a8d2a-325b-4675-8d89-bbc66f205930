using FleetXQ.Domain.Enums;

namespace FleetXQ.Application.Features.Vehicles.DTOs;

/// <summary>
/// Data transfer object for vehicle list information (summary view)
/// </summary>
public class VehicleListDto
{
    /// <summary>
    /// Gets or sets the vehicle identifier
    /// </summary>
    public Guid Id { get; set; }

    /// <summary>
    /// Gets or sets the vehicle name
    /// </summary>
    public string VehicleName { get; set; } = string.Empty;

    /// <summary>
    /// Gets or sets the license plate number
    /// </summary>
    public string LicensePlate { get; set; } = string.Empty;

    /// <summary>
    /// Gets or sets the vehicle type
    /// </summary>
    public string VehicleType { get; set; } = string.Empty;

    /// <summary>
    /// Gets or sets the vehicle brand
    /// </summary>
    public string? Brand { get; set; }

    /// <summary>
    /// Gets or sets the vehicle model
    /// </summary>
    public string? Model { get; set; }

    /// <summary>
    /// Gets or sets the vehicle status
    /// </summary>
    public VehicleStatus Status { get; set; }

    /// <summary>
    /// Gets or sets the current fuel level percentage
    /// </summary>
    public decimal? CurrentFuelLevelPercentage { get; set; }

    /// <summary>
    /// Gets or sets the current location
    /// </summary>
    public LocationDto? CurrentLocation { get; set; }

    /// <summary>
    /// Gets or sets a value indicating whether the vehicle is available
    /// </summary>
    public bool IsAvailable { get; set; }

    /// <summary>
    /// Gets or sets a value indicating whether the vehicle is moving
    /// </summary>
    public bool IsMoving { get; set; }

    /// <summary>
    /// Gets or sets a value indicating whether the vehicle needs maintenance
    /// </summary>
    public bool NeedsMaintenance { get; set; }

    /// <summary>
    /// Gets or sets the last telemetry update timestamp
    /// </summary>
    public DateTime? LastTelemetryUpdate { get; set; }
}
