-- =============================================
-- Fleet XQ Enhanced Database Setup for Technical Assessment
-- Optimized for .NET Core + Entity Framework + AI-Augmented Development
-- Execution Time: <2 minutes | Enhanced for Real-time Features & Analytics
-- =============================================

-- Drop existing database if it exists
IF EXISTS (SELECT name FROM sys.databases WHERE name = 'FleetXQ_Assessment')
BEGIN
    ALTER DATABASE FleetXQ_Assessment SET SINGLE_USER WITH ROLLBACK IMMEDIATE;
    DROP DATABASE FleetXQ_Assessment;
END
GO

-- Create database with enhanced settings
CREATE DATABASE FleetXQ_Assessment;
GO

USE FleetXQ_Assessment;
GO

-- =============================================
-- ENHANCED TABLE STRUCTURES FOR MODERN FLEET MANAGEMENT
-- =============================================

-- 1. USER MANAGEMENT & SECURITY (Added for Assessment)
CREATE TABLE Users (
    UserId INT IDENTITY(1,1) PRIMARY KEY,
    Username NVARCHAR(50) NOT NULL UNIQUE,
    Email NVARCHAR(100) NOT NULL UNIQUE,
    PasswordHash NVARCHAR(255) NOT NULL,
    FirstName NVARCHAR(50) NOT NULL,
    LastName NVARCHAR(50) NOT NULL,
    Role NVARCHAR(20) NOT NULL DEFAULT 'User', -- 'Admin', 'Manager', 'Driver', 'User'
    IsActive BIT DEFAULT 1,
    CreatedDate DATETIME2 DEFAULT GETDATE(),
    LastLoginDate DATETIME2,
    
    INDEX IX_Users_Email (Email),
    INDEX IX_Users_Username (Username),
    INDEX IX_Users_Role (Role, IsActive)
);

-- 2. ENHANCED VEHICLES TABLE
CREATE TABLE Vehicles (
    VehicleId INT IDENTITY(1,1) PRIMARY KEY,
    VehicleName NVARCHAR(50) NOT NULL,
    LicensePlate NVARCHAR(20) NOT NULL UNIQUE,
    VIN NVARCHAR(17) UNIQUE, -- Vehicle Identification Number
    VehicleType NVARCHAR(30) NOT NULL, -- 'Truck', 'Van', 'Car', 'Motorcycle', 'Electric'
    Brand NVARCHAR(30),
    Model NVARCHAR(50),
    Year INT,
    Color NVARCHAR(20),
    FuelTankCapacity DECIMAL(8,2), -- in liters
    FuelType NVARCHAR(20) DEFAULT 'Gasoline', -- 'Gasoline', 'Diesel', 'Electric', 'Hybrid'
    Status NVARCHAR(20) NOT NULL DEFAULT 'active', -- 'active', 'maintenance', 'offline', 'retired'
    CreatedDate DATETIME2 DEFAULT GETDATE(),
    LastMaintenanceDate DATETIME2,
    NextMaintenanceDate DATETIME2,
    PurchaseDate DATETIME2,
    PurchasePrice DECIMAL(12,2),
    CurrentMileage DECIMAL(12,2),
    
    -- Enhanced Indexing for Performance
    INDEX IX_Vehicles_Status (Status),
    INDEX IX_Vehicles_Type (VehicleType),
    INDEX IX_Vehicles_LicensePlate (LicensePlate),
    INDEX IX_Vehicles_VIN (VIN),
    INDEX IX_Vehicles_Status_Type (Status, VehicleType)
);

-- 3. ENHANCED DRIVERS TABLE
CREATE TABLE Drivers (
    DriverId INT IDENTITY(1,1) PRIMARY KEY,
    UserId INT, -- Link to Users table
    EmployeeId NVARCHAR(20) UNIQUE,
    FirstName NVARCHAR(50) NOT NULL,
    LastName NVARCHAR(50) NOT NULL,
    Email NVARCHAR(100) UNIQUE,
    Phone NVARCHAR(20),
    LicenseNumber NVARCHAR(30) UNIQUE,
    LicenseClass NVARCHAR(10), -- 'A', 'B', 'C', 'CDL'
    LicenseExpiryDate DATETIME2,
    HireDate DATETIME2,
    DateOfBirth DATETIME2,
    Address NVARCHAR(200),
    EmergencyContact NVARCHAR(100),
    EmergencyPhone NVARCHAR(20),
    Status NVARCHAR(20) DEFAULT 'active', -- 'active', 'inactive', 'on_leave', 'terminated'
    
    FOREIGN KEY (UserId) REFERENCES Users(UserId),
    INDEX IX_Drivers_Status (Status),
    INDEX IX_Drivers_Email (Email),
    INDEX IX_Drivers_EmployeeId (EmployeeId),
    INDEX IX_Drivers_LicenseExpiry (LicenseExpiryDate)
);

-- 4. VEHICLE ASSIGNMENTS TABLE (Enhanced)
CREATE TABLE VehicleAssignments (
    AssignmentId INT IDENTITY(1,1) PRIMARY KEY,
    VehicleId INT NOT NULL,
    DriverId INT NOT NULL,
    AssignedDate DATETIME2 DEFAULT GETDATE(),
    UnassignedDate DATETIME2 NULL,
    AssignmentType NVARCHAR(20) DEFAULT 'Primary', -- 'Primary', 'Secondary', 'Temporary'
    IsActive BIT DEFAULT 1,
    Notes NVARCHAR(500),
    
    FOREIGN KEY (VehicleId) REFERENCES Vehicles(VehicleId),
    FOREIGN KEY (DriverId) REFERENCES Drivers(DriverId),
    INDEX IX_VehicleAssignments_Active (IsActive, VehicleId),
    INDEX IX_VehicleAssignments_Driver (DriverId, IsActive),
    INDEX IX_VehicleAssignments_Vehicle_Driver (VehicleId, DriverId)
);

-- 5. ENHANCED TELEMETRY DATA TABLE (Optimized for Real-time)
CREATE TABLE TelemetryData (
    TelemetryId BIGINT IDENTITY(1,1) PRIMARY KEY,
    VehicleId INT NOT NULL,
    Timestamp DATETIME2 NOT NULL,
    Latitude DECIMAL(10,8),
    Longitude DECIMAL(11,8),
    Speed DECIMAL(5,2), -- km/h
    Heading DECIMAL(5,2), -- degrees (0-360)
    FuelLevel DECIMAL(5,2), -- percentage (0-100)
    FuelConsumption DECIMAL(8,3), -- liters per 100km
    Odometer DECIMAL(12,2), -- total kilometers
    TripDistance DECIMAL(8,2), -- current trip distance
    EngineStatus NVARCHAR(20), -- 'running', 'idle', 'off'
    EngineRPM INT,
    Temperature DECIMAL(5,2), -- engine temperature in Celsius
    BatteryVoltage DECIMAL(4,2),
    Acceleration DECIMAL(6,3), -- m/s²
    HarshBraking BIT DEFAULT 0,
    HarshAcceleration BIT DEFAULT 0,
    IdleTime INT DEFAULT 0, -- seconds
    IsMoving BIT DEFAULT 0,
    
    FOREIGN KEY (VehicleId) REFERENCES Vehicles(VehicleId),
    
    -- Optimized Indexing for Real-time Queries
    INDEX IX_TelemetryData_Vehicle_Time (VehicleId, Timestamp),
    INDEX IX_TelemetryData_Timestamp (Timestamp),
    INDEX IX_TelemetryData_Vehicle_Latest (VehicleId, Timestamp DESC),
    INDEX IX_TelemetryData_Speed (Speed),
    INDEX IX_TelemetryData_FuelLevel (FuelLevel),
    INDEX IX_TelemetryData_Moving (IsMoving, Timestamp)
);

-- 6. ENHANCED MAINTENANCE RECORDS
CREATE TABLE MaintenanceRecords (
    MaintenanceId INT IDENTITY(1,1) PRIMARY KEY,
    VehicleId INT NOT NULL,
    MaintenanceType NVARCHAR(50), -- 'oil_change', 'tire_replacement', 'brake_service', 'general_inspection', 'repair'
    Category NVARCHAR(30), -- 'Preventive', 'Repair', 'Emergency'
    Description NVARCHAR(500),
    Cost DECIMAL(10,2),
    LaborCost DECIMAL(10,2),
    PartsCost DECIMAL(10,2),
    MaintenanceDate DATETIME2,
    CompletedDate DATETIME2,
    NextMaintenanceDate DATETIME2,
    NextMaintenanceMileage DECIMAL(12,2),
    ServiceProvider NVARCHAR(100),
    TechnicianName NVARCHAR(100),
    WarrantyExpiry DATETIME2,
    Status NVARCHAR(20) DEFAULT 'Completed', -- 'Scheduled', 'In Progress', 'Completed', 'Cancelled'
    
    FOREIGN KEY (VehicleId) REFERENCES Vehicles(VehicleId),
    INDEX IX_MaintenanceRecords_Vehicle (VehicleId),
    INDEX IX_MaintenanceRecords_Date (MaintenanceDate),
    INDEX IX_MaintenanceRecords_Status (Status),
    INDEX IX_MaintenanceRecords_NextDue (NextMaintenanceDate)
);

-- 7. ENHANCED FUEL TRANSACTIONS
CREATE TABLE FuelTransactions (
    TransactionId INT IDENTITY(1,1) PRIMARY KEY,
    VehicleId INT NOT NULL,
    DriverId INT,
    TransactionDate DATETIME2,
    LitersRefueled DECIMAL(8,2),
    CostPerLiter DECIMAL(6,3),
    TotalCost DECIMAL(10,2),
    FuelStation NVARCHAR(100),
    FuelStationAddress NVARCHAR(200),
    OdometerReading DECIMAL(12,2),
    FuelType NVARCHAR(20),
    PaymentMethod NVARCHAR(20), -- 'Card', 'Cash', 'Account'
    CardNumber NVARCHAR(20), -- Last 4 digits only
    
    FOREIGN KEY (VehicleId) REFERENCES Vehicles(VehicleId),
    FOREIGN KEY (DriverId) REFERENCES Drivers(DriverId),
    INDEX IX_FuelTransactions_Vehicle_Date (VehicleId, TransactionDate),
    INDEX IX_FuelTransactions_Date (TransactionDate),
    INDEX IX_FuelTransactions_Driver (DriverId)
);

-- 8. TRIPS TABLE (New - For Route Analytics)
CREATE TABLE Trips (
    TripId BIGINT IDENTITY(1,1) PRIMARY KEY,
    VehicleId INT NOT NULL,
    DriverId INT,
    StartTime DATETIME2 NOT NULL,
    EndTime DATETIME2,
    StartLatitude DECIMAL(10,8),
    StartLongitude DECIMAL(11,8),
    EndLatitude DECIMAL(10,8),
    EndLongitude DECIMAL(11,8),
    StartAddress NVARCHAR(200),
    EndAddress NVARCHAR(200),
    TotalDistance DECIMAL(10,2),
    TotalDuration INT, -- minutes
    MaxSpeed DECIMAL(5,2),
    AvgSpeed DECIMAL(5,2),
    FuelConsumed DECIMAL(8,2),
    IdleTime INT, -- minutes
    HarshEvents INT DEFAULT 0,
    Status NVARCHAR(20) DEFAULT 'Active', -- 'Active', 'Completed', 'Cancelled'
    
    FOREIGN KEY (VehicleId) REFERENCES Vehicles(VehicleId),
    FOREIGN KEY (DriverId) REFERENCES Drivers(DriverId),
    INDEX IX_Trips_Vehicle (VehicleId),
    INDEX IX_Trips_Driver (DriverId),
    INDEX IX_Trips_StartTime (StartTime),
    INDEX IX_Trips_Status (Status)
);

-- 9. ALERTS/NOTIFICATIONS TABLE (New - For Real-time Monitoring)
CREATE TABLE Alerts (
    AlertId BIGINT IDENTITY(1,1) PRIMARY KEY,
    VehicleId INT,
    DriverId INT,
    AlertType NVARCHAR(50), -- 'Speed', 'Fuel', 'Maintenance', 'Geofence', 'Harsh Driving'
    Severity NVARCHAR(20), -- 'Low', 'Medium', 'High', 'Critical'
    Message NVARCHAR(500),
    CreatedDate DATETIME2 DEFAULT GETDATE(),
    AcknowledgedDate DATETIME2,
    AcknowledgedBy INT, -- UserId
    Status NVARCHAR(20) DEFAULT 'Active', -- 'Active', 'Acknowledged', 'Resolved'
    Latitude DECIMAL(10,8),
    Longitude DECIMAL(11,8),
    
    FOREIGN KEY (VehicleId) REFERENCES Vehicles(VehicleId),
    FOREIGN KEY (DriverId) REFERENCES Drivers(DriverId),
    FOREIGN KEY (AcknowledgedBy) REFERENCES Users(UserId),
    INDEX IX_Alerts_Vehicle (VehicleId),
    INDEX IX_Alerts_Status (Status),
    INDEX IX_Alerts_CreatedDate (CreatedDate),
    INDEX IX_Alerts_Type_Severity (AlertType, Severity)
);

-- 10. GEOFENCES TABLE (New - For Location-based Alerts)
CREATE TABLE Geofences (
    GeofenceId INT IDENTITY(1,1) PRIMARY KEY,
    Name NVARCHAR(100) NOT NULL,
    Description NVARCHAR(500),
    CenterLatitude DECIMAL(10,8),
    CenterLongitude DECIMAL(11,8),
    Radius DECIMAL(8,2), -- meters
    GeofenceType NVARCHAR(20), -- 'Circular', 'Polygon'
    IsActive BIT DEFAULT 1,
    CreatedDate DATETIME2 DEFAULT GETDATE(),
    
    INDEX IX_Geofences_Active (IsActive),
    INDEX IX_Geofences_Location (CenterLatitude, CenterLongitude)
);

-- =============================================
-- SAMPLE DATA GENERATION (Enhanced for Assessment)
-- =============================================

PRINT 'Generating enhanced sample data for Fleet XQ Assessment...';

-- Insert sample users (for authentication testing)
INSERT INTO Users (Username, Email, PasswordHash, FirstName, LastName, Role) VALUES
('admin', '<EMAIL>', 'hashed_password_admin', 'System', 'Administrator', 'Admin'),
('manager1', '<EMAIL>', 'hashed_password_mgr', 'Fleet', 'Manager', 'Manager'),
('john.doe', '<EMAIL>', 'hashed_password_123', 'John', 'Doe', 'Driver'),
('jane.smith', '<EMAIL>', 'hashed_password_456', 'Jane', 'Smith', 'Driver'),
('mike.johnson', '<EMAIL>', 'hashed_password_789', 'Mike', 'Johnson', 'Driver'),
('sarah.wilson', '<EMAIL>', 'hashed_password_101', 'Sarah', 'Wilson', 'Driver'),
('analyst1', '<EMAIL>', 'hashed_password_ana', 'Data', 'Analyst', 'User');

-- Insert enhanced drivers with more realistic data
INSERT INTO Drivers (UserId, EmployeeId, FirstName, LastName, Email, Phone, LicenseNumber, LicenseClass, LicenseExpiryDate, HireDate, DateOfBirth, Address, EmergencyContact, EmergencyPhone, Status) VALUES
(3, 'EMP001', 'John', 'Doe', '<EMAIL>', '******-0101', 'DL001234', 'B', '2026-01-15', '2023-01-15', '1985-03-20', '123 Main St, Cityville', 'Mary Doe', '******-0201', 'active'),
(4, 'EMP002', 'Jane', 'Smith', '<EMAIL>', '******-0102', 'DL001235', 'B', '2025-12-20', '2023-02-20', '1988-07-15', '456 Oak Ave, Townsburg', 'Bob Smith', '******-0202', 'active'),
(5, 'EMP003', 'Mike', 'Johnson', '<EMAIL>', '******-0103', 'DL001236', 'A', '2026-03-10', '2023-03-10', '1982-11-30', '789 Pine Rd, Villageton', 'Lisa Johnson', '******-0203', 'active'),
(6, 'EMP004', 'Sarah', 'Wilson', '<EMAIL>', '******-0104', 'DL001237', 'B', '2025-11-05', '2023-04-05', '1990-01-25', '321 Elm St, Hamletville', 'Tom Wilson', '******-0204', 'active'),
(NULL, 'EMP005', 'Bob', 'Brown', '<EMAIL>', '******-0105', 'DL001238', 'B', '2026-05-12', '2023-05-12', '1987-09-10', '654 Maple Dr, Countryside', 'Alice Brown', '******-0205', 'on_leave'),
(NULL, 'EMP006', 'Alice', 'Davis', '<EMAIL>', '******-0106', 'DL001239', 'C', '2025-10-01', '2023-06-01', '1992-04-18', '987 Cedar Ln, Suburbia', 'Charlie Davis', '******-0206', 'active'),
(NULL, 'EMP007', 'Tom', 'Miller', '<EMAIL>', '******-0107', 'DL001240', 'A', '2026-07-15', '2023-07-15', '1984-12-05', '147 Birch St, Metro City', 'Nancy Miller', '******-0207', 'active'),
(NULL, 'EMP008', 'Lisa', 'Garcia', '<EMAIL>', '******-0108', 'DL001241', 'B', '2025-09-20', '2023-08-20', '1989-06-12', '258 Spruce Ave, Urbandale', 'Carlos Garcia', '******-0208', 'active');

-- Insert enhanced vehicles with more variety
INSERT INTO Vehicles (VehicleName, LicensePlate, VIN, VehicleType, Brand, Model, Year, Color, FuelTankCapacity, FuelType, Status, LastMaintenanceDate, NextMaintenanceDate, PurchaseDate, PurchasePrice, CurrentMileage) VALUES
('Truck-001', 'FLT-001', '1HGBH41JXMN109186', 'Truck', 'Ford', 'F-150', 2022, 'White', 98.0, 'Gasoline', 'active', '2025-05-15', '2025-08-15', '2022-01-15', 45000.00, 35000.5),
('Van-002', 'FLT-002', '2T1BURHE0FC123456', 'Van', 'Mercedes', 'Sprinter', 2021, 'Silver', 75.0, 'Diesel', 'active', '2025-05-20', '2025-08-20', '2021-03-20', 55000.00, 42000.2),
('Car-003', 'FLT-003', '5NPE34AF6GH123789', 'Car', 'Toyota', 'Camry', 2023, 'Blue', 60.0, 'Hybrid', 'maintenance', '2025-04-10', '2025-07-10', '2023-02-10', 32000.00, 15000.8),
('Truck-004', 'FLT-004', '1GC1KVEG8FF123456', 'Truck', 'Chevrolet', 'Silverado', 2022, 'Black', 98.0, 'Gasoline', 'active', '2025-06-01', '2025-09-01', '2022-04-01', 48000.00, 28000.3),
('Van-005', 'FLT-005', '1FTBW2CM8GKA12345', 'Van', 'Ford', 'Transit', 2020, 'Red', 70.0, 'Gasoline', 'active', '2025-05-25', '2025-08-25', '2020-06-25', 38000.00, 65000.7),
('Car-006', 'FLT-006', '2HGFC2F59GH123456', 'Car', 'Honda', 'Civic', 2023, 'Gray', 50.0, 'Gasoline', 'offline', '2025-03-15', '2025-06-15', '2023-01-15', 28000.00, 12000.1),
('Motorcycle-007', 'FLT-007', '1HD1KB4197Y123456', 'Motorcycle', 'Harley-Davidson', 'Street 750', 2021, 'Orange', 13.0, 'Gasoline', 'active', '2025-06-05', '2025-09-05', '2021-05-05', 12000.00, 8000.5),
('Truck-008', 'FLT-008', '1C6SRFFT4GN123456', 'Truck', 'Ram', '1500', 2022, 'Green', 95.0, 'Gasoline', 'active', '2025-05-30', '2025-08-30', '2022-03-30', 52000.00, 31000.9),
('Van-009', 'FLT-009', 'JN6AR06W41W123456', 'Van', 'Nissan', 'NV200', 2021, 'Yellow', 55.0, 'Gasoline', 'maintenance', '2025-04-20', '2025-07-20', '2021-02-20', 35000.00, 45000.4),
('Electric-010', 'FLT-010', '5YJ3E1EA4GF123456', 'Electric', 'Tesla', 'Model 3', 2023, 'White', 0.0, 'Electric', 'active', '2025-06-10', '2025-09-10', '2023-03-10', 45000.00, 8000.2);

-- Enhanced vehicle assignments
INSERT INTO VehicleAssignments (VehicleId, DriverId, AssignedDate, AssignmentType, IsActive) VALUES
(1, 1, '2025-01-01', 'Primary', 1),
(2, 2, '2025-01-01', 'Primary', 1),
(3, 3, '2025-01-01', 'Primary', 1),
(4, 4, '2025-01-01', 'Primary', 1),
(5, 6, '2025-01-01', 'Primary', 1),
(6, 7, '2025-01-01', 'Primary', 1),
(7, 8, '2025-01-01', 'Primary', 1),
(8, 1, '2025-02-01', 'Secondary', 1),
(9, 2, '2025-02-01', 'Secondary', 1),
(10, 3, '2025-02-01', 'Primary', 1);

-- Insert sample geofences
INSERT INTO Geofences (Name, Description, CenterLatitude, CenterLongitude, Radius, GeofenceType, IsActive) VALUES
('Main Depot', 'Primary vehicle depot and maintenance facility', 40.7128, -74.0060, 500, 'Circular', 1),
('Customer Zone A', 'High-priority customer delivery area', 40.7589, -73.9851, 1000, 'Circular', 1),
('Service Center', 'Authorized maintenance and repair center', 40.6892, -74.0445, 300, 'Circular', 1),
('Restricted Area', 'No-entry zone for security reasons', 40.7831, -73.9712, 200, 'Circular', 1);

-- =============================================
-- ENHANCED TELEMETRY DATA GENERATION
-- Optimized for Real-time Analytics and AI Training
-- =============================================

PRINT 'Generating enhanced telemetry data for AI analytics...';

DECLARE @StartDate DATETIME2 = '2025-06-01';
DECLARE @EndDate DATETIME2 = GETDATE();
DECLARE @VehicleCount INT = 10;

-- Generate realistic telemetry data with enhanced patterns
WITH DateHours AS (
    SELECT DATEADD(MINUTE, n.n * 5, @StartDate) as TimeSlot
    FROM (
        SELECT ROW_NUMBER() OVER (ORDER BY (SELECT NULL)) - 1 as n
        FROM sys.objects s1 CROSS JOIN sys.objects s2
        WHERE ROW_NUMBER() OVER (ORDER BY (SELECT NULL)) <= DATEDIFF(MINUTE, @StartDate, @EndDate) / 5
    ) n
),
VehicleTimeSlots AS (
    SELECT 
        v.VehicleId,
        dh.TimeSlot,
        -- Generate realistic patterns based on vehicle type and time
        CASE v.VehicleType
            WHEN 'Truck' THEN 40.7128 + (v.VehicleId * 0.01 - 0.05) + SIN(DATEPART(HOUR, dh.TimeSlot) * PI() / 12) * 0.005
            WHEN 'Electric' THEN 40.7128 + (v.VehicleId * 0.005) + COS(DATEPART(HOUR, dh.TimeSlot) * PI() / 8) * 0.003
            ELSE 40.7128 + (v.VehicleId * 0.008 - 0.04) + SIN(DATEPART(HOUR, dh.TimeSlot) * PI() / 10) * 0.004
        END as Latitude,
        CASE v.VehicleType
            WHEN 'Truck' THEN -74.0060 + (v.VehicleId * 0.01 - 0.05) + COS(DATEPART(HOUR, dh.TimeSlot) * PI() / 12) * 0.005
            WHEN 'Electric' THEN -74.0060 + (v.VehicleId * 0.005) + SIN(DATEPART(HOUR, dh.TimeSlot) * PI() / 8) * 0.003
            ELSE -74.0060 + (v.VehicleId * 0.008 - 0.04) + COS(DATEPART(HOUR, dh.TimeSlot) * PI() / 10) * 0.004
        END as Longitude,
        v.VehicleType,
        v.FuelType
    FROM Vehicles v
    CROSS JOIN DateHours dh
    WHERE v.VehicleId <= @VehicleCount
),
EnhancedTelemetry AS (
    SELECT 
        vts.VehicleId,
        vts.TimeSlot as Timestamp,
        vts.Latitude,
        vts.Longitude,
        -- Realistic speed patterns based on time and vehicle type
        CASE 
            WHEN DATEPART(HOUR, vts.TimeSlot) BETWEEN 7 AND 9 OR DATEPART(HOUR, vts.TimeSlot) BETWEEN 17 AND 19
            THEN CASE vts.VehicleType
                WHEN 'Truck' THEN 15 + (ABS(CHECKSUM(NEWID())) % 25) -- Rush hour: 15-40 km/h
                WHEN 'Motorcycle' THEN 25 + (ABS(CHECKSUM(NEWID())) % 35) -- 25-60 km/h
                ELSE 20 + (ABS(CHECKSUM(NEWID())) % 30) -- 20-50 km/h
            END
            WHEN DATEPART(HOUR, vts.TimeSlot) BETWEEN 10 AND 16
            THEN CASE vts.VehicleType
                WHEN 'Truck' THEN 35 + (ABS(CHECKSUM(NEWID())) % 25) -- Day: 35-60 km/h
                WHEN 'Motorcycle' THEN 45 + (ABS(CHECKSUM(NEWID())) % 35) -- 45-80 km/h
                ELSE 40 + (ABS(CHECKSUM(NEWID())) % 30) -- 40-70 km/h
            END
            WHEN DATEPART(HOUR, vts.TimeSlot) BETWEEN 22 AND 6
            THEN (ABS(CHECKSUM(NEWID())) % 10) -- Night: 0-10 km/h
            ELSE 25 + (ABS(CHECKSUM(NEWID())) % 25) -- Other: 25-50 km/h
        END as Speed,
        -- Realistic heading (direction)
        (ABS(CHECKSUM(NEWID())) % 360) as Heading,
        -- Smart fuel level simulation
        CASE vts.FuelType
            WHEN 'Electric' THEN 
                CASE WHEN (ABS(CHECKSUM(NEWID())) % 100) < 3 THEN 85 + (ABS(CHECKSUM(NEWID())) % 15) -- 3% charge events
                ELSE GREATEST(75 - (ABS(CHECKSUM(NEWID())) % 5), 15) END
            ELSE
                CASE WHEN (ABS(CHECKSUM(NEWID())) % 100) < 5 THEN 85 + (ABS(CHECKSUM(NEWID())) % 15) -- 5% refuel events
                ELSE GREATEST(70 - (ABS(CHECKSUM(NEWID())) % 3), 10) END
        END as FuelLevel,
        -- Fuel consumption based on vehicle type and speed
        CASE vts.VehicleType
            WHEN 'Truck' THEN 12.0 + (ABS(CHECKSUM(NEWID())) % 80) * 0.1 -- 12.0-20.0 L/100km
            WHEN 'Electric' THEN 0 -- Electric vehicles
            WHEN 'Motorcycle' THEN 3.5 + (ABS(CHECKSUM(NEWID())) % 30) * 0.1 -- 3.5-6.5 L/100km
            ELSE 7.0 + (ABS(CHECKSUM(NEWID())) % 50) * 0.1 -- 7.0-12.0 L/100km
        END as FuelConsumption,
        -- Progressive odometer
        15000 + (vts.VehicleId * 5000) + (ABS(CHECKSUM(NEWID())) % 5) as Odometer,
        -- Trip distance (resets periodically)
        CASE WHEN DATEPART(HOUR, vts.TimeSlot) = 6 AND DATEPART(MINUTE, vts.TimeSlot) < 30
             THEN (ABS(CHECKSUM(NEWID())) % 10) * 0.1 -- Reset at start of day
             ELSE 5 + (ABS(CHECKSUM(NEWID())) % 200) * 0.1 END as TripDistance,
        -- Engine status based on speed and time
        CASE 
            WHEN DATEPART(HOUR, vts.TimeSlot) BETWEEN 22 AND 6 THEN 
                CASE WHEN (ABS(CHECKSUM(NEWID())) % 10) > 8 THEN 'running' ELSE 'off' END
            ELSE 
                CASE WHEN (ABS(CHECKSUM(NEWID())) % 10) > 2 THEN 'running' 
                     WHEN (ABS(CHECKSUM(NEWID())) % 10) > 1 THEN 'idle' 
                     ELSE 'off' END
        END as EngineStatus,
        -- Engine RPM based on speed
        CASE vts.VehicleType
            WHEN 'Electric' THEN NULL -- Electric vehicles don't have RPM
            ELSE 800 + (ABS(CHECKSUM(NEWID())) % 2000) -- 800-2800 RPM
        END as EngineRPM,
        -- Engine temperature
        CASE vts.VehicleType
            WHEN 'Electric' THEN 25 + (ABS(CHECKSUM(NEWID())) % 15) -- Battery temp: 25-40°C
            ELSE 70 + (ABS(CHECKSUM(NEWID())) % 25) -- Engine temp: 70-95°C
        END as Temperature,
        -- Battery voltage
        CASE vts.VehicleType
            WHEN 'Electric' THEN 350 + (ABS(CHECKSUM(NEWID())) % 50) -- High voltage: 350-400V
            ELSE 12.0 + (ABS(CHECKSUM(NEWID())) % 200) * 0.01 -- 12V system: 12.0-14.0V
        END as BatteryVoltage,
        -- Acceleration and harsh events
        (-2.0 + (ABS(CHECKSUM(NEWID())) % 400) * 0.01) as Acceleration, -- -2.0 to +2.0 m/s²
        CASE WHEN (ABS(CHECKSUM(NEWID())) % 1000) < 15 THEN 1 ELSE 0 END as HarshBraking, -- 1.5% chance
        CASE WHEN (ABS(CHECKSUM(NEWID())) % 1000) < 12 THEN 1 ELSE 0 END as HarshAcceleration, -- 1.2% chance
        -- Idle time in seconds
        CASE WHEN DATEPART(HOUR, vts.TimeSlot) BETWEEN 12 AND 13 -- Lunch break
             THEN 300 + (ABS(CHECKSUM(NEWID())) % 1800) -- 5-35 minutes
             ELSE (ABS(CHECKSUM(NEWID())) % 300) END as IdleTime, -- 0-5 minutes
        -- Is moving flag
        CASE WHEN (ABS(CHECKSUM(NEWID())) % 10) > 2 THEN 1 ELSE 0 END as IsMoving
    FROM VehicleTimeSlots vts
)
INSERT INTO TelemetryData (
    VehicleId, Timestamp, Latitude, Longitude, Speed, Heading, FuelLevel, FuelConsumption,
    Odometer, TripDistance, EngineStatus, EngineRPM, Temperature, BatteryVoltage,
    Acceleration, HarshBraking, HarshAcceleration, IdleTime, IsMoving
)
SELECT * FROM EnhancedTelemetry;

DECLARE @TelemetryRecordCount INT = @@ROWCOUNT;
PRINT 'Enhanced telemetry data generation completed!';
PRINT 'Total telemetry records generated: ' + CAST(@TelemetryRecordCount AS NVARCHAR(20));

-- Generate realistic trip data
INSERT INTO Trips (VehicleId, DriverId, StartTime, EndTime, StartLatitude, StartLongitude, EndLatitude, EndLongitude, 
                   StartAddress, EndAddress, TotalDistance, TotalDuration, MaxSpeed, AvgSpeed, FuelConsumed, IdleTime, HarshEvents, Status)
SELECT 
    v.VehicleId,
    va.DriverId,
    DATEADD(HOUR, 8 + (ABS(CHECKSUM(NEWID())) % 8), DATEADD(DAY, -1 * (ABS(CHECKSUM(NEWID())) % 30), GETDATE())) as StartTime,
    DATEADD(HOUR, 2 + (ABS(CHECKSUM(NEWID())) % 6), DATEADD(HOUR, 8 + (ABS(CHECKSUM(NEWID())) % 8), DATEADD(DAY, -1 * (ABS(CHECKSUM(NEWID())) % 30), GETDATE()))) as EndTime,
    40.7128 + (ABS(CHECKSUM(NEWID())) % 100) * 0.001 - 0.05 as StartLatitude,
    -74.0060 + (ABS(CHECKSUM(NEWID())) % 100) * 0.001 - 0.05 as StartLongitude,
    40.7128 + (ABS(CHECKSUM(NEWID())) % 100) * 0.001 - 0.05 as EndLatitude,
    -74.0060 + (ABS(CHECKSUM(NEWID())) % 100) * 0.001 - 0.05 as EndLongitude,
    'Depot Main Facility' as StartAddress,
    'Customer Site ' + CAST((ABS(CHECKSUM(NEWID())) % 50) + 1 AS NVARCHAR(10)) as EndAddress,
    25 + (ABS(CHECKSUM(NEWID())) % 150) as TotalDistance, -- 25-175 km
    60 + (ABS(CHECKSUM(NEWID())) % 240) as TotalDuration, -- 1-5 hours
    80 + (ABS(CHECKSUM(NEWID())) % 40) as MaxSpeed, -- 80-120 km/h
    45 + (ABS(CHECKSUM(NEWID())) % 25) as AvgSpeed, -- 45-70 km/h
    12.5 + (ABS(CHECKSUM(NEWID())) % 150) * 0.1 as FuelConsumed, -- Realistic fuel consumption
    5 + (ABS(CHECKSUM(NEWID())) % 25) as IdleTime, -- 5-30 minutes idle
    (ABS(CHECKSUM(NEWID())) % 5) as HarshEvents, -- 0-4 harsh events
    'Completed' as Status
FROM Vehicles v
INNER JOIN VehicleAssignments va ON v.VehicleId = va.VehicleId AND va.IsActive = 1
CROSS JOIN (SELECT TOP 5 ROW_NUMBER() OVER (ORDER BY (SELECT NULL)) as rn FROM sys.objects) r
WHERE v.VehicleId <= 10;

-- Generate sample alerts for real-time monitoring
INSERT INTO Alerts (VehicleId, DriverId, AlertType, Severity, Message, CreatedDate, Status, Latitude, Longitude)
SELECT 
    v.VehicleId,
    va.DriverId,
    CASE (ABS(CHECKSUM(NEWID())) % 5)
        WHEN 0 THEN 'Speed'
        WHEN 1 THEN 'Fuel'
        WHEN 2 THEN 'Maintenance'
        WHEN 3 THEN 'Harsh Driving'
        ELSE 'Geofence'
    END as AlertType,
    CASE (ABS(CHECKSUM(NEWID())) % 4)
        WHEN 0 THEN 'Low'
        WHEN 1 THEN 'Medium'
        WHEN 2 THEN 'High'
        ELSE 'Critical'
    END as Severity,
    'System generated alert for vehicle monitoring' as Message,
    DATEADD(MINUTE, -1 * (ABS(CHECKSUM(NEWID())) % 2880), GETDATE()) as CreatedDate, -- Last 48 hours
    CASE (ABS(CHECKSUM(NEWID())) % 3)
        WHEN 0 THEN 'Active'
        WHEN 1 THEN 'Acknowledged'
        ELSE 'Resolved'
    END as Status,
    40.7128 + (ABS(CHECKSUM(NEWID())) % 100) * 0.001 - 0.05 as Latitude,
    -74.0060 + (ABS(CHECKSUM(NEWID())) % 100) * 0.001 - 0.05 as Longitude
FROM Vehicles v
INNER JOIN VehicleAssignments va ON v.VehicleId = va.VehicleId AND va.IsActive = 1
CROSS JOIN (SELECT TOP 3 ROW_NUMBER() OVER (ORDER BY (SELECT NULL)) as rn FROM sys.objects) r
WHERE v.VehicleId <= 10;

-- Enhanced fuel transaction data
INSERT INTO FuelTransactions (VehicleId, DriverId, TransactionDate, LitersRefueled, CostPerLiter, TotalCost, FuelStation, FuelStationAddress, OdometerReading, FuelType, PaymentMethod, CardNumber)
SELECT 
    v.VehicleId,
    va.DriverId,
    DATEADD(DAY, (ABS(CHECKSUM(NEWID())) % 21), '2025-06-01') as TransactionDate,
    CASE v.FuelType
        WHEN 'Electric' THEN 0 -- Electric vehicles don't refuel
        ELSE 20 + (ABS(CHECKSUM(NEWID())) % 40)
    END as LitersRefueled,
    1.25 + (ABS(CHECKSUM(NEWID())) % 30) * 0.01 as CostPerLiter,
    0 as TotalCost, -- Will be calculated
    'FuelMart Station ' + CAST((ABS(CHECKSUM(NEWID())) % 25) + 1 AS NVARCHAR(10)) as FuelStation,
    CAST((ABS(CHECKSUM(NEWID())) % 999) + 1 AS NVARCHAR(10)) + ' Highway Blvd, Metro Area' as FuelStationAddress,
    15000 + (ABS(CHECKSUM(NEWID())) % 50000) as OdometerReading,
    v.FuelType,
    CASE (ABS(CHECKSUM(NEWID())) % 3)
        WHEN 0 THEN 'Card'
        WHEN 1 THEN 'Account'
        ELSE 'Cash'
    END as PaymentMethod,
    '****' + CAST(1000 + (ABS(CHECKSUM(NEWID())) % 9000) AS NVARCHAR(10)) as CardNumber
FROM Vehicles v
INNER JOIN VehicleAssignments va ON v.VehicleId = va.VehicleId AND va.IsActive = 1
CROSS JOIN (SELECT TOP 4 ROW_NUMBER() OVER (ORDER BY (SELECT NULL)) as rn FROM sys.objects) r
WHERE v.VehicleId <= 10 AND v.FuelType != 'Electric';

-- Update TotalCost for fuel transactions
UPDATE FuelTransactions 
SET TotalCost = ROUND(LitersRefueled * CostPerLiter, 2)
WHERE LitersRefueled > 0;

-- Enhanced maintenance records
INSERT INTO MaintenanceRecords (VehicleId, MaintenanceType, Category, Description, Cost, LaborCost, PartsCost, MaintenanceDate, CompletedDate, NextMaintenanceDate, NextMaintenanceMileage, ServiceProvider, TechnicianName, Status)
SELECT 
    v.VehicleId,
    CASE (ABS(CHECKSUM(NEWID())) % 6)
        WHEN 0 THEN 'oil_change'
        WHEN 1 THEN 'tire_replacement'
        WHEN 2 THEN 'brake_service'
        WHEN 3 THEN 'general_inspection'
        WHEN 4 THEN 'battery_service'
        ELSE 'transmission_service'
    END as MaintenanceType,
    CASE (ABS(CHECKSUM(NEWID())) % 3)
        WHEN 0 THEN 'Preventive'
        WHEN 1 THEN 'Repair'
        ELSE 'Emergency'
    END as Category,
    'Professional maintenance service performed according to manufacturer specifications' as Description,
    150 + (ABS(CHECKSUM(NEWID())) % 850) as Cost,
    75 + (ABS(CHECKSUM(NEWID())) % 200) as LaborCost,
    25 + (ABS(CHECKSUM(NEWID())) % 300) as PartsCost,
    DATEADD(DAY, (ABS(CHECKSUM(NEWID())) % 180), '2025-01-01') as MaintenanceDate,
    DATEADD(DAY, 1 + (ABS(CHECKSUM(NEWID())) % 3), DATEADD(DAY, (ABS(CHECKSUM(NEWID())) % 180), '2025-01-01')) as CompletedDate,
    DATEADD(DAY, 90 + (ABS(CHECKSUM(NEWID())) % 90), DATEADD(DAY, (ABS(CHECKSUM(NEWID())) % 180), '2025-01-01')) as NextMaintenanceDate,
    v.CurrentMileage + 5000 + (ABS(CHECKSUM(NEWID())) % 5000) as NextMaintenanceMileage,
    'FleetCare Service Center ' + CAST((ABS(CHECKSUM(NEWID())) % 10) + 1 AS NVARCHAR(10)) as ServiceProvider,
    'Tech-' + CAST((ABS(CHECKSUM(NEWID())) % 50) + 1 AS NVARCHAR(10)) as TechnicianName,
    'Completed' as Status
FROM Vehicles v
CROSS JOIN (SELECT TOP 3 ROW_NUMBER() OVER (ORDER BY (SELECT NULL)) as rn FROM sys.objects) r
WHERE v.VehicleId <= 10;

-- =============================================
-- ENHANCED VIEWS FOR API AND ANALYTICS
-- =============================================

-- Enhanced current vehicle status view
CREATE VIEW VehicleCurrentStatus AS
SELECT 
    v.VehicleId,
    v.VehicleName,
    v.LicensePlate,
    v.VIN,
    v.VehicleType,
    v.Brand,
    v.Model,
    v.Year,
    v.FuelType,
    v.Status as VehicleStatus,
    d.FirstName + ' ' + d.LastName as DriverName,
    d.Email as DriverEmail,
    d.Phone as DriverPhone,
    d.EmployeeId,
    t.Timestamp as LastUpdate,
    t.Latitude,
    t.Longitude,
    t.Speed,
    t.Heading,
    t.FuelLevel,
    t.Odometer,
    t.EngineStatus,
    t.Temperature,
    t.IsMoving,
    -- Calculate time since last update
    DATEDIFF(MINUTE, t.Timestamp, GETDATE()) as MinutesSinceLastUpdate,
    -- Status indicators
    CASE 
        WHEN DATEDIFF(MINUTE, t.Timestamp, GETDATE()) > 30 THEN 'Offline'
        WHEN t.IsMoving = 1 THEN 'Moving'
        WHEN t.EngineStatus = 'idle' THEN 'Idle'
        ELSE 'Parked'
    END as CurrentStatus
FROM Vehicles v
LEFT JOIN VehicleAssignments va ON v.VehicleId = va.VehicleId AND va.IsActive = 1
LEFT JOIN Drivers d ON va.DriverId = d.DriverId
OUTER APPLY (
    SELECT TOP 1 *
    FROM TelemetryData t2
    WHERE t2.VehicleId = v.VehicleId
    ORDER BY t2.Timestamp DESC
) t;

-- Enhanced daily vehicle summary for analytics
CREATE VIEW DailyVehicleSummary AS
SELECT 
    td.VehicleId,
    v.VehicleName,
    v.VehicleType,
    CAST(td.Timestamp AS DATE) as Date,
    COUNT(*) as TotalReadings,
    AVG(td.Speed) as AvgSpeed,
    MAX(td.Speed) as MaxSpeed,
    MIN(td.FuelLevel) as MinFuelLevel,
    MAX(td.FuelLevel) as MaxFuelLevel,
    AVG(td.FuelLevel) as AvgFuelLevel,
    MAX(td.Odometer) - MIN(td.Odometer) as DistanceTraveled,
    SUM(CASE WHEN td.HarshBraking = 1 THEN 1 ELSE 0 END) as HarshBrakingCount,
    SUM(CASE WHEN td.HarshAcceleration = 1 THEN 1 ELSE 0 END) as HarshAccelerationCount,
    SUM(td.IdleTime) as TotalIdleTime,
    AVG(td.Temperature) as AvgTemperature,
    COUNT(CASE WHEN td.IsMoving = 1 THEN 1 END) as MovingReadings,
    ROUND(AVG(td.FuelConsumption), 2) as AvgFuelConsumption
FROM TelemetryData td
INNER JOIN Vehicles v ON td.VehicleId = v.VehicleId
GROUP BY td.VehicleId, v.VehicleName, v.VehicleType, CAST(td.Timestamp AS DATE);

-- Vehicle utilization view for fleet optimization
CREATE VIEW VehicleUtilization AS
SELECT 
    v.VehicleId,
    v.VehicleName,
    v.VehicleType,
    v.Status,
    COUNT(DISTINCT CAST(td.Timestamp AS DATE)) as ActiveDays,
    AVG(td.Speed) as AvgSpeed,
    SUM(CASE WHEN td.IsMoving = 1 THEN 1 ELSE 0 END) * 1.0 / COUNT(*) * 100 as UtilizationPercentage,
    SUM(CASE WHEN td.HarshBraking = 1 OR td.HarshAcceleration = 1 THEN 1 ELSE 0 END) as TotalHarshEvents,
    COUNT(*) as TotalReadings
FROM Vehicles v
LEFT JOIN TelemetryData td ON v.VehicleId = td.VehicleId 
    AND td.Timestamp >= DATEADD(DAY, -30, GETDATE())
GROUP BY v.VehicleId, v.VehicleName, v.VehicleType, v.Status;

-- Alert summary view for dashboard
CREATE VIEW AlertSummary AS
SELECT 
    AlertType,
    Severity,
    COUNT(*) as AlertCount,
    COUNT(CASE WHEN Status = 'Active' THEN 1 END) as ActiveAlerts,
    COUNT(CASE WHEN Status = 'Acknowledged' THEN 1 END) as AcknowledgedAlerts,
    COUNT(CASE WHEN Status = 'Resolved' THEN 1 END) as ResolvedAlerts,
    MIN(CreatedDate) as FirstAlert,
    MAX(CreatedDate) as LastAlert
FROM Alerts
WHERE CreatedDate >= DATEADD(DAY, -7, GETDATE())
GROUP BY AlertType, Severity;

-- =============================================
-- STORED PROCEDURES FOR API ENDPOINTS
-- =============================================

-- Get real-time vehicle location data
CREATE PROCEDURE GetVehicleLocations
    @VehicleId INT = NULL
AS
BEGIN
    SELECT 
        v.VehicleId,
        v.VehicleName,
        v.LicensePlate,
        v.VehicleType,
        t.Latitude,
        t.Longitude,
        t.Speed,
        t.Heading,
        t.Timestamp,
        t.EngineStatus,
        t.FuelLevel,
        CASE 
            WHEN DATEDIFF(MINUTE, t.Timestamp, GETDATE()) > 30 THEN 'Offline'
            WHEN t.IsMoving = 1 THEN 'Moving'
            WHEN t.EngineStatus = 'idle' THEN 'Idle'
            ELSE 'Parked'
        END as Status
    FROM Vehicles v
    OUTER APPLY (
        SELECT TOP 1 *
        FROM TelemetryData t2
        WHERE t2.VehicleId = v.VehicleId
        ORDER BY t2.Timestamp DESC
    ) t
    WHERE (@VehicleId IS NULL OR v.VehicleId = @VehicleId)
        AND v.Status = 'active'
    ORDER BY v.VehicleName;
END
GO

-- Get vehicle performance metrics
CREATE PROCEDURE GetVehiclePerformance
    @VehicleId INT,
    @StartDate DATETIME2 = NULL,
    @EndDate DATETIME2 = NULL
AS
BEGIN
    SET @StartDate = ISNULL(@StartDate, DATEADD(DAY, -30, GETDATE()));
    SET @EndDate = ISNULL(@EndDate, GETDATE());
    
    SELECT 
        @VehicleId as VehicleId,
        COUNT(*) as TotalReadings,
        AVG(Speed) as AvgSpeed,
        MAX(Speed) as MaxSpeed,
        AVG(FuelLevel) as AvgFuelLevel,
        MIN(FuelLevel) as MinFuelLevel,
        SUM(CASE WHEN HarshBraking = 1 THEN 1 ELSE 0 END) as HarshBrakingEvents,
        SUM(CASE WHEN HarshAcceleration = 1 THEN 1 ELSE 0 END) as HarshAccelerationEvents,
        SUM(IdleTime) / 60.0 as TotalIdleHours,
        AVG(Temperature) as AvgTemperature,
        MAX(Odometer) - MIN(Odometer) as TotalDistance
    FROM TelemetryData
    WHERE VehicleId = @VehicleId
        AND Timestamp BETWEEN @StartDate AND @EndDate;
END
GO

-- =============================================
-- ADDITIONAL INDEXES FOR PERFORMANCE
-- =============================================

-- Composite indexes for common query patterns
CREATE INDEX IX_TelemetryData_Vehicle_Date_Performance 
ON TelemetryData (VehicleId, Timestamp) 
INCLUDE (Speed, FuelLevel, Odometer, EngineStatus, IsMoving, HarshBraking, HarshAcceleration);

CREATE INDEX IX_TelemetryData_Speed_Analysis 
ON TelemetryData (Speed, Timestamp) 
INCLUDE (VehicleId, Latitude, Longitude);

CREATE INDEX IX_Alerts_Vehicle_Status_Date 
ON Alerts (VehicleId, Status, CreatedDate) 
INCLUDE (AlertType, Severity, Message);

CREATE INDEX IX_Trips_Analysis 
ON Trips (VehicleId, StartTime, Status) 
INCLUDE (TotalDistance, TotalDuration, MaxSpeed, AvgSpeed, HarshEvents);

-- =============================================
-- UPDATE STATISTICS FOR OPTIMAL PERFORMANCE
-- =============================================
UPDATE STATISTICS Vehicles;
UPDATE STATISTICS Drivers;
UPDATE STATISTICS TelemetryData;
UPDATE STATISTICS Trips;
UPDATE STATISTICS Alerts;
UPDATE STATISTICS FuelTransactions;
UPDATE STATISTICS MaintenanceRecords;

-- =============================================
-- VERIFICATION AND SUMMARY
-- =============================================

PRINT '====================================';
PRINT 'ENHANCED FLEET XQ DATABASE SETUP COMPLETED!';
PRINT '====================================';
PRINT 'Database: FleetXQ_Assessment';
PRINT 'Tables created: 10 (Enhanced for AI & Real-time)';
PRINT 'Views created: 4 (Optimized for Analytics)';
PRINT 'Stored Procedures: 2 (API-ready)';
PRINT 'Enhanced Features:';
PRINT '- User Management & Security';
PRINT '- Real-time Telemetry Analytics';
PRINT '- AI-ready Data Patterns';
PRINT '- Advanced Fleet Monitoring';
PRINT '- Performance Optimization';
PRINT '====================================';

-- Final verification with enhanced metrics
SELECT 
    'Users' as TableName, COUNT(*) as RecordCount, 'Authentication & Authorization' as Purpose FROM Users
UNION ALL
SELECT 'Vehicles', COUNT(*), 'Fleet Asset Management' FROM Vehicles
UNION ALL
SELECT 'Drivers', COUNT(*), 'Human Resource Management' FROM Drivers
UNION ALL
SELECT 'TelemetryData', COUNT(*), 'Real-time Vehicle Monitoring' FROM TelemetryData
UNION ALL
SELECT 'Trips', COUNT(*), 'Route Analytics & Optimization' FROM Trips
UNION ALL
SELECT 'Alerts', COUNT(*), 'Proactive Fleet Monitoring' FROM Alerts
UNION ALL
SELECT 'FuelTransactions', COUNT(*), 'Cost Management & Analysis' FROM FuelTransactions
UNION ALL
SELECT 'MaintenanceRecords', COUNT(*), 'Preventive Maintenance' FROM MaintenanceRecords
UNION ALL
SELECT 'Geofences', COUNT(*), 'Location-based Services' FROM Geofences
UNION ALL
SELECT 'VehicleAssignments', COUNT(*), 'Resource Allocation' FROM VehicleAssignments;

-- Data quality verification
SELECT 
    'Data Quality Check' as CheckType, 
    'Active Vehicles with Recent Data' as Description,
    COUNT(*) as Count 
FROM Vehicles v
INNER JOIN TelemetryData t ON v.VehicleId = t.VehicleId
WHERE v.Status = 'active' 
    AND t.Timestamp >= DATEADD(HOUR, -24, GETDATE());

SELECT 
    'Performance Check' as CheckType,
    'Average Records per Vehicle (Last 24h)' as Description,
    AVG(RecordCount) as Count
FROM (
    SELECT VehicleId, COUNT(*) as RecordCount
    FROM TelemetryData
    WHERE Timestamp >= DATEADD(HOUR, -24, GETDATE())
    GROUP BY VehicleId
) subq;

PRINT '====================================';
PRINT 'Ready for AI-Augmented Development!';
PRINT 'Optimized for: .NET Core + EF + Real-time Analytics';
PRINT '====================================';
