using FleetXQ.Domain.Enums;
using MediatR;

namespace FleetXQ.Application.Features.Vehicles.Commands.UpdateVehicleStatus;

/// <summary>
/// Command to update a vehicle's status
/// </summary>
public sealed class UpdateVehicleStatusCommand : IRequest<UpdateVehicleStatusResult>
{
    /// <summary>
    /// Gets or sets the vehicle ID
    /// </summary>
    public Guid VehicleId { get; set; }

    /// <summary>
    /// Gets or sets the new vehicle status
    /// </summary>
    public VehicleStatus Status { get; set; }

    /// <summary>
    /// Gets or sets the reason for the status change
    /// </summary>
    public string? Reason { get; set; }

    /// <summary>
    /// Gets or sets the user ID who is making the change
    /// </summary>
    public Guid? ChangedBy { get; set; }

    /// <summary>
    /// Gets or sets additional context for the status change
    /// </summary>
    public string? Context { get; set; }
}

/// <summary>
/// Result of updating a vehicle's status
/// </summary>
public sealed class UpdateVehicleStatusResult
{
    /// <summary>
    /// Gets or sets the vehicle ID
    /// </summary>
    public Guid VehicleId { get; set; }

    /// <summary>
    /// Gets or sets the previous status
    /// </summary>
    public VehicleStatus PreviousStatus { get; set; }

    /// <summary>
    /// Gets or sets the new status
    /// </summary>
    public VehicleStatus NewStatus { get; set; }

    /// <summary>
    /// Gets or sets a value indicating whether the operation was successful
    /// </summary>
    public bool Success { get; set; }

    /// <summary>
    /// Gets or sets the error message if the operation failed
    /// </summary>
    public string? ErrorMessage { get; set; }

    /// <summary>
    /// Creates a successful result
    /// </summary>
    /// <param name="vehicleId">The vehicle ID</param>
    /// <param name="previousStatus">The previous status</param>
    /// <param name="newStatus">The new status</param>
    /// <returns>A successful result</returns>
    public static UpdateVehicleStatusResult Successful(Guid vehicleId, VehicleStatus previousStatus, VehicleStatus newStatus)
    {
        return new UpdateVehicleStatusResult
        {
            VehicleId = vehicleId,
            PreviousStatus = previousStatus,
            NewStatus = newStatus,
            Success = true
        };
    }

    /// <summary>
    /// Creates a failed result
    /// </summary>
    /// <param name="vehicleId">The vehicle ID</param>
    /// <param name="errorMessage">The error message</param>
    /// <returns>A failed result</returns>
    public static UpdateVehicleStatusResult Failed(Guid vehicleId, string errorMessage)
    {
        return new UpdateVehicleStatusResult
        {
            VehicleId = vehicleId,
            Success = false,
            ErrorMessage = errorMessage
        };
    }
}
