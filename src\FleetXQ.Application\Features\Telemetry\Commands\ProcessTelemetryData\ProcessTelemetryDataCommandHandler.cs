using FleetXQ.Domain.Interfaces;
using FleetXQ.Domain.Services;
using FleetXQ.Domain.ValueObjects;
using MediatR;
using Microsoft.Extensions.Logging;

namespace FleetXQ.Application.Features.Telemetry.Commands.ProcessTelemetryData;

/// <summary>
/// Handler for ProcessTelemetryDataCommand
/// </summary>
public sealed class ProcessTelemetryDataCommandHandler : IRequestHandler<ProcessTelemetryDataCommand, ProcessTelemetryDataResult>
{
    private readonly IVehicleRepository _vehicleRepository;
    private readonly IAlertRepository _alertRepository;
    private readonly IAlertEvaluationService _alertEvaluationService;
    private readonly ILogger<ProcessTelemetryDataCommandHandler> _logger;

    /// <summary>
    /// Initializes a new instance of the <see cref="ProcessTelemetryDataCommandHandler"/> class
    /// </summary>
    /// <param name="vehicleRepository">The vehicle repository</param>
    /// <param name="alertRepository">The alert repository</param>
    /// <param name="alertEvaluationService">The alert evaluation service</param>
    /// <param name="logger">The logger</param>
    public ProcessTelemetryDataCommandHandler(
        IVehicleRepository vehicleRepository,
        IAlertRepository alertRepository,
        IAlertEvaluationService alertEvaluationService,
        ILogger<ProcessTelemetryDataCommandHandler> logger)
    {
        _vehicleRepository = vehicleRepository ?? throw new ArgumentNullException(nameof(vehicleRepository));
        _alertRepository = alertRepository ?? throw new ArgumentNullException(nameof(alertRepository));
        _alertEvaluationService = alertEvaluationService ?? throw new ArgumentNullException(nameof(alertEvaluationService));
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
    }

    /// <summary>
    /// Handles the ProcessTelemetryDataCommand
    /// </summary>
    /// <param name="request">The command request</param>
    /// <param name="cancellationToken">The cancellation token</param>
    /// <returns>The command result</returns>
    public async Task<ProcessTelemetryDataResult> Handle(ProcessTelemetryDataCommand request, CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogInformation("Processing telemetry data for vehicle {VehicleId}", request.VehicleId);

            // Get the vehicle
            var vehicle = await _vehicleRepository.GetByIdAsync(request.VehicleId, cancellationToken);
            if (vehicle == null)
            {
                _logger.LogWarning("Vehicle with ID {VehicleId} not found", request.VehicleId);
                return ProcessTelemetryDataResult.Failed(request.VehicleId, "Vehicle not found");
            }

            // Create value objects from the telemetry data
            var location = new Location(request.Latitude, request.Longitude);
            var speed = new Speed(request.SpeedKmh);
            FuelLevel? fuelLevel = request.FuelLevelPercentage.HasValue 
                ? new FuelLevel(request.FuelLevelPercentage.Value) 
                : null;

            // Update vehicle telemetry
            vehicle.UpdateTelemetry(location, speed, fuelLevel, request.Mileage);

            // Update the vehicle in the repository
            await _vehicleRepository.UpdateAsync(vehicle, cancellationToken);

            // Evaluate alerts based on telemetry data
            var triggeredAlerts = new List<string>();
            var alertsToCreate = _alertEvaluationService.EvaluateTelemetryData(vehicle, null, location, speed, fuelLevel);

            // Process each alert
            foreach (var alert in alertsToCreate)
            {
                try
                {
                    await _alertRepository.AddAsync(alert, cancellationToken);
                    triggeredAlerts.Add(alert.Type.ToString());
                    
                    _logger.LogInformation("Created alert {AlertType} for vehicle {VehicleId}", 
                        alert.Type, request.VehicleId);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Failed to create alert {AlertType} for vehicle {VehicleId}", 
                        alert.Type, request.VehicleId);
                }
            }

            _logger.LogInformation("Successfully processed telemetry data for vehicle {VehicleId}. Alerts triggered: {AlertCount}", 
                request.VehicleId, triggeredAlerts.Count);

            return ProcessTelemetryDataResult.Successful(
                request.VehicleId, 
                triggeredAlerts.Count > 0, 
                triggeredAlerts);
        }
        catch (ArgumentException ex)
        {
            _logger.LogWarning(ex, "Invalid argument when processing telemetry data for vehicle {VehicleId}", request.VehicleId);
            return ProcessTelemetryDataResult.Failed(request.VehicleId, ex.Message);
        }
        catch (InvalidOperationException ex)
        {
            _logger.LogWarning(ex, "Invalid operation when processing telemetry data for vehicle {VehicleId}", request.VehicleId);
            return ProcessTelemetryDataResult.Failed(request.VehicleId, ex.Message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing telemetry data for vehicle {VehicleId}", request.VehicleId);
            return ProcessTelemetryDataResult.Failed(request.VehicleId, $"An error occurred while processing telemetry data: {ex.Message}");
        }
    }
}
