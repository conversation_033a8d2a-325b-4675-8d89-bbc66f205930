using FleetXQ.Application.Common.Behaviors;
using FluentAssertions;
using FluentValidation;
using FluentValidation.Results;
using MediatR;
using Moq;

namespace FleetXQ.Application.Tests.Common.Behaviors;

public class ValidationBehaviorTests
{
    private readonly Mock<IValidator<TestRequest>> _mockValidator;
    private readonly Mock<RequestHandlerDelegate<TestResponse>> _mockNext;
    private readonly ValidationBehavior<TestRequest, TestResponse> _behavior;

    public ValidationBehaviorTests()
    {
        _mockValidator = new Mock<IValidator<TestRequest>>();
        _mockNext = new Mock<RequestHandlerDelegate<TestResponse>>();
        _behavior = new ValidationBehavior<TestRequest, TestResponse>(new[] { _mockValidator.Object });
    }

    [Fact]
    public async Task Handle_WithNoValidators_ShouldCallNext()
    {
        // Arrange
        var behaviorWithoutValidators = new ValidationBehavior<TestRequest, TestResponse>(Enumerable.Empty<IValidator<TestRequest>>());
        var request = new TestRequest { Value = "test" };
        var expectedResponse = new TestResponse { Result = "success" };

        _mockNext.Setup(x => x()).ReturnsAsync(expectedResponse);

        // Act
        var result = await behaviorWithoutValidators.Handle(request, _mockNext.Object, CancellationToken.None);

        // Assert
        result.Should().Be(expectedResponse);
        _mockNext.Verify(x => x(), Times.Once);
    }

    [Fact]
    public async Task Handle_WithValidRequest_ShouldCallNext()
    {
        // Arrange
        var request = new TestRequest { Value = "test" };
        var expectedResponse = new TestResponse { Result = "success" };

        _mockValidator.Setup(x => x.ValidateAsync(It.IsAny<ValidationContext<TestRequest>>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(new ValidationResult());

        _mockNext.Setup(x => x()).ReturnsAsync(expectedResponse);

        // Act
        var result = await _behavior.Handle(request, _mockNext.Object, CancellationToken.None);

        // Assert
        result.Should().Be(expectedResponse);
        _mockValidator.Verify(x => x.ValidateAsync(It.IsAny<ValidationContext<TestRequest>>(), It.IsAny<CancellationToken>()), Times.Once);
        _mockNext.Verify(x => x(), Times.Once);
    }

    [Fact]
    public async Task Handle_WithValidationErrors_ShouldThrowValidationException()
    {
        // Arrange
        var request = new TestRequest { Value = "invalid" };
        var validationFailures = new List<ValidationFailure>
        {
            new("Value", "Value is required"),
            new("Value", "Value must be at least 5 characters"),
            new("Name", "Name is required")
        };

        _mockValidator.Setup(x => x.ValidateAsync(It.IsAny<ValidationContext<TestRequest>>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(new ValidationResult(validationFailures));

        // Act & Assert
        var exception = await Assert.ThrowsAsync<ValidationException>(() =>
            _behavior.Handle(request, _mockNext.Object, CancellationToken.None));

        exception.Should().NotBeNull();
        exception.Message.Should().Contain("validation failures");
        exception.Errors.Should().HaveCount(2); // Two properties with errors
        exception.Errors.Should().ContainKey("Value");
        exception.Errors.Should().ContainKey("Name");
        exception.Errors["Value"].Should().HaveCount(2);
        exception.Errors["Name"].Should().HaveCount(1);

        _mockNext.Verify(x => x(), Times.Never);
    }

    [Fact]
    public async Task Handle_WithMultipleValidators_ShouldRunAllValidators()
    {
        // Arrange
        var mockValidator2 = new Mock<IValidator<TestRequest>>();
        var behaviorWithMultipleValidators = new ValidationBehavior<TestRequest, TestResponse>(
            new[] { _mockValidator.Object, mockValidator2.Object });

        var request = new TestRequest { Value = "test" };
        var expectedResponse = new TestResponse { Result = "success" };

        _mockValidator.Setup(x => x.ValidateAsync(It.IsAny<ValidationContext<TestRequest>>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(new ValidationResult());

        mockValidator2.Setup(x => x.ValidateAsync(It.IsAny<ValidationContext<TestRequest>>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(new ValidationResult());

        _mockNext.Setup(x => x()).ReturnsAsync(expectedResponse);

        // Act
        var result = await behaviorWithMultipleValidators.Handle(request, _mockNext.Object, CancellationToken.None);

        // Assert
        result.Should().Be(expectedResponse);
        _mockValidator.Verify(x => x.ValidateAsync(It.IsAny<ValidationContext<TestRequest>>(), It.IsAny<CancellationToken>()), Times.Once);
        mockValidator2.Verify(x => x.ValidateAsync(It.IsAny<ValidationContext<TestRequest>>(), It.IsAny<CancellationToken>()), Times.Once);
        _mockNext.Verify(x => x(), Times.Once);
    }

    [Fact]
    public async Task Handle_WithMixedValidationResults_ShouldThrowValidationException()
    {
        // Arrange
        var mockValidator2 = new Mock<IValidator<TestRequest>>();
        var behaviorWithMultipleValidators = new ValidationBehavior<TestRequest, TestResponse>(
            new[] { _mockValidator.Object, mockValidator2.Object });

        var request = new TestRequest { Value = "test" };

        // First validator passes
        _mockValidator.Setup(x => x.ValidateAsync(It.IsAny<ValidationContext<TestRequest>>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(new ValidationResult());

        // Second validator fails
        var validationFailures = new List<ValidationFailure>
        {
            new("Email", "Email is required")
        };
        mockValidator2.Setup(x => x.ValidateAsync(It.IsAny<ValidationContext<TestRequest>>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(new ValidationResult(validationFailures));

        // Act & Assert
        var exception = await Assert.ThrowsAsync<ValidationException>(() =>
            behaviorWithMultipleValidators.Handle(request, _mockNext.Object, CancellationToken.None));

        exception.Should().NotBeNull();
        exception.Errors.Should().ContainKey("Email");
        _mockNext.Verify(x => x(), Times.Never);
    }

    [Fact]
    public async Task Handle_WithCancellationToken_ShouldPassTokenToValidators()
    {
        // Arrange
        var request = new TestRequest { Value = "test" };
        var expectedResponse = new TestResponse { Result = "success" };
        var cancellationToken = new CancellationTokenSource().Token;

        _mockValidator.Setup(x => x.ValidateAsync(It.IsAny<ValidationContext<TestRequest>>(), cancellationToken))
            .ReturnsAsync(new ValidationResult());

        _mockNext.Setup(x => x()).ReturnsAsync(expectedResponse);

        // Act
        var result = await _behavior.Handle(request, _mockNext.Object, cancellationToken);

        // Assert
        result.Should().Be(expectedResponse);
        _mockValidator.Verify(x => x.ValidateAsync(It.IsAny<ValidationContext<TestRequest>>(), cancellationToken), Times.Once);
    }

    [Fact]
    public void ValidationException_WithPropertyNameAndMessage_ShouldCreateCorrectException()
    {
        // Arrange
        var propertyName = "TestProperty";
        var errorMessage = "Test error message";

        // Act
        var exception = new ValidationException(propertyName, errorMessage);

        // Assert
        exception.Should().NotBeNull();
        exception.Message.Should().Contain(propertyName);
        exception.Message.Should().Contain(errorMessage);
        exception.Errors.Should().ContainKey(propertyName);
        exception.Errors[propertyName].Should().Contain(errorMessage);
    }

    [Fact]
    public void ValidationException_WithErrorsDictionary_ShouldCreateCorrectException()
    {
        // Arrange
        var errors = new Dictionary<string, string[]>
        {
            { "Property1", new[] { "Error1", "Error2" } },
            { "Property2", new[] { "Error3" } }
        };

        // Act
        var exception = new ValidationException(errors);

        // Assert
        exception.Should().NotBeNull();
        exception.Message.Should().Contain("validation failures");
        exception.Errors.Should().BeEquivalentTo(errors);
    }

    [Fact]
    public void ValidationException_WithValidationFailures_ShouldGroupErrorsByProperty()
    {
        // Arrange
        var failures = new List<ValidationFailure>
        {
            new("Property1", "Error1"),
            new("Property1", "Error2"),
            new("Property2", "Error3")
        };

        // Act
        var exception = new ValidationException(failures);

        // Assert
        exception.Should().NotBeNull();
        exception.Errors.Should().HaveCount(2);
        exception.Errors["Property1"].Should().HaveCount(2);
        exception.Errors["Property1"].Should().Contain("Error1");
        exception.Errors["Property1"].Should().Contain("Error2");
        exception.Errors["Property2"].Should().HaveCount(1);
        exception.Errors["Property2"].Should().Contain("Error3");
    }

    // Test helper classes
    public class TestRequest : IRequest<TestResponse>
    {
        public string Value { get; set; } = string.Empty;
    }

    public class TestResponse
    {
        public string Result { get; set; } = string.Empty;
    }
}
