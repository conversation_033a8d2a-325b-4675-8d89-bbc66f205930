using FleetXQ.Domain.Entities;
using FleetXQ.Domain.Enums;
using FleetXQ.Domain.ValueObjects;
using FleetXQ.Domain.Events;
using Xunit;

namespace FleetXQ.Domain.Tests.Entities;

public class VehicleTests
{
    [Fact]
    public void Constructor_WithValidParameters_ShouldCreateVehicle()
    {
        // Arrange
        var vehicleName = "Fleet Vehicle 001";
        var licensePlate = "ABC-123";
        var vehicleType = "Car";
        var fuelType = "Gasoline";

        // Act
        var vehicle = new Vehicle(vehicleName, licensePlate, vehicleType, fuelType);

        // Assert
        Assert.Equal(vehicleName, vehicle.VehicleName);
        Assert.Equal(licensePlate, vehicle.LicensePlate);
        Assert.Equal(vehicleType, vehicle.VehicleType);
        Assert.Equal(fuelType, vehicle.FuelType);
        Assert.Equal(VehicleStatus.Active, vehicle.Status);
        Assert.Equal(0, vehicle.CurrentMileage);
        Assert.True(vehicle.IsAvailable);
    }

    [Theory]
    [InlineData("", "ABC-123", "Car")]
    [InlineData("Vehicle", "", "Car")]
    [InlineData("Vehicle", "ABC-123", "")]
    [InlineData(null, "ABC-123", "Car")]
    [InlineData("Vehicle", null, "Car")]
    [InlineData("Vehicle", "ABC-123", null)]
    public void Constructor_WithInvalidParameters_ShouldThrowArgumentException(string vehicleName, string licensePlate, string vehicleType)
    {
        // Act & Assert
        Assert.Throws<ArgumentException>(() => new Vehicle(vehicleName, licensePlate, vehicleType));
    }

    [Fact]
    public void UpdateBasicInfo_WithValidParameters_ShouldUpdateProperties()
    {
        // Arrange
        var vehicle = new Vehicle("Old Name", "ABC-123", "Car");
        var newName = "New Name";
        var brand = "Toyota";
        var model = "Camry";
        var year = 2023;
        var color = "Blue";

        // Act
        vehicle.UpdateBasicInfo(newName, brand, model, year, color);

        // Assert
        Assert.Equal(newName, vehicle.VehicleName);
        Assert.Equal(brand, vehicle.Brand);
        Assert.Equal(model, vehicle.Model);
        Assert.Equal(year, vehicle.Year);
        Assert.Equal(color, vehicle.Color);
    }

    [Fact]
    public void UpdateBasicInfo_WithEmptyName_ShouldThrowArgumentException()
    {
        // Arrange
        var vehicle = new Vehicle("Vehicle", "ABC-123", "Car");

        // Act & Assert
        Assert.Throws<ArgumentException>(() => vehicle.UpdateBasicInfo(""));
    }

    [Fact]
    public void UpdateFuelTankCapacity_WithValidCapacity_ShouldUpdateCapacity()
    {
        // Arrange
        var vehicle = new Vehicle("Vehicle", "ABC-123", "Car");
        var capacity = 60m;

        // Act
        vehicle.UpdateFuelTankCapacity(capacity);

        // Assert
        Assert.Equal(capacity, vehicle.FuelTankCapacity);
    }

    [Fact]
    public void UpdateFuelTankCapacity_WithNegativeCapacity_ShouldThrowArgumentOutOfRangeException()
    {
        // Arrange
        var vehicle = new Vehicle("Vehicle", "ABC-123", "Car");

        // Act & Assert
        Assert.Throws<ArgumentOutOfRangeException>(() => vehicle.UpdateFuelTankCapacity(-10));
    }

    [Fact]
    public void ChangeStatus_WithDifferentStatus_ShouldUpdateStatusAndRaiseDomainEvent()
    {
        // Arrange
        var vehicle = new Vehicle("Vehicle", "ABC-123", "Car");
        var newStatus = VehicleStatus.Maintenance;
        var reason = "Scheduled maintenance";

        // Act
        vehicle.ChangeStatus(newStatus, reason);

        // Assert
        Assert.Equal(newStatus, vehicle.Status);
        Assert.Single(vehicle.DomainEvents);
        Assert.IsType<VehicleStatusChangedEvent>(vehicle.DomainEvents.First());
    }

    [Fact]
    public void ChangeStatus_WithSameStatus_ShouldNotRaiseDomainEvent()
    {
        // Arrange
        var vehicle = new Vehicle("Vehicle", "ABC-123", "Car");
        var currentStatus = vehicle.Status;

        // Act
        vehicle.ChangeStatus(currentStatus);

        // Assert
        Assert.Empty(vehicle.DomainEvents);
    }

    [Fact]
    public void UpdateTelemetry_WithValidData_ShouldUpdatePropertiesAndRaiseDomainEvent()
    {
        // Arrange
        var vehicle = new Vehicle("Vehicle", "ABC-123", "Car");
        var location = new Location(40.7128m, -74.0060m);
        var speed = new Speed(60);
        var fuelLevel = new FuelLevel(75);
        var mileage = 1000m;

        // Act
        vehicle.UpdateTelemetry(location, speed, fuelLevel, mileage);

        // Assert
        Assert.Equal(location, vehicle.CurrentLocation);
        Assert.Equal(speed, vehicle.CurrentSpeed);
        Assert.Equal(fuelLevel, vehicle.CurrentFuelLevel);
        Assert.Equal(mileage, vehicle.CurrentMileage);
        Assert.NotNull(vehicle.LastTelemetryUpdate);
        Assert.Single(vehicle.DomainEvents);
        Assert.IsType<TelemetryDataReceivedEvent>(vehicle.DomainEvents.First());
    }

    [Fact]
    public void UpdateTelemetry_WithNullLocation_ShouldThrowArgumentNullException()
    {
        // Arrange
        var vehicle = new Vehicle("Vehicle", "ABC-123", "Car");
        var speed = new Speed(60);

        // Act & Assert
        Assert.Throws<ArgumentNullException>(() => vehicle.UpdateTelemetry(null!, speed));
    }

    [Fact]
    public void UpdateTelemetry_WithLowerMileage_ShouldNotUpdateMileage()
    {
        // Arrange
        var vehicle = new Vehicle("Vehicle", "ABC-123", "Car");
        var location = new Location(40.7128m, -74.0060m);
        var speed = new Speed(60);
        
        // Set initial mileage
        vehicle.UpdateTelemetry(location, speed, null, 1000m);
        
        // Act - try to set lower mileage
        vehicle.UpdateTelemetry(location, speed, null, 500m);

        // Assert
        Assert.Equal(1000m, vehicle.CurrentMileage);
    }

    [Fact]
    public void ScheduleMaintenance_WithFutureDate_ShouldUpdateNextMaintenanceDate()
    {
        // Arrange
        var vehicle = new Vehicle("Vehicle", "ABC-123", "Car");
        var futureDate = DateTime.UtcNow.AddDays(30);

        // Act
        vehicle.ScheduleMaintenance(futureDate);

        // Assert
        Assert.Equal(futureDate, vehicle.NextMaintenanceDate);
    }

    [Fact]
    public void ScheduleMaintenance_WithPastDate_ShouldThrowArgumentException()
    {
        // Arrange
        var vehicle = new Vehicle("Vehicle", "ABC-123", "Car");
        var pastDate = DateTime.UtcNow.AddDays(-1);

        // Act & Assert
        Assert.Throws<ArgumentException>(() => vehicle.ScheduleMaintenance(pastDate));
    }

    [Fact]
    public void RecordMaintenanceCompleted_ShouldUpdateMaintenanceDatesAndStatus()
    {
        // Arrange
        var vehicle = new Vehicle("Vehicle", "ABC-123", "Car");
        vehicle.ChangeStatus(VehicleStatus.Maintenance);
        var completionDate = DateTime.UtcNow;
        var nextMaintenanceDate = DateTime.UtcNow.AddDays(90);

        // Act
        vehicle.RecordMaintenanceCompleted(completionDate, nextMaintenanceDate);

        // Assert
        Assert.Equal(completionDate, vehicle.LastMaintenanceDate);
        Assert.Equal(nextMaintenanceDate, vehicle.NextMaintenanceDate);
        Assert.Equal(VehicleStatus.Active, vehicle.Status);
    }

    [Fact]
    public void NeedsMaintenance_WithOverdueMaintenanceDate_ShouldReturnTrue()
    {
        // Arrange
        var vehicle = new Vehicle("Vehicle", "ABC-123", "Car");
        vehicle.ScheduleMaintenance(DateTime.UtcNow.AddDays(-1)); // Overdue

        // Act & Assert
        Assert.True(vehicle.NeedsMaintenance);
    }

    [Fact]
    public void NeedsMaintenance_WithFutureMaintenanceDate_ShouldReturnFalse()
    {
        // Arrange
        var vehicle = new Vehicle("Vehicle", "ABC-123", "Car");
        vehicle.ScheduleMaintenance(DateTime.UtcNow.AddDays(30));

        // Act & Assert
        Assert.False(vehicle.NeedsMaintenance);
    }

    [Fact]
    public void IsAvailable_WithActiveStatusAndNoMaintenance_ShouldReturnTrue()
    {
        // Arrange
        var vehicle = new Vehicle("Vehicle", "ABC-123", "Car");

        // Act & Assert
        Assert.True(vehicle.IsAvailable);
    }

    [Fact]
    public void IsAvailable_WithMaintenanceStatus_ShouldReturnFalse()
    {
        // Arrange
        var vehicle = new Vehicle("Vehicle", "ABC-123", "Car");
        vehicle.ChangeStatus(VehicleStatus.Maintenance);

        // Act & Assert
        Assert.False(vehicle.IsAvailable);
    }

    [Fact]
    public void RequiresImmediateAttention_WithCriticalFuelLevel_ShouldReturnTrue()
    {
        // Arrange
        var vehicle = new Vehicle("Vehicle", "ABC-123", "Car");
        var location = new Location(40.7128m, -74.0060m);
        var speed = new Speed(60);
        var criticalFuelLevel = new FuelLevel(5); // Critical level

        vehicle.UpdateTelemetry(location, speed, criticalFuelLevel);

        // Act & Assert
        Assert.True(vehicle.RequiresImmediateAttention());
    }

    [Fact]
    public void RequiresImmediateAttention_WithNormalFuelLevel_ShouldReturnFalse()
    {
        // Arrange
        var vehicle = new Vehicle("Vehicle", "ABC-123", "Car");
        var location = new Location(40.7128m, -74.0060m);
        var speed = new Speed(60);
        var normalFuelLevel = new FuelLevel(50);

        vehicle.UpdateTelemetry(location, speed, normalFuelLevel);

        // Act & Assert
        Assert.False(vehicle.RequiresImmediateAttention());
    }

    [Fact]
    public void GetEstimatedRange_WithValidData_ShouldCalculateRange()
    {
        // Arrange
        var vehicle = new Vehicle("Vehicle", "ABC-123", "Car");
        vehicle.UpdateFuelTankCapacity(60m);
        
        var location = new Location(40.7128m, -74.0060m);
        var speed = new Speed(60);
        var fuelLevel = new FuelLevel(50); // 50% of 60L = 30L
        
        vehicle.UpdateTelemetry(location, speed, fuelLevel);
        
        var averageConsumption = 8m; // 8L per 100km
        var expectedRange = (30m / 8m) * 100m; // 375km

        // Act
        var range = vehicle.GetEstimatedRange(averageConsumption);

        // Assert
        Assert.Equal(expectedRange, range);
    }

    [Fact]
    public void GetEstimatedRange_WithoutFuelData_ShouldReturnNull()
    {
        // Arrange
        var vehicle = new Vehicle("Vehicle", "ABC-123", "Car");

        // Act
        var range = vehicle.GetEstimatedRange(8m);

        // Assert
        Assert.Null(range);
    }

    [Fact]
    public void IsMoving_WithNonZeroSpeed_ShouldReturnTrue()
    {
        // Arrange
        var vehicle = new Vehicle("Vehicle", "ABC-123", "Car");
        var location = new Location(40.7128m, -74.0060m);
        var speed = new Speed(60);

        vehicle.UpdateTelemetry(location, speed);

        // Act & Assert
        Assert.True(vehicle.IsMoving);
    }

    [Fact]
    public void IsMoving_WithZeroSpeed_ShouldReturnFalse()
    {
        // Arrange
        var vehicle = new Vehicle("Vehicle", "ABC-123", "Car");
        var location = new Location(40.7128m, -74.0060m);
        var speed = Speed.Zero;

        vehicle.UpdateTelemetry(location, speed);

        // Act & Assert
        Assert.False(vehicle.IsMoving);
    }
}
