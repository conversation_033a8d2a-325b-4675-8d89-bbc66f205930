# FleetXQ Database Development Prompts

## Phase 1: Database Setup and EF Core Scaffolding

### Prompt 1.1: Initial Database Setup Validation
**Objective**: Validate the existing FleetXQ database schema and prepare for EF Core scaffolding.

**Context**: 
- Use the provided SQL schema from `docs/complete_sql_setup_for_asynchronous_project.sql`
- Database-first approach is mandatory per PRD requirements
- Target: SQL Server 2019+ with FleetXQ_Assessment database

**Instructions**:
1. Execute the complete SQL setup script and verify all tables are created
2. Validate sample data generation completed successfully
3. Test all views (`VehicleCurrentStatus`, `DailyVehicleSummary`, `VehicleUtilization`, `AlertSummary`)
4. Execute stored procedures (`GetVehicleLocations`, `GetVehiclePerformance`) with sample parameters
5. Verify all indexes and foreign key constraints are properly established

**Expected Deliverables**:
- Database connection string for development environment
- Verification report of all database objects (tables, views, procedures, indexes)
- Sample query results from each view and stored procedure

**Validation Criteria**:
- All 10 tables contain sample data
- Views return meaningful results
- Stored procedures execute without errors
- Performance indexes are active

### Prompt 1.2: EF Core Model Scaffolding
**Objective**: Generate EF Core models using database-first approach with proper configuration.

**Context**:
- Follow Clean Architecture principles from `docs/backend.md`
- Use data annotations approach as specified
- Target .NET 8 with latest EF Core version
- Models should be placed in `Infrastructure` layer

**Instructions**:
1. Install required NuGet packages: `Microsoft.EntityFrameworkCore.SqlServer`, `Microsoft.EntityFrameworkCore.Tools`
2. Execute scaffolding command:
   ```
   dotnet ef dbcontext scaffold "Server=localhost;Database=FleetXQ_Assessment;Trusted_Connection=true;TrustServerCertificate=true;" Microsoft.EntityFrameworkCore.SqlServer -o Models --data-annotations --context-dir Data --context FleetXQDbContext
   ```
3. Review generated models for proper data annotations
4. Verify navigation properties are correctly configured
5. Ensure all entity relationships match database foreign keys
6. Configure DbContext with proper connection string management

**Expected Deliverables**:
- Complete set of entity models with data annotations
- `FleetXQDbContext` class with all DbSets configured
- Connection string configuration in appsettings
- Model validation report

**Validation Criteria**:
- All database tables have corresponding entity models
- Navigation properties correctly represent relationships
- Data annotations match database constraints
- DbContext compiles without errors

### Prompt 1.3: Repository Pattern Implementation
**Objective**: Implement repository interfaces and concrete implementations following Clean Architecture.

**Context**:
- Repository pattern abstracts data access as per `docs/backend.md`
- Interfaces belong in Domain layer, implementations in Infrastructure
- Support async operations throughout
- Include unit of work pattern for transaction management

**Instructions**:
1. Create base repository interface `IRepository<T>` with common CRUD operations
2. Create specific repository interfaces for each aggregate root:
   - `IVehicleRepository`
   - `IDriverRepository`
   - `ITelemetryRepository`
   - `IUserRepository`
   - `IAlertRepository`
3. Implement concrete repositories in Infrastructure layer
4. Create `IUnitOfWork` interface and implementation
5. Configure dependency injection for all repositories
6. Add repository-specific methods for complex queries

**Expected Deliverables**:
- Base repository interface and implementation
- All domain-specific repository interfaces
- Concrete repository implementations with EF Core
- Unit of work pattern implementation
- Dependency injection configuration

**Validation Criteria**:
- All repositories implement async patterns
- Complex queries use proper EF Core techniques
- Unit of work manages transactions correctly
- Repository methods follow naming conventions

## Phase 2: Data Access Optimization

### Prompt 2.1: Query Optimization and Performance
**Objective**: Optimize database queries for real-time telemetry and analytics requirements.

**Context**:
- High-volume telemetry data requires optimized queries
- Real-time dashboard needs sub-second response times
- Analytics queries should leverage existing views and stored procedures

**Instructions**:
1. Implement optimized telemetry data queries using:
   - Proper indexing strategies
   - Pagination for large datasets
   - Projection to DTOs to reduce data transfer
2. Create specialized repository methods for:
   - Latest vehicle positions
   - Real-time telemetry streaming
   - Historical analytics queries
3. Implement caching strategies for frequently accessed data
4. Use compiled queries for repeated operations
5. Leverage database views and stored procedures where appropriate

**Expected Deliverables**:
- Optimized repository methods for telemetry data
- Caching implementation for static/semi-static data
- Performance benchmarks for critical queries
- Query execution plan analysis

**Validation Criteria**:
- Telemetry queries execute under 100ms
- Dashboard data loads under 500ms
- Memory usage remains stable under load
- No N+1 query problems detected

### Prompt 2.2: Real-time Data Streaming Setup
**Objective**: Configure data access layer for real-time telemetry streaming and SignalR integration.

**Context**:
- SignalR requires efficient data streaming from database
- Telemetry data updates frequently and needs push notifications
- Change tracking should trigger real-time updates

**Instructions**:
1. Implement change tracking for critical entities:
   - Vehicle status changes
   - New telemetry data
   - Alert generation
   - Driver assignments
2. Create streaming repository methods for:
   - Live telemetry data feeds
   - Vehicle location updates
   - Alert notifications
3. Configure EF Core change tracking for optimal performance
4. Implement database polling mechanisms where needed
5. Set up proper connection pooling for high-concurrency scenarios

**Expected Deliverables**:
- Change tracking implementation
- Streaming data access methods
- Connection pooling configuration
- Real-time data flow architecture

**Validation Criteria**:
- Change detection works reliably
- Streaming methods handle high frequency updates
- Connection pooling prevents resource exhaustion
- Data consistency maintained during concurrent access

## Phase 3: Data Validation and Testing

### Prompt 3.1: Data Layer Unit Testing
**Objective**: Create comprehensive unit tests for repository layer and data access logic.

**Context**:
- Use xUnit and Moq as specified in `docs/backend.md`
- Test both success and failure scenarios
- Mock EF Core DbContext for unit tests
- Include integration tests with in-memory database

**Instructions**:
1. Set up test project structure:
   - `FleetXQ.Infrastructure.Tests`
   - Separate test classes for each repository
   - Mock configurations for DbContext
2. Create unit tests for each repository method:
   - CRUD operations
   - Complex queries
   - Error handling
   - Async operation testing
3. Implement integration tests using in-memory database
4. Test transaction handling and rollback scenarios
5. Validate data mapping and entity relationships

**Expected Deliverables**:
- Complete unit test suite for all repositories
- Integration tests with in-memory database
- Mock configurations for external dependencies
- Test coverage report (minimum 80%)

**Validation Criteria**:
- All repository methods have corresponding tests
- Tests cover both success and failure paths
- Integration tests validate end-to-end data flow
- Test execution time under 30 seconds

### Prompt 3.2: Data Migration and Seeding Strategy
**Objective**: Implement data migration strategy and seed data management for different environments.

**Context**:
- Database-first approach limits migration options
- Need consistent seed data across environments
- Support for development, testing, and production data sets

**Instructions**:
1. Create data seeding service for:
   - User accounts with proper password hashing
   - Sample vehicles and drivers
   - Test telemetry data for development
   - Geofences and alert configurations
2. Implement environment-specific seeding:
   - Development: Rich sample data
   - Testing: Minimal test data
   - Production: Essential configuration data only
3. Create data validation service to verify:
   - Referential integrity
   - Business rule compliance
   - Data quality standards
4. Implement backup and restore procedures
5. Document data management procedures

**Expected Deliverables**:
- Data seeding service implementation
- Environment-specific seed data sets
- Data validation service
- Backup/restore procedures documentation

**Validation Criteria**:
- Seeding works consistently across environments
- Data validation catches integrity issues
- Backup/restore procedures tested successfully
- Documentation covers all data management aspects