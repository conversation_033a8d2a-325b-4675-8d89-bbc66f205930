using FleetXQ.Domain.Entities;
using FleetXQ.Domain.Enums;
using Xunit;

namespace FleetXQ.Domain.Tests.Entities;

public class DriverTests
{
    [Fact]
    public void Constructor_WithValidParameters_ShouldCreateDriver()
    {
        // Arrange
        var firstName = "John";
        var lastName = "Doe";
        var email = "<EMAIL>";

        // Act
        var driver = new Driver(firstName, lastName, email);

        // Assert
        Assert.Equal(firstName, driver.FirstName);
        Assert.Equal(lastName, driver.LastName);
        Assert.Equal(email, driver.Email);
        Assert.Equal(DriverStatus.Active, driver.Status);
        Assert.Equal("<PERSON>", driver.FullName);
    }

    [Theory]
    [InlineData("", "Doe")]
    [InlineData("John", "")]
    [InlineData(null, "Doe")]
    [InlineData("John", null)]
    public void Constructor_WithInvalidParameters_ShouldThrowArgumentException(string firstName, string lastName)
    {
        // Act & Assert
        Assert.Throws<ArgumentException>(() => new Driver(firstName, lastName));
    }

    [Fact]
    public void UpdateBasicInfo_WithValidParameters_ShouldUpdateProperties()
    {
        // Arrange
        var driver = new Driver("John", "Doe");
        var newFirstName = "Jane";
        var newLastName = "Smith";
        var newEmail = "<EMAIL>";
        var newPhone = "************";
        var newAddress = "123 Main St";

        // Act
        driver.UpdateBasicInfo(newFirstName, newLastName, newEmail, newPhone, newAddress);

        // Assert
        Assert.Equal(newFirstName, driver.FirstName);
        Assert.Equal(newLastName, driver.LastName);
        Assert.Equal(newEmail, driver.Email);
        Assert.Equal(newPhone, driver.Phone);
        Assert.Equal(newAddress, driver.Address);
    }

    [Fact]
    public void UpdateLicenseInfo_WithValidParameters_ShouldUpdateLicenseInfo()
    {
        // Arrange
        var driver = new Driver("John", "Doe");
        var licenseNumber = "D123456789";
        var licenseClass = "Class A";
        var expiryDate = DateTime.UtcNow.AddYears(2);

        // Act
        driver.UpdateLicenseInfo(licenseNumber, licenseClass, expiryDate);

        // Assert
        Assert.Equal(licenseNumber, driver.LicenseNumber);
        Assert.Equal(licenseClass, driver.LicenseClass);
        Assert.Equal(expiryDate, driver.LicenseExpiryDate);
    }

    [Fact]
    public void UpdateLicenseInfo_WithPastExpiryDate_ShouldThrowArgumentException()
    {
        // Arrange
        var driver = new Driver("John", "Doe");
        var pastDate = DateTime.UtcNow.AddDays(-1);

        // Act & Assert
        Assert.Throws<ArgumentException>(() => driver.UpdateLicenseInfo("D123", "Class A", pastDate));
    }

    [Fact]
    public void UpdateEmploymentInfo_WithValidParameters_ShouldUpdateEmploymentInfo()
    {
        // Arrange
        var driver = new Driver("John", "Doe");
        var employeeId = "EMP001";
        var hireDate = DateTime.UtcNow.AddYears(-2);

        // Act
        driver.UpdateEmploymentInfo(employeeId, hireDate);

        // Assert
        Assert.Equal(employeeId, driver.EmployeeId);
        Assert.Equal(hireDate, driver.HireDate);
    }

    [Fact]
    public void UpdateEmploymentInfo_WithFutureHireDate_ShouldThrowArgumentException()
    {
        // Arrange
        var driver = new Driver("John", "Doe");
        var futureDate = DateTime.UtcNow.AddDays(1);

        // Act & Assert
        Assert.Throws<ArgumentException>(() => driver.UpdateEmploymentInfo("EMP001", futureDate));
    }

    [Fact]
    public void UpdateEmergencyContact_WithValidParameters_ShouldUpdateEmergencyContact()
    {
        // Arrange
        var driver = new Driver("John", "Doe");
        var contactName = "Jane Doe";
        var contactPhone = "************";

        // Act
        driver.UpdateEmergencyContact(contactName, contactPhone);

        // Assert
        Assert.Equal(contactName, driver.EmergencyContact);
        Assert.Equal(contactPhone, driver.EmergencyPhone);
    }

    [Fact]
    public void SetDateOfBirth_WithValidDate_ShouldSetDateOfBirth()
    {
        // Arrange
        var driver = new Driver("John", "Doe");
        var dateOfBirth = DateTime.UtcNow.AddYears(-30);

        // Act
        driver.SetDateOfBirth(dateOfBirth);

        // Assert
        Assert.Equal(dateOfBirth, driver.DateOfBirth);
        Assert.Equal(30, driver.Age);
    }

    [Fact]
    public void SetDateOfBirth_WithFutureDate_ShouldThrowArgumentException()
    {
        // Arrange
        var driver = new Driver("John", "Doe");
        var futureDate = DateTime.UtcNow.AddDays(1);

        // Act & Assert
        Assert.Throws<ArgumentException>(() => driver.SetDateOfBirth(futureDate));
    }

    [Fact]
    public void SetDateOfBirth_WithUnderageDate_ShouldThrowArgumentException()
    {
        // Arrange
        var driver = new Driver("John", "Doe");
        var underageDate = DateTime.UtcNow.AddYears(-17);

        // Act & Assert
        Assert.Throws<ArgumentException>(() => driver.SetDateOfBirth(underageDate));
    }

    [Fact]
    public void IsLicenseExpired_WithExpiredLicense_ShouldReturnTrue()
    {
        // Arrange
        var driver = new Driver("John", "Doe");
        var expiredDate = DateTime.UtcNow.AddDays(-1);
        driver.UpdateLicenseInfo("D123", "Class A", expiredDate);

        // Act & Assert
        Assert.True(driver.IsLicenseExpired);
    }

    [Fact]
    public void IsLicenseExpired_WithValidLicense_ShouldReturnFalse()
    {
        // Arrange
        var driver = new Driver("John", "Doe");
        var futureDate = DateTime.UtcNow.AddYears(1);
        driver.UpdateLicenseInfo("D123", "Class A", futureDate);

        // Act & Assert
        Assert.False(driver.IsLicenseExpired);
    }

    [Fact]
    public void IsLicenseExpiringSoon_WithLicenseExpiringIn20Days_ShouldReturnTrue()
    {
        // Arrange
        var driver = new Driver("John", "Doe");
        var soonExpiryDate = DateTime.UtcNow.AddDays(20);
        driver.UpdateLicenseInfo("D123", "Class A", soonExpiryDate);

        // Act & Assert
        Assert.True(driver.IsLicenseExpiringSoon);
    }

    [Fact]
    public void IsLicenseExpiringSoon_WithLicenseExpiringIn40Days_ShouldReturnFalse()
    {
        // Arrange
        var driver = new Driver("John", "Doe");
        var laterExpiryDate = DateTime.UtcNow.AddDays(40);
        driver.UpdateLicenseInfo("D123", "Class A", laterExpiryDate);

        // Act & Assert
        Assert.False(driver.IsLicenseExpiringSoon);
    }

    [Fact]
    public void IsAvailable_WithActiveStatusAndValidLicense_ShouldReturnTrue()
    {
        // Arrange
        var driver = new Driver("John", "Doe");
        var futureDate = DateTime.UtcNow.AddYears(1);
        driver.UpdateLicenseInfo("D123", "Class A", futureDate);

        // Act & Assert
        Assert.True(driver.IsAvailable);
    }

    [Fact]
    public void IsAvailable_WithInactiveStatus_ShouldReturnFalse()
    {
        // Arrange
        var driver = new Driver("John", "Doe");
        driver.ChangeStatus(DriverStatus.Inactive);

        // Act & Assert
        Assert.False(driver.IsAvailable);
    }

    [Fact]
    public void IsAvailable_WithExpiredLicense_ShouldReturnFalse()
    {
        // Arrange
        var driver = new Driver("John", "Doe");
        var expiredDate = DateTime.UtcNow.AddDays(-1);
        driver.UpdateLicenseInfo("D123", "Class A", expiredDate);

        // Act & Assert
        Assert.False(driver.IsAvailable);
    }

    [Fact]
    public void YearsOfService_WithHireDate_ShouldCalculateCorrectly()
    {
        // Arrange
        var driver = new Driver("John", "Doe");
        var hireDate = DateTime.UtcNow.AddYears(-5);
        driver.UpdateEmploymentInfo("EMP001", hireDate);

        // Act
        var yearsOfService = driver.YearsOfService;

        // Assert
        Assert.NotNull(yearsOfService);
        Assert.True(yearsOfService >= 4.9 && yearsOfService <= 5.1); // Allow some tolerance
    }

    [Fact]
    public void CanBeAssignedToVehicle_WithAvailableDriverAndLicense_ShouldReturnTrue()
    {
        // Arrange
        var driver = new Driver("John", "Doe");
        var futureDate = DateTime.UtcNow.AddYears(1);
        driver.UpdateLicenseInfo("D123", "Class A", futureDate);

        // Act & Assert
        Assert.True(driver.CanBeAssignedToVehicle());
    }

    [Fact]
    public void CanBeAssignedToVehicle_WithoutLicense_ShouldReturnFalse()
    {
        // Arrange
        var driver = new Driver("John", "Doe");

        // Act & Assert
        Assert.False(driver.CanBeAssignedToVehicle());
    }

    [Theory]
    [InlineData("truck", "CDL", true)]
    [InlineData("truck", "Class A", true)]
    [InlineData("truck", "Class B", false)]
    [InlineData("van", "Class B", true)]
    [InlineData("van", "Class A", true)]
    [InlineData("car", "Class B", true)]
    [InlineData("car", "Class C", true)]
    [InlineData("motorcycle", "Class M", true)]
    [InlineData("motorcycle", "Class A", true)]
    [InlineData("motorcycle", "Class B", false)]
    public void IsLicenseCompatibleWith_ShouldReturnCorrectCompatibility(string vehicleType, string licenseClass, bool expectedCompatible)
    {
        // Arrange
        var driver = new Driver("John", "Doe");
        var futureDate = DateTime.UtcNow.AddYears(1);
        driver.UpdateLicenseInfo("D123", licenseClass, futureDate);

        // Act
        var isCompatible = driver.IsLicenseCompatibleWith(vehicleType);

        // Assert
        Assert.Equal(expectedCompatible, isCompatible);
    }

    [Fact]
    public void AssociateWithUser_WithValidUserId_ShouldSetUserId()
    {
        // Arrange
        var driver = new Driver("John", "Doe");
        var userId = Guid.NewGuid();

        // Act
        driver.AssociateWithUser(userId);

        // Assert
        Assert.Equal(userId, driver.UserId);
    }

    [Fact]
    public void AssociateWithUser_WithEmptyGuid_ShouldThrowArgumentException()
    {
        // Arrange
        var driver = new Driver("John", "Doe");

        // Act & Assert
        Assert.Throws<ArgumentException>(() => driver.AssociateWithUser(Guid.Empty));
    }

    [Fact]
    public void ChangeStatus_WithDifferentStatus_ShouldUpdateStatus()
    {
        // Arrange
        var driver = new Driver("John", "Doe");
        var newStatus = DriverStatus.OnLeave;

        // Act
        driver.ChangeStatus(newStatus, "Taking vacation");

        // Assert
        Assert.Equal(newStatus, driver.Status);
    }

    [Fact]
    public void ChangeStatus_WithSameStatus_ShouldNotChangeStatus()
    {
        // Arrange
        var driver = new Driver("John", "Doe");
        var currentStatus = driver.Status;

        // Act
        driver.ChangeStatus(currentStatus);

        // Assert
        Assert.Equal(currentStatus, driver.Status);
    }
}
