using FleetXQ.Domain.Entities;
using FleetXQ.Domain.Interfaces;
using MediatR;
using Microsoft.Extensions.Logging;

namespace FleetXQ.Application.Features.Vehicles.Commands.CreateVehicle;

/// <summary>
/// Handler for CreateVehicleCommand
/// </summary>
public sealed class CreateVehicleCommandHandler : IRequestHandler<CreateVehicleCommand, CreateVehicleResult>
{
    private readonly IVehicleRepository _vehicleRepository;
    private readonly ILogger<CreateVehicleCommandHandler> _logger;

    /// <summary>
    /// Initializes a new instance of the <see cref="CreateVehicleCommandHandler"/> class
    /// </summary>
    /// <param name="vehicleRepository">The vehicle repository</param>
    /// <param name="logger">The logger</param>
    public CreateVehicleCommandHandler(
        IVehicleRepository vehicleRepository,
        ILogger<CreateVehicleCommandHandler> logger)
    {
        _vehicleRepository = vehicleRepository ?? throw new ArgumentNullException(nameof(vehicleRepository));
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
    }

    /// <summary>
    /// Handles the CreateVehicleCommand
    /// </summary>
    /// <param name="request">The command request</param>
    /// <param name="cancellationToken">The cancellation token</param>
    /// <returns>The command result</returns>
    public async Task<CreateVehicleResult> Handle(CreateVehicleCommand request, CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogInformation("Creating vehicle with license plate: {LicensePlate}", request.LicensePlate);

            // Check if license plate already exists
            var existingVehicleByPlate = await _vehicleRepository.GetByLicensePlateAsync(request.LicensePlate, cancellationToken);
            if (existingVehicleByPlate != null)
            {
                _logger.LogWarning("Vehicle with license plate {LicensePlate} already exists", request.LicensePlate);
                return CreateVehicleResult.Failed($"A vehicle with license plate '{request.LicensePlate}' already exists");
            }

            // Check if VIN already exists (if provided)
            if (!string.IsNullOrWhiteSpace(request.VIN))
            {
                var existingVehicleByVin = await _vehicleRepository.GetByVinAsync(request.VIN, cancellationToken);
                if (existingVehicleByVin != null)
                {
                    _logger.LogWarning("Vehicle with VIN {VIN} already exists", request.VIN);
                    return CreateVehicleResult.Failed($"A vehicle with VIN '{request.VIN}' already exists");
                }
            }

            // Create the vehicle entity
            var vehicle = new Vehicle(request.VehicleName, request.LicensePlate, request.VehicleType, request.FuelType);

            // Update additional properties if provided
            if (!string.IsNullOrWhiteSpace(request.Brand) || 
                !string.IsNullOrWhiteSpace(request.Model) || 
                request.Year.HasValue || 
                !string.IsNullOrWhiteSpace(request.Color))
            {
                vehicle.UpdateBasicInfo(request.VehicleName, request.Brand, request.Model, request.Year, request.Color);
            }

            // Set VIN if provided
            if (!string.IsNullOrWhiteSpace(request.VIN))
            {
                vehicle.UpdateVIN(request.VIN);
            }

            // Set fuel tank capacity if provided
            if (request.FuelTankCapacity.HasValue)
            {
                vehicle.UpdateFuelTankCapacity(request.FuelTankCapacity.Value);
            }

            // Add to repository
            await _vehicleRepository.AddAsync(vehicle, cancellationToken);

            _logger.LogInformation("Successfully created vehicle with ID: {VehicleId}", vehicle.Id);

            return CreateVehicleResult.Successful(vehicle.Id);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating vehicle with license plate: {LicensePlate}", request.LicensePlate);
            return CreateVehicleResult.Failed($"An error occurred while creating the vehicle: {ex.Message}");
        }
    }
}
