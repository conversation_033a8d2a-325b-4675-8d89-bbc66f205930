using FleetXQ.Application.Features.Vehicles.DTOs;
using FleetXQ.Domain.Enums;
using MediatR;

namespace FleetXQ.Application.Features.Vehicles.Queries.GetVehicleList;

/// <summary>
/// Query to get a list of vehicles with optional filtering
/// </summary>
public sealed class GetVehicleListQuery : IRequest<GetVehicleListResult>
{
    /// <summary>
    /// Gets or sets the vehicle status filter
    /// </summary>
    public VehicleStatus? Status { get; set; }

    /// <summary>
    /// Gets or sets the vehicle type filter
    /// </summary>
    public string? VehicleType { get; set; }

    /// <summary>
    /// Gets or sets a value indicating whether to include only available vehicles
    /// </summary>
    public bool? AvailableOnly { get; set; }

    /// <summary>
    /// Gets or sets the page number for pagination (1-based)
    /// </summary>
    public int PageNumber { get; set; } = 1;

    /// <summary>
    /// Gets or sets the page size for pagination
    /// </summary>
    public int PageSize { get; set; } = 50;

    /// <summary>
    /// Gets or sets the search term for filtering by vehicle name or license plate
    /// </summary>
    public string? SearchTerm { get; set; }

    /// <summary>
    /// Gets or sets the sort field
    /// </summary>
    public string SortBy { get; set; } = "VehicleName";

    /// <summary>
    /// Gets or sets a value indicating whether to sort in descending order
    /// </summary>
    public bool SortDescending { get; set; } = false;
}

/// <summary>
/// Result of getting a vehicle list
/// </summary>
public sealed class GetVehicleListResult
{
    /// <summary>
    /// Gets or sets the list of vehicles
    /// </summary>
    public IEnumerable<VehicleListDto> Vehicles { get; set; } = new List<VehicleListDto>();

    /// <summary>
    /// Gets or sets the total count of vehicles (before pagination)
    /// </summary>
    public int TotalCount { get; set; }

    /// <summary>
    /// Gets or sets the current page number
    /// </summary>
    public int PageNumber { get; set; }

    /// <summary>
    /// Gets or sets the page size
    /// </summary>
    public int PageSize { get; set; }

    /// <summary>
    /// Gets or sets the total number of pages
    /// </summary>
    public int TotalPages { get; set; }

    /// <summary>
    /// Gets or sets a value indicating whether there is a next page
    /// </summary>
    public bool HasNextPage { get; set; }

    /// <summary>
    /// Gets or sets a value indicating whether there is a previous page
    /// </summary>
    public bool HasPreviousPage { get; set; }

    /// <summary>
    /// Gets or sets a value indicating whether the operation was successful
    /// </summary>
    public bool Success { get; set; }

    /// <summary>
    /// Gets or sets the error message if the operation failed
    /// </summary>
    public string? ErrorMessage { get; set; }

    /// <summary>
    /// Creates a successful result
    /// </summary>
    /// <param name="vehicles">The vehicles</param>
    /// <param name="totalCount">The total count</param>
    /// <param name="pageNumber">The page number</param>
    /// <param name="pageSize">The page size</param>
    /// <returns>A successful result</returns>
    public static GetVehicleListResult CreateSuccess(IEnumerable<VehicleListDto> vehicles, int totalCount, int pageNumber, int pageSize)
    {
        var totalPages = (int)Math.Ceiling((double)totalCount / pageSize);
        
        return new GetVehicleListResult
        {
            Vehicles = vehicles,
            TotalCount = totalCount,
            PageNumber = pageNumber,
            PageSize = pageSize,
            TotalPages = totalPages,
            HasNextPage = pageNumber < totalPages,
            HasPreviousPage = pageNumber > 1,
            Success = true
        };
    }

    /// <summary>
    /// Creates a failed result
    /// </summary>
    /// <param name="errorMessage">The error message</param>
    /// <returns>A failed result</returns>
    public static GetVehicleListResult Failed(string errorMessage)
    {
        return new GetVehicleListResult
        {
            Success = false,
            ErrorMessage = errorMessage
        };
    }
}
