using FleetXQ.Domain.Entities;
using FleetXQ.Domain.ValueObjects;

namespace FleetXQ.Domain.Services;

/// <summary>
/// Domain service for fleet analytics and performance calculations
/// </summary>
public interface IFleetAnalyticsService
{
    /// <summary>
    /// Calculates fleet utilization metrics
    /// </summary>
    /// <param name="vehicles">The fleet vehicles</param>
    /// <param name="trips">The trips data</param>
    /// <param name="fromDate">The start date for analysis</param>
    /// <param name="toDate">The end date for analysis</param>
    /// <returns>Fleet utilization metrics</returns>
    FleetUtilizationMetrics CalculateFleetUtilization(IEnumerable<Vehicle> vehicles, 
        IEnumerable<Trip> trips, DateTime fromDate, DateTime toDate);

    /// <summary>
    /// Calculates driver performance metrics
    /// </summary>
    /// <param name="driver">The driver</param>
    /// <param name="trips">The driver's trips</param>
    /// <param name="alerts">The driver's alerts</param>
    /// <param name="fromDate">The start date for analysis</param>
    /// <param name="toDate">The end date for analysis</param>
    /// <returns>Driver performance metrics</returns>
    DriverPerformanceMetrics CalculateDriverPerformance(Driver driver, IEnumerable<Trip> trips, 
        IEnumerable<Alert> alerts, DateTime fromDate, DateTime toDate);

    /// <summary>
    /// Calculates vehicle performance metrics
    /// </summary>
    /// <param name="vehicle">The vehicle</param>
    /// <param name="trips">The vehicle's trips</param>
    /// <param name="alerts">The vehicle's alerts</param>
    /// <param name="fromDate">The start date for analysis</param>
    /// <param name="toDate">The end date for analysis</param>
    /// <returns>Vehicle performance metrics</returns>
    VehiclePerformanceMetrics CalculateVehiclePerformance(Vehicle vehicle, IEnumerable<Trip> trips, 
        IEnumerable<Alert> alerts, DateTime fromDate, DateTime toDate);

    /// <summary>
    /// Calculates fuel efficiency metrics for the fleet
    /// </summary>
    /// <param name="vehicles">The fleet vehicles</param>
    /// <param name="trips">The trips data</param>
    /// <param name="fromDate">The start date for analysis</param>
    /// <param name="toDate">The end date for analysis</param>
    /// <returns>Fuel efficiency metrics</returns>
    FuelEfficiencyMetrics CalculateFuelEfficiency(IEnumerable<Vehicle> vehicles, 
        IEnumerable<Trip> trips, DateTime fromDate, DateTime toDate);

    /// <summary>
    /// Calculates safety metrics for the fleet
    /// </summary>
    /// <param name="drivers">The fleet drivers</param>
    /// <param name="vehicles">The fleet vehicles</param>
    /// <param name="trips">The trips data</param>
    /// <param name="alerts">The alerts data</param>
    /// <param name="fromDate">The start date for analysis</param>
    /// <param name="toDate">The end date for analysis</param>
    /// <returns>Safety metrics</returns>
    SafetyMetrics CalculateSafetyMetrics(IEnumerable<Driver> drivers, IEnumerable<Vehicle> vehicles,
        IEnumerable<Trip> trips, IEnumerable<Alert> alerts, DateTime fromDate, DateTime toDate);

    /// <summary>
    /// Calculates maintenance metrics for the fleet
    /// </summary>
    /// <param name="vehicles">The fleet vehicles</param>
    /// <param name="fromDate">The start date for analysis</param>
    /// <param name="toDate">The end date for analysis</param>
    /// <returns>Maintenance metrics</returns>
    MaintenanceMetrics CalculateMaintenanceMetrics(IEnumerable<Vehicle> vehicles, 
        DateTime fromDate, DateTime toDate);

    /// <summary>
    /// Generates fleet performance dashboard data
    /// </summary>
    /// <param name="vehicles">The fleet vehicles</param>
    /// <param name="drivers">The fleet drivers</param>
    /// <param name="trips">The trips data</param>
    /// <param name="alerts">The alerts data</param>
    /// <returns>Dashboard data</returns>
    FleetDashboardData GenerateDashboardData(IEnumerable<Vehicle> vehicles, IEnumerable<Driver> drivers,
        IEnumerable<Trip> trips, IEnumerable<Alert> alerts);

    /// <summary>
    /// Identifies performance trends and patterns
    /// </summary>
    /// <param name="historicalData">Historical performance data</param>
    /// <param name="currentData">Current performance data</param>
    /// <returns>Performance trends analysis</returns>
    PerformanceTrends AnalyzePerformanceTrends(IEnumerable<FleetPerformanceSnapshot> historicalData,
        FleetPerformanceSnapshot currentData);

    /// <summary>
    /// Calculates cost metrics for the fleet
    /// </summary>
    /// <param name="vehicles">The fleet vehicles</param>
    /// <param name="trips">The trips data</param>
    /// <param name="fuelTransactions">Fuel transaction data</param>
    /// <param name="fromDate">The start date for analysis</param>
    /// <param name="toDate">The end date for analysis</param>
    /// <returns>Cost metrics</returns>
    CostMetrics CalculateCostMetrics(IEnumerable<Vehicle> vehicles, IEnumerable<Trip> trips,
        IEnumerable<object> fuelTransactions, DateTime fromDate, DateTime toDate);

    /// <summary>
    /// Generates optimization recommendations
    /// </summary>
    /// <param name="fleetData">Current fleet data</param>
    /// <param name="performanceMetrics">Performance metrics</param>
    /// <returns>Optimization recommendations</returns>
    IEnumerable<OptimizationRecommendation> GenerateOptimizationRecommendations(
        FleetDashboardData fleetData, object performanceMetrics);
}

/// <summary>
/// Represents fleet utilization metrics
/// </summary>
public class FleetUtilizationMetrics
{
    public int TotalVehicles { get; set; }
    public int ActiveVehicles { get; set; }
    public double UtilizationRate { get; set; }
    public double AverageDistancePerVehicle { get; set; }
    public double AverageHoursPerVehicle { get; set; }
    public int IdleVehicles { get; set; }
    public double IdleRate { get; set; }
    public Dictionary<string, int> VehiclesByType { get; set; } = new();
    public Dictionary<string, double> UtilizationByType { get; set; } = new();
}

/// <summary>
/// Represents driver performance metrics
/// </summary>
public class DriverPerformanceMetrics
{
    public Guid DriverId { get; set; }
    public string DriverName { get; set; } = string.Empty;
    public int TotalTrips { get; set; }
    public decimal TotalDistance { get; set; }
    public double TotalHours { get; set; }
    public decimal AverageSpeed { get; set; }
    public decimal FuelEfficiency { get; set; }
    public int SafetyScore { get; set; }
    public int HarshEvents { get; set; }
    public int AlertsGenerated { get; set; }
    public double OnTimePerformance { get; set; }
    public int PerformanceRank { get; set; }
    public List<string> StrengthAreas { get; set; } = new();
    public List<string> ImprovementAreas { get; set; } = new();
}

/// <summary>
/// Represents vehicle performance metrics
/// </summary>
public class VehiclePerformanceMetrics
{
    public Guid VehicleId { get; set; }
    public string VehicleName { get; set; } = string.Empty;
    public int TotalTrips { get; set; }
    public decimal TotalDistance { get; set; }
    public double TotalHours { get; set; }
    public double UtilizationRate { get; set; }
    public decimal FuelEfficiency { get; set; }
    public int MaintenanceEvents { get; set; }
    public decimal MaintenanceCost { get; set; }
    public int AlertsGenerated { get; set; }
    public double ReliabilityScore { get; set; }
    public double CostPerKilometer { get; set; }
    public DateTime? LastMaintenanceDate { get; set; }
    public DateTime? NextMaintenanceDate { get; set; }
}

/// <summary>
/// Represents fuel efficiency metrics
/// </summary>
public class FuelEfficiencyMetrics
{
    public decimal TotalFuelConsumed { get; set; }
    public decimal TotalDistance { get; set; }
    public decimal AverageFuelEfficiency { get; set; }
    public decimal BestFuelEfficiency { get; set; }
    public decimal WorstFuelEfficiency { get; set; }
    public Dictionary<string, decimal> EfficiencyByVehicleType { get; set; } = new();
    public Dictionary<Guid, decimal> EfficiencyByVehicle { get; set; } = new();
    public Dictionary<Guid, decimal> EfficiencyByDriver { get; set; } = new();
    public decimal FuelCostTotal { get; set; }
    public decimal FuelCostPerKilometer { get; set; }
}

/// <summary>
/// Represents safety metrics
/// </summary>
public class SafetyMetrics
{
    public int TotalTrips { get; set; }
    public int SafeTrips { get; set; }
    public double SafetyRate { get; set; }
    public int TotalHarshEvents { get; set; }
    public double HarshEventsPerTrip { get; set; }
    public int SpeedingIncidents { get; set; }
    public int CriticalAlerts { get; set; }
    public Dictionary<Guid, int> SafetyScoreByDriver { get; set; } = new();
    public Dictionary<string, int> IncidentsByType { get; set; } = new();
    public double AccidentRate { get; set; }
    public int DaysWithoutIncident { get; set; }
}

/// <summary>
/// Represents maintenance metrics
/// </summary>
public class MaintenanceMetrics
{
    public int VehiclesDueMaintenance { get; set; }
    public int VehiclesOverdueMaintenance { get; set; }
    public decimal TotalMaintenanceCost { get; set; }
    public decimal AverageMaintenanceCostPerVehicle { get; set; }
    public double AverageMaintenanceInterval { get; set; }
    public Dictionary<string, int> MaintenanceByType { get; set; } = new();
    public Dictionary<Guid, decimal> MaintenanceCostByVehicle { get; set; } = new();
    public double PreventiveMaintenanceRate { get; set; }
    public double VehicleAvailabilityRate { get; set; }
}

/// <summary>
/// Represents fleet dashboard data
/// </summary>
public class FleetDashboardData
{
    public FleetUtilizationMetrics Utilization { get; set; } = new();
    public FuelEfficiencyMetrics FuelEfficiency { get; set; } = new();
    public SafetyMetrics Safety { get; set; } = new();
    public MaintenanceMetrics Maintenance { get; set; } = new();
    public List<DriverPerformanceMetrics> TopDrivers { get; set; } = new();
    public List<VehiclePerformanceMetrics> TopVehicles { get; set; } = new();
    public List<Alert> RecentAlerts { get; set; } = new();
    public Dictionary<string, object> KPIs { get; set; } = new();
    public DateTime GeneratedAt { get; set; } = DateTime.UtcNow;
}

/// <summary>
/// Represents a fleet performance snapshot
/// </summary>
public class FleetPerformanceSnapshot
{
    public DateTime Date { get; set; }
    public double UtilizationRate { get; set; }
    public decimal FuelEfficiency { get; set; }
    public double SafetyScore { get; set; }
    public decimal TotalCost { get; set; }
    public int ActiveVehicles { get; set; }
    public int ActiveDrivers { get; set; }
    public Dictionary<string, object> AdditionalMetrics { get; set; } = new();
}

/// <summary>
/// Represents performance trends analysis
/// </summary>
public class PerformanceTrends
{
    public TrendDirection UtilizationTrend { get; set; }
    public TrendDirection FuelEfficiencyTrend { get; set; }
    public TrendDirection SafetyTrend { get; set; }
    public TrendDirection CostTrend { get; set; }
    public List<string> KeyInsights { get; set; } = new();
    public List<string> Recommendations { get; set; } = new();
    public Dictionary<string, TrendData> DetailedTrends { get; set; } = new();
}

/// <summary>
/// Represents cost metrics
/// </summary>
public class CostMetrics
{
    public decimal TotalOperatingCost { get; set; }
    public decimal FuelCost { get; set; }
    public decimal MaintenanceCost { get; set; }
    public decimal CostPerKilometer { get; set; }
    public decimal CostPerHour { get; set; }
    public Dictionary<Guid, decimal> CostByVehicle { get; set; } = new();
    public Dictionary<string, decimal> CostByCategory { get; set; } = new();
    public decimal BudgetVariance { get; set; }
    public decimal ProjectedMonthlyCost { get; set; }
}

/// <summary>
/// Represents an optimization recommendation
/// </summary>
public class OptimizationRecommendation
{
    public string Title { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public RecommendationType Type { get; set; }
    public RecommendationPriority Priority { get; set; }
    public decimal EstimatedSavings { get; set; }
    public string Impact { get; set; } = string.Empty;
    public List<string> ActionItems { get; set; } = new();
    public DateTime RecommendedBy { get; set; } = DateTime.UtcNow;
}

/// <summary>
/// Represents trend direction
/// </summary>
public enum TrendDirection
{
    Improving,
    Stable,
    Declining,
    Unknown
}

/// <summary>
/// Represents trend data
/// </summary>
public class TrendData
{
    public TrendDirection Direction { get; set; }
    public double PercentageChange { get; set; }
    public string Description { get; set; } = string.Empty;
}

/// <summary>
/// Represents recommendation type
/// </summary>
public enum RecommendationType
{
    Efficiency,
    Safety,
    Cost,
    Maintenance,
    Utilization
}
