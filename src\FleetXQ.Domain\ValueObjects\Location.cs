using FleetXQ.Domain.Common;

namespace FleetXQ.Domain.ValueObjects;

/// <summary>
/// Represents a geographical location with latitude and longitude coordinates
/// </summary>
public sealed class Location : ValueObject
{
    /// <summary>
    /// Gets the latitude coordinate in decimal degrees
    /// </summary>
    public decimal Latitude { get; }

    /// <summary>
    /// Gets the longitude coordinate in decimal degrees
    /// </summary>
    public decimal Longitude { get; }

    /// <summary>
    /// Initializes a new instance of the <see cref="Location"/> class
    /// </summary>
    /// <param name="latitude">The latitude coordinate in decimal degrees (-90 to 90)</param>
    /// <param name="longitude">The longitude coordinate in decimal degrees (-180 to 180)</param>
    /// <exception cref="ArgumentOutOfRangeException">Thrown when latitude or longitude are out of valid range</exception>
    public Location(decimal latitude, decimal longitude)
    {
        if (latitude < -90 || latitude > 90)
        {
            throw new ArgumentOutOfRangeException(nameof(latitude), latitude, "Latitude must be between -90 and 90 degrees");
        }

        if (longitude < -180 || longitude > 180)
        {
            throw new ArgumentOutOfRangeException(nameof(longitude), longitude, "Longitude must be between -180 and 180 degrees");
        }

        Latitude = latitude;
        Longitude = longitude;
    }

    /// <summary>
    /// Calculates the distance between this location and another location using the Haversine formula
    /// </summary>
    /// <param name="other">The other location</param>
    /// <returns>The distance in kilometers</returns>
    public double DistanceTo(Location other)
    {
        if (other == null)
        {
            throw new ArgumentNullException(nameof(other));
        }

        const double earthRadiusKm = 6371.0;
        
        var lat1Rad = (double)Latitude * Math.PI / 180.0;
        var lat2Rad = (double)other.Latitude * Math.PI / 180.0;
        var deltaLatRad = ((double)other.Latitude - (double)Latitude) * Math.PI / 180.0;
        var deltaLonRad = ((double)other.Longitude - (double)Longitude) * Math.PI / 180.0;

        var a = Math.Sin(deltaLatRad / 2) * Math.Sin(deltaLatRad / 2) +
                Math.Cos(lat1Rad) * Math.Cos(lat2Rad) *
                Math.Sin(deltaLonRad / 2) * Math.Sin(deltaLonRad / 2);
        
        var c = 2 * Math.Atan2(Math.Sqrt(a), Math.Sqrt(1 - a));
        
        return earthRadiusKm * c;
    }

    /// <summary>
    /// Determines if this location is within a specified radius of another location
    /// </summary>
    /// <param name="other">The other location</param>
    /// <param name="radiusKm">The radius in kilometers</param>
    /// <returns>True if within the radius, false otherwise</returns>
    public bool IsWithinRadius(Location other, double radiusKm)
    {
        if (other == null)
        {
            throw new ArgumentNullException(nameof(other));
        }

        if (radiusKm < 0)
        {
            throw new ArgumentOutOfRangeException(nameof(radiusKm), radiusKm, "Radius must be non-negative");
        }

        return DistanceTo(other) <= radiusKm;
    }

    /// <summary>
    /// Creates a Location from double values
    /// </summary>
    /// <param name="latitude">The latitude coordinate</param>
    /// <param name="longitude">The longitude coordinate</param>
    /// <returns>A new Location instance</returns>
    public static Location FromDouble(double latitude, double longitude)
    {
        return new Location((decimal)latitude, (decimal)longitude);
    }

    /// <summary>
    /// Gets the atomic values that define the value object's equality
    /// </summary>
    /// <returns>An enumerable of atomic values</returns>
    protected override IEnumerable<object> GetEqualityComponents()
    {
        yield return Latitude;
        yield return Longitude;
    }

    /// <summary>
    /// Returns a string representation of the location
    /// </summary>
    /// <returns>A string in the format "Latitude, Longitude"</returns>
    public override string ToString()
    {
        return $"{Latitude}, {Longitude}";
    }
}
