using FleetXQ.Application.Features.Vehicles.DTOs;
using FleetXQ.Domain.Enums;

namespace FleetXQ.Application.Features.Alerts.DTOs;

/// <summary>
/// Data transfer object for alert information
/// </summary>
public class AlertDto
{
    /// <summary>
    /// Gets or sets the alert ID
    /// </summary>
    public Guid Id { get; set; }

    /// <summary>
    /// Gets or sets the alert type
    /// </summary>
    public AlertType Type { get; set; }

    /// <summary>
    /// Gets or sets the alert severity
    /// </summary>
    public AlertSeverity Severity { get; set; }

    /// <summary>
    /// Gets or sets the alert status
    /// </summary>
    public AlertStatus Status { get; set; }

    /// <summary>
    /// Gets or sets the alert title
    /// </summary>
    public string Title { get; set; } = string.Empty;

    /// <summary>
    /// Gets or sets the alert description
    /// </summary>
    public string Description { get; set; } = string.Empty;

    /// <summary>
    /// Gets or sets the vehicle ID
    /// </summary>
    public Guid? VehicleId { get; set; }

    /// <summary>
    /// Gets or sets the vehicle name
    /// </summary>
    public string? VehicleName { get; set; }

    /// <summary>
    /// Gets or sets the driver ID
    /// </summary>
    public Guid? DriverId { get; set; }

    /// <summary>
    /// Gets or sets the driver name
    /// </summary>
    public string? DriverName { get; set; }

    /// <summary>
    /// Gets or sets the location where the alert occurred
    /// </summary>
    public LocationDto? Location { get; set; }

    /// <summary>
    /// Gets or sets the timestamp when the alert was triggered
    /// </summary>
    public DateTime TriggeredAt { get; set; }

    /// <summary>
    /// Gets or sets the timestamp when the alert was acknowledged
    /// </summary>
    public DateTime? AcknowledgedAt { get; set; }

    /// <summary>
    /// Gets or sets the user who acknowledged the alert
    /// </summary>
    public string? AcknowledgedBy { get; set; }

    /// <summary>
    /// Gets or sets the timestamp when the alert was resolved
    /// </summary>
    public DateTime? ResolvedAt { get; set; }

    /// <summary>
    /// Gets or sets the user who resolved the alert
    /// </summary>
    public string? ResolvedBy { get; set; }

    /// <summary>
    /// Gets or sets additional alert data
    /// </summary>
    public Dictionary<string, object>? AdditionalData { get; set; }

    /// <summary>
    /// Gets or sets a value indicating whether the alert requires immediate attention
    /// </summary>
    public bool RequiresImmediateAttention { get; set; }

    /// <summary>
    /// Gets or sets the date when the alert was created
    /// </summary>
    public DateTime CreatedAt { get; set; }

    /// <summary>
    /// Gets or sets the date when the alert was last updated
    /// </summary>
    public DateTime UpdatedAt { get; set; }
}

/// <summary>
/// Data transfer object for alert list information (summary view)
/// </summary>
public class AlertListDto
{
    /// <summary>
    /// Gets or sets the alert ID
    /// </summary>
    public Guid Id { get; set; }

    /// <summary>
    /// Gets or sets the alert type
    /// </summary>
    public AlertType Type { get; set; }

    /// <summary>
    /// Gets or sets the alert severity
    /// </summary>
    public AlertSeverity Severity { get; set; }

    /// <summary>
    /// Gets or sets the alert status
    /// </summary>
    public AlertStatus Status { get; set; }

    /// <summary>
    /// Gets or sets the alert title
    /// </summary>
    public string Title { get; set; } = string.Empty;

    /// <summary>
    /// Gets or sets the vehicle name
    /// </summary>
    public string? VehicleName { get; set; }

    /// <summary>
    /// Gets or sets the driver name
    /// </summary>
    public string? DriverName { get; set; }

    /// <summary>
    /// Gets or sets the timestamp when the alert was triggered
    /// </summary>
    public DateTime TriggeredAt { get; set; }

    /// <summary>
    /// Gets or sets a value indicating whether the alert requires immediate attention
    /// </summary>
    public bool RequiresImmediateAttention { get; set; }
}
