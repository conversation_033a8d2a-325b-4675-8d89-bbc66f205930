using FleetXQ.Domain.ValueObjects;
using Xunit;

namespace FleetXQ.Domain.Tests.ValueObjects;

public class LocationTests
{
    [Fact]
    public void Constructor_WithValidCoordinates_ShouldCreateLocation()
    {
        // Arrange
        var latitude = 40.7128m;
        var longitude = -74.0060m;

        // Act
        var location = new Location(latitude, longitude);

        // Assert
        Assert.Equal(latitude, location.Latitude);
        Assert.Equal(longitude, location.Longitude);
    }

    [Theory]
    [InlineData(-91, 0)]
    [InlineData(91, 0)]
    [InlineData(0, -181)]
    [InlineData(0, 181)]
    public void Constructor_WithInvalidCoordinates_ShouldThrowArgumentOutOfRangeException(decimal latitude, decimal longitude)
    {
        // Act & Assert
        Assert.Throws<ArgumentOutOfRangeException>(() => new Location(latitude, longitude));
    }

    [Fact]
    public void DistanceTo_WithValidLocation_ShouldCalculateCorrectDistance()
    {
        // Arrange
        var location1 = new Location(40.7128m, -74.0060m); // New York
        var location2 = new Location(34.0522m, -118.2437m); // Los Angeles
        var expectedDistance = 3944.0; // Approximate distance in km

        // Act
        var distance = location1.DistanceTo(location2);

        // Assert
        Assert.True(Math.Abs(distance - expectedDistance) < 50); // Allow 50km tolerance
    }

    [Fact]
    public void DistanceTo_WithSameLocation_ShouldReturnZero()
    {
        // Arrange
        var location = new Location(40.7128m, -74.0060m);

        // Act
        var distance = location.DistanceTo(location);

        // Assert
        Assert.Equal(0, distance, 1); // Allow 1km tolerance for floating point precision
    }

    [Fact]
    public void DistanceTo_WithNullLocation_ShouldThrowArgumentNullException()
    {
        // Arrange
        var location = new Location(40.7128m, -74.0060m);

        // Act & Assert
        Assert.Throws<ArgumentNullException>(() => location.DistanceTo(null!));
    }

    [Fact]
    public void IsWithinRadius_WithLocationWithinRadius_ShouldReturnTrue()
    {
        // Arrange
        var location1 = new Location(40.7128m, -74.0060m);
        var location2 = new Location(40.7589m, -73.9851m); // About 8km away
        var radius = 10.0;

        // Act
        var result = location1.IsWithinRadius(location2, radius);

        // Assert
        Assert.True(result);
    }

    [Fact]
    public void IsWithinRadius_WithLocationOutsideRadius_ShouldReturnFalse()
    {
        // Arrange
        var location1 = new Location(40.7128m, -74.0060m);
        var location2 = new Location(34.0522m, -118.2437m); // About 3944km away
        var radius = 100.0;

        // Act
        var result = location1.IsWithinRadius(location2, radius);

        // Assert
        Assert.False(result);
    }

    [Fact]
    public void IsWithinRadius_WithNegativeRadius_ShouldThrowArgumentOutOfRangeException()
    {
        // Arrange
        var location1 = new Location(40.7128m, -74.0060m);
        var location2 = new Location(40.7589m, -73.9851m);

        // Act & Assert
        Assert.Throws<ArgumentOutOfRangeException>(() => location1.IsWithinRadius(location2, -1.0));
    }

    [Fact]
    public void FromDouble_WithValidValues_ShouldCreateLocation()
    {
        // Arrange
        var latitude = 40.7128;
        var longitude = -74.0060;

        // Act
        var location = Location.FromDouble(latitude, longitude);

        // Assert
        Assert.Equal((decimal)latitude, location.Latitude);
        Assert.Equal((decimal)longitude, location.Longitude);
    }

    [Fact]
    public void Equals_WithSameCoordinates_ShouldReturnTrue()
    {
        // Arrange
        var location1 = new Location(40.7128m, -74.0060m);
        var location2 = new Location(40.7128m, -74.0060m);

        // Act & Assert
        Assert.Equal(location1, location2);
        Assert.True(location1 == location2);
        Assert.False(location1 != location2);
    }

    [Fact]
    public void Equals_WithDifferentCoordinates_ShouldReturnFalse()
    {
        // Arrange
        var location1 = new Location(40.7128m, -74.0060m);
        var location2 = new Location(34.0522m, -118.2437m);

        // Act & Assert
        Assert.NotEqual(location1, location2);
        Assert.False(location1 == location2);
        Assert.True(location1 != location2);
    }

    [Fact]
    public void GetHashCode_WithSameCoordinates_ShouldReturnSameHashCode()
    {
        // Arrange
        var location1 = new Location(40.7128m, -74.0060m);
        var location2 = new Location(40.7128m, -74.0060m);

        // Act & Assert
        Assert.Equal(location1.GetHashCode(), location2.GetHashCode());
    }

    [Fact]
    public void ToString_ShouldReturnFormattedString()
    {
        // Arrange
        var location = new Location(40.7128m, -74.0060m);
        var expected = "40.7128, -74.0060";

        // Act
        var result = location.ToString();

        // Assert
        Assert.Equal(expected, result);
    }
}
