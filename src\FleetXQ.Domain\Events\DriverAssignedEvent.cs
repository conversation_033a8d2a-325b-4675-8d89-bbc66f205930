using FleetXQ.Domain.Common;

namespace FleetXQ.Domain.Events;

/// <summary>
/// Domain event raised when a driver is assigned to a vehicle
/// </summary>
public sealed class DriverAssignedEvent : BaseDomainEvent
{
    /// <summary>
    /// Gets the assignment ID
    /// </summary>
    public Guid AssignmentId { get; }

    /// <summary>
    /// Gets the vehicle ID
    /// </summary>
    public Guid VehicleId { get; }

    /// <summary>
    /// Gets the driver ID
    /// </summary>
    public Guid DriverId { get; }

    /// <summary>
    /// Gets the assignment type
    /// </summary>
    public AssignmentType AssignmentType { get; }

    /// <summary>
    /// Gets the assignment start date
    /// </summary>
    public DateTime AssignedDate { get; }

    /// <summary>
    /// Gets the assignment end date (if temporary)
    /// </summary>
    public DateTime? EndDate { get; }

    /// <summary>
    /// Gets the user who made the assignment
    /// </summary>
    public Guid? AssignedBy { get; }

    /// <summary>
    /// Gets the reason for the assignment
    /// </summary>
    public string? Reason { get; }

    /// <summary>
    /// Gets additional notes about the assignment
    /// </summary>
    public string? Notes { get; }

    /// <summary>
    /// Gets the previous driver ID (if this is a reassignment)
    /// </summary>
    public Guid? PreviousDriverId { get; }

    /// <summary>
    /// Gets additional context about the assignment
    /// </summary>
    public Dictionary<string, object> Context { get; }

    /// <summary>
    /// Gets a value indicating whether this is a reassignment
    /// </summary>
    public bool IsReassignment => PreviousDriverId.HasValue;

    /// <summary>
    /// Gets a value indicating whether this is a temporary assignment
    /// </summary>
    public bool IsTemporary => AssignmentType == AssignmentType.Temporary;

    /// <summary>
    /// Gets a value indicating whether the assignment is automated
    /// </summary>
    public bool IsAutomated => AssignedBy == null;

    /// <summary>
    /// Initializes a new instance of the <see cref="DriverAssignedEvent"/> class
    /// </summary>
    /// <param name="assignmentId">The assignment ID</param>
    /// <param name="vehicleId">The vehicle ID</param>
    /// <param name="driverId">The driver ID</param>
    /// <param name="assignmentType">The assignment type</param>
    /// <param name="assignedDate">The assignment date</param>
    /// <param name="endDate">The assignment end date</param>
    /// <param name="assignedBy">The user who made the assignment</param>
    /// <param name="reason">The reason for the assignment</param>
    /// <param name="notes">Additional notes</param>
    /// <param name="previousDriverId">The previous driver ID</param>
    /// <param name="context">Additional context</param>
    public DriverAssignedEvent(Guid assignmentId, Guid vehicleId, Guid driverId, AssignmentType assignmentType,
        DateTime assignedDate, DateTime? endDate = null, Guid? assignedBy = null, string? reason = null,
        string? notes = null, Guid? previousDriverId = null, Dictionary<string, object>? context = null)
    {
        if (assignmentId == Guid.Empty)
            throw new ArgumentException("Assignment ID cannot be empty", nameof(assignmentId));
        
        if (vehicleId == Guid.Empty)
            throw new ArgumentException("Vehicle ID cannot be empty", nameof(vehicleId));
        
        if (driverId == Guid.Empty)
            throw new ArgumentException("Driver ID cannot be empty", nameof(driverId));

        if (endDate.HasValue && endDate.Value <= assignedDate)
            throw new ArgumentException("End date must be after assigned date", nameof(endDate));

        AssignmentId = assignmentId;
        VehicleId = vehicleId;
        DriverId = driverId;
        AssignmentType = assignmentType;
        AssignedDate = assignedDate;
        EndDate = endDate;
        AssignedBy = assignedBy;
        Reason = reason;
        Notes = notes;
        PreviousDriverId = previousDriverId;
        Context = context ?? new Dictionary<string, object>();
    }

    /// <summary>
    /// Creates a driver assigned event for a primary assignment
    /// </summary>
    /// <param name="assignmentId">The assignment ID</param>
    /// <param name="vehicleId">The vehicle ID</param>
    /// <param name="driverId">The driver ID</param>
    /// <param name="assignedBy">The user who made the assignment</param>
    /// <param name="reason">The reason for the assignment</param>
    /// <param name="previousDriverId">The previous driver ID</param>
    /// <returns>A new DriverAssignedEvent</returns>
    public static DriverAssignedEvent ForPrimaryAssignment(Guid assignmentId, Guid vehicleId, Guid driverId,
        Guid? assignedBy = null, string? reason = null, Guid? previousDriverId = null)
    {
        var context = new Dictionary<string, object>
        {
            ["assignmentCategory"] = "primary",
            ["automated"] = assignedBy == null
        };

        return new DriverAssignedEvent(assignmentId, vehicleId, driverId, AssignmentType.Primary,
            DateTime.UtcNow, null, assignedBy, reason ?? "Primary driver assignment", null, previousDriverId, context);
    }

    /// <summary>
    /// Creates a driver assigned event for a secondary assignment
    /// </summary>
    /// <param name="assignmentId">The assignment ID</param>
    /// <param name="vehicleId">The vehicle ID</param>
    /// <param name="driverId">The driver ID</param>
    /// <param name="assignedBy">The user who made the assignment</param>
    /// <param name="reason">The reason for the assignment</param>
    /// <returns>A new DriverAssignedEvent</returns>
    public static DriverAssignedEvent ForSecondaryAssignment(Guid assignmentId, Guid vehicleId, Guid driverId,
        Guid? assignedBy = null, string? reason = null)
    {
        var context = new Dictionary<string, object>
        {
            ["assignmentCategory"] = "secondary",
            ["automated"] = assignedBy == null
        };

        return new DriverAssignedEvent(assignmentId, vehicleId, driverId, AssignmentType.Secondary,
            DateTime.UtcNow, null, assignedBy, reason ?? "Secondary driver assignment", null, null, context);
    }

    /// <summary>
    /// Creates a driver assigned event for a temporary assignment
    /// </summary>
    /// <param name="assignmentId">The assignment ID</param>
    /// <param name="vehicleId">The vehicle ID</param>
    /// <param name="driverId">The driver ID</param>
    /// <param name="endDate">The assignment end date</param>
    /// <param name="assignedBy">The user who made the assignment</param>
    /// <param name="reason">The reason for the assignment</param>
    /// <returns>A new DriverAssignedEvent</returns>
    public static DriverAssignedEvent ForTemporaryAssignment(Guid assignmentId, Guid vehicleId, Guid driverId,
        DateTime endDate, Guid? assignedBy = null, string? reason = null)
    {
        var context = new Dictionary<string, object>
        {
            ["assignmentCategory"] = "temporary",
            ["duration"] = (endDate - DateTime.UtcNow).TotalHours,
            ["automated"] = assignedBy == null
        };

        return new DriverAssignedEvent(assignmentId, vehicleId, driverId, AssignmentType.Temporary,
            DateTime.UtcNow, endDate, assignedBy, reason ?? "Temporary driver assignment", null, null, context);
    }

    /// <summary>
    /// Creates a driver assigned event for an emergency assignment
    /// </summary>
    /// <param name="assignmentId">The assignment ID</param>
    /// <param name="vehicleId">The vehicle ID</param>
    /// <param name="driverId">The driver ID</param>
    /// <param name="emergencyReason">The emergency reason</param>
    /// <param name="assignedBy">The user who made the assignment</param>
    /// <param name="previousDriverId">The previous driver ID</param>
    /// <returns>A new DriverAssignedEvent</returns>
    public static DriverAssignedEvent ForEmergencyAssignment(Guid assignmentId, Guid vehicleId, Guid driverId,
        string emergencyReason, Guid? assignedBy = null, Guid? previousDriverId = null)
    {
        var context = new Dictionary<string, object>
        {
            ["assignmentCategory"] = "emergency",
            ["emergencyReason"] = emergencyReason,
            ["automated"] = assignedBy == null,
            ["priority"] = "high"
        };

        return new DriverAssignedEvent(assignmentId, vehicleId, driverId, AssignmentType.Primary,
            DateTime.UtcNow, null, assignedBy, $"Emergency assignment: {emergencyReason}", null, previousDriverId, context);
    }

    /// <summary>
    /// Gets a human-readable description of the assignment
    /// </summary>
    /// <returns>A description of the assignment</returns>
    public string GetDescription()
    {
        var description = $"Driver assigned to vehicle ({AssignmentType.ToString().ToLower()})";
        
        if (IsReassignment)
        {
            description = $"Driver reassigned to vehicle ({AssignmentType.ToString().ToLower()})";
        }

        if (IsTemporary && EndDate.HasValue)
        {
            var duration = EndDate.Value - AssignedDate;
            description += $" for {duration.TotalHours:F1} hours";
        }

        if (!string.IsNullOrWhiteSpace(Reason))
        {
            description += $": {Reason}";
        }

        return description;
    }

    /// <summary>
    /// Gets the priority level of the assignment
    /// </summary>
    /// <returns>The priority level</returns>
    public AssignmentPriority GetPriority()
    {
        if (Context.ContainsKey("priority") && Context["priority"].ToString() == "high")
            return AssignmentPriority.High;

        return AssignmentType switch
        {
            AssignmentType.Primary => AssignmentPriority.High,
            AssignmentType.Secondary => AssignmentPriority.Medium,
            AssignmentType.Temporary => AssignmentPriority.Low,
            _ => AssignmentPriority.Medium
        };
    }

    /// <summary>
    /// Checks if the assignment conflicts with another assignment
    /// </summary>
    /// <param name="otherAssignment">The other assignment to check against</param>
    /// <returns>True if there's a conflict</returns>
    public bool ConflictsWith(DriverAssignedEvent otherAssignment)
    {
        if (otherAssignment == null)
            return false;

        // Same driver assigned to different vehicles at the same time
        if (DriverId == otherAssignment.DriverId && VehicleId != otherAssignment.VehicleId)
        {
            var thisEnd = EndDate ?? DateTime.MaxValue;
            var otherEnd = otherAssignment.EndDate ?? DateTime.MaxValue;

            return AssignedDate < otherEnd && thisEnd > otherAssignment.AssignedDate;
        }

        // Same vehicle assigned to different drivers at the same time (primary assignments)
        if (VehicleId == otherAssignment.VehicleId && DriverId != otherAssignment.DriverId &&
            AssignmentType == AssignmentType.Primary && otherAssignment.AssignmentType == AssignmentType.Primary)
        {
            var thisEnd = EndDate ?? DateTime.MaxValue;
            var otherEnd = otherAssignment.EndDate ?? DateTime.MaxValue;

            return AssignedDate < otherEnd && thisEnd > otherAssignment.AssignedDate;
        }

        return false;
    }
}

/// <summary>
/// Represents the type of driver assignment
/// </summary>
public enum AssignmentType
{
    /// <summary>
    /// Primary driver assignment
    /// </summary>
    Primary,

    /// <summary>
    /// Secondary driver assignment
    /// </summary>
    Secondary,

    /// <summary>
    /// Temporary driver assignment
    /// </summary>
    Temporary
}

/// <summary>
/// Represents the priority level of an assignment
/// </summary>
public enum AssignmentPriority
{
    /// <summary>
    /// Low priority assignment
    /// </summary>
    Low,

    /// <summary>
    /// Medium priority assignment
    /// </summary>
    Medium,

    /// <summary>
    /// High priority assignment
    /// </summary>
    High
}
