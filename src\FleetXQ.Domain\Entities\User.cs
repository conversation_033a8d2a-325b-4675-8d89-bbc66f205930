using FleetXQ.Domain.Common;
using FleetXQ.Domain.Enums;

namespace FleetXQ.Domain.Entities;

/// <summary>
/// Represents a user in the fleet management system
/// </summary>
public class User : BaseEntity
{
    /// <summary>
    /// Gets or sets the username
    /// </summary>
    public string Username { get; private set; } = string.Empty;

    /// <summary>
    /// Gets or sets the email address
    /// </summary>
    public string Email { get; private set; } = string.Empty;

    /// <summary>
    /// Gets or sets the password hash
    /// </summary>
    public string PasswordHash { get; private set; } = string.Empty;

    /// <summary>
    /// Gets or sets the first name
    /// </summary>
    public string FirstName { get; private set; } = string.Empty;

    /// <summary>
    /// Gets or sets the last name
    /// </summary>
    public string LastName { get; private set; } = string.Empty;

    /// <summary>
    /// Gets or sets the user role
    /// </summary>
    public UserRole Role { get; private set; }

    /// <summary>
    /// Gets or sets a value indicating whether the user is active
    /// </summary>
    public bool IsActive { get; private set; }

    /// <summary>
    /// Gets or sets the last login date
    /// </summary>
    public DateTime? LastLoginDate { get; private set; }

    /// <summary>
    /// Gets or sets the password reset token
    /// </summary>
    public string? PasswordResetToken { get; private set; }

    /// <summary>
    /// Gets or sets the password reset token expiry date
    /// </summary>
    public DateTime? PasswordResetTokenExpiry { get; private set; }

    /// <summary>
    /// Gets or sets the email confirmation token
    /// </summary>
    public string? EmailConfirmationToken { get; private set; }

    /// <summary>
    /// Gets or sets a value indicating whether the email is confirmed
    /// </summary>
    public bool IsEmailConfirmed { get; private set; }

    /// <summary>
    /// Gets or sets the failed login attempts count
    /// </summary>
    public int FailedLoginAttempts { get; private set; }

    /// <summary>
    /// Gets or sets the account lockout end date
    /// </summary>
    public DateTime? LockoutEnd { get; private set; }

    /// <summary>
    /// Gets the full name of the user
    /// </summary>
    public string FullName => $"{FirstName} {LastName}".Trim();

    /// <summary>
    /// Gets a value indicating whether the user account is locked out
    /// </summary>
    public bool IsLockedOut => LockoutEnd.HasValue && LockoutEnd.Value > DateTime.UtcNow;

    /// <summary>
    /// Gets a value indicating whether the password reset token is valid
    /// </summary>
    public bool IsPasswordResetTokenValid => !string.IsNullOrWhiteSpace(PasswordResetToken) && 
        PasswordResetTokenExpiry.HasValue && PasswordResetTokenExpiry.Value > DateTime.UtcNow;

    /// <summary>
    /// Private constructor for Entity Framework
    /// </summary>
    private User() { }

    /// <summary>
    /// Initializes a new instance of the <see cref="User"/> class
    /// </summary>
    /// <param name="username">The username</param>
    /// <param name="email">The email address</param>
    /// <param name="passwordHash">The password hash</param>
    /// <param name="firstName">The first name</param>
    /// <param name="lastName">The last name</param>
    /// <param name="role">The user role</param>
    public User(string username, string email, string passwordHash, string firstName, string lastName, UserRole role = UserRole.User)
    {
        if (string.IsNullOrWhiteSpace(username))
            throw new ArgumentException("Username cannot be empty", nameof(username));
        
        if (string.IsNullOrWhiteSpace(email))
            throw new ArgumentException("Email cannot be empty", nameof(email));
        
        if (string.IsNullOrWhiteSpace(passwordHash))
            throw new ArgumentException("Password hash cannot be empty", nameof(passwordHash));
        
        if (string.IsNullOrWhiteSpace(firstName))
            throw new ArgumentException("First name cannot be empty", nameof(firstName));
        
        if (string.IsNullOrWhiteSpace(lastName))
            throw new ArgumentException("Last name cannot be empty", nameof(lastName));

        if (!IsValidEmail(email))
            throw new ArgumentException("Invalid email format", nameof(email));

        Username = username;
        Email = email;
        PasswordHash = passwordHash;
        FirstName = firstName;
        LastName = lastName;
        Role = role;
        IsActive = true;
        IsEmailConfirmed = false;
        FailedLoginAttempts = 0;
    }

    /// <summary>
    /// Updates the user's basic information
    /// </summary>
    /// <param name="firstName">The first name</param>
    /// <param name="lastName">The last name</param>
    /// <param name="email">The email address</param>
    public void UpdateBasicInfo(string firstName, string lastName, string? email = null)
    {
        if (string.IsNullOrWhiteSpace(firstName))
            throw new ArgumentException("First name cannot be empty", nameof(firstName));
        
        if (string.IsNullOrWhiteSpace(lastName))
            throw new ArgumentException("Last name cannot be empty", nameof(lastName));

        FirstName = firstName;
        LastName = lastName;

        if (!string.IsNullOrWhiteSpace(email) && email != Email)
        {
            if (!IsValidEmail(email))
                throw new ArgumentException("Invalid email format", nameof(email));
            
            Email = email;
            IsEmailConfirmed = false; // Require re-confirmation for new email
        }
    }

    /// <summary>
    /// Changes the user's password
    /// </summary>
    /// <param name="newPasswordHash">The new password hash</param>
    public void ChangePassword(string newPasswordHash)
    {
        if (string.IsNullOrWhiteSpace(newPasswordHash))
            throw new ArgumentException("Password hash cannot be empty", nameof(newPasswordHash));

        PasswordHash = newPasswordHash;
        ClearPasswordResetToken();
    }

    /// <summary>
    /// Changes the user's role
    /// </summary>
    /// <param name="newRole">The new role</param>
    public void ChangeRole(UserRole newRole)
    {
        Role = newRole;
    }

    /// <summary>
    /// Activates the user account
    /// </summary>
    public void Activate()
    {
        IsActive = true;
        LockoutEnd = null;
        FailedLoginAttempts = 0;
    }

    /// <summary>
    /// Deactivates the user account
    /// </summary>
    public void Deactivate()
    {
        IsActive = false;
    }

    /// <summary>
    /// Records a successful login
    /// </summary>
    public void RecordSuccessfulLogin()
    {
        LastLoginDate = DateTime.UtcNow;
        FailedLoginAttempts = 0;
        LockoutEnd = null;
    }

    /// <summary>
    /// Records a failed login attempt
    /// </summary>
    /// <param name="maxFailedAttempts">Maximum allowed failed attempts before lockout</param>
    /// <param name="lockoutDurationMinutes">Lockout duration in minutes</param>
    public void RecordFailedLogin(int maxFailedAttempts = 5, int lockoutDurationMinutes = 30)
    {
        FailedLoginAttempts++;
        
        if (FailedLoginAttempts >= maxFailedAttempts)
        {
            LockoutEnd = DateTime.UtcNow.AddMinutes(lockoutDurationMinutes);
        }
    }

    /// <summary>
    /// Generates a password reset token
    /// </summary>
    /// <param name="token">The reset token</param>
    /// <param name="expiryHours">Token expiry in hours</param>
    public void GeneratePasswordResetToken(string token, int expiryHours = 24)
    {
        if (string.IsNullOrWhiteSpace(token))
            throw new ArgumentException("Token cannot be empty", nameof(token));

        PasswordResetToken = token;
        PasswordResetTokenExpiry = DateTime.UtcNow.AddHours(expiryHours);
    }

    /// <summary>
    /// Clears the password reset token
    /// </summary>
    public void ClearPasswordResetToken()
    {
        PasswordResetToken = null;
        PasswordResetTokenExpiry = null;
    }

    /// <summary>
    /// Generates an email confirmation token
    /// </summary>
    /// <param name="token">The confirmation token</param>
    public void GenerateEmailConfirmationToken(string token)
    {
        if (string.IsNullOrWhiteSpace(token))
            throw new ArgumentException("Token cannot be empty", nameof(token));

        EmailConfirmationToken = token;
    }

    /// <summary>
    /// Confirms the user's email address
    /// </summary>
    /// <param name="token">The confirmation token</param>
    public void ConfirmEmail(string token)
    {
        if (string.IsNullOrWhiteSpace(token))
            throw new ArgumentException("Token cannot be empty", nameof(token));

        if (EmailConfirmationToken != token)
            throw new ArgumentException("Invalid confirmation token", nameof(token));

        IsEmailConfirmed = true;
        EmailConfirmationToken = null;
    }

    /// <summary>
    /// Unlocks the user account
    /// </summary>
    public void Unlock()
    {
        LockoutEnd = null;
        FailedLoginAttempts = 0;
    }

    /// <summary>
    /// Checks if the user has a specific role or higher
    /// </summary>
    /// <param name="requiredRole">The required role</param>
    /// <returns>True if the user has the required role or higher</returns>
    public bool HasRoleOrHigher(UserRole requiredRole)
    {
        return (int)Role >= (int)requiredRole;
    }

    /// <summary>
    /// Validates if the user can perform an action based on their role
    /// </summary>
    /// <param name="requiredRole">The required role for the action</param>
    /// <returns>True if the user can perform the action</returns>
    public bool CanPerformAction(UserRole requiredRole)
    {
        return IsActive && !IsLockedOut && HasRoleOrHigher(requiredRole);
    }

    /// <summary>
    /// Validates email format
    /// </summary>
    /// <param name="email">The email to validate</param>
    /// <returns>True if valid, false otherwise</returns>
    private static bool IsValidEmail(string email)
    {
        try
        {
            var addr = new System.Net.Mail.MailAddress(email);
            return addr.Address == email;
        }
        catch
        {
            return false;
        }
    }
}
