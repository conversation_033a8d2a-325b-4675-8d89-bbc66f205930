using FleetXQ.Api.Models;
using FleetXQ.Application.Features.Authentication.Commands.LoginUser;
using FleetXQ.Application.Features.Authentication.Commands.RefreshToken;
using FleetXQ.Application.Features.Authentication.Queries.GetUserProfile;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Swashbuckle.AspNetCore.Annotations;

namespace FleetXQ.Api.Controllers;

/// <summary>
/// Authentication controller for user login, token refresh, and profile management
/// </summary>
[ApiController]
[Route("api/[controller]")]
[Produces("application/json")]
public class AuthController : BaseApiController
{
    /// <summary>
    /// Authenticates a user and returns JWT tokens
    /// </summary>
    /// <param name="request">The login request</param>
    /// <param name="cancellationToken">The cancellation token</param>
    /// <returns>Authentication result with tokens and user information</returns>
    [HttpPost("login")]
    [AllowAnonymous]
    [SwaggerOperation(
        Summary = "User login",
        Description = "Authenticates a user with username/email and password, returns JWT tokens"
    )]
    [SwaggerResponse(200, "Login successful", typeof(ApiResponse<LoginUserResult>))]
    [SwaggerResponse(400, "Invalid credentials or validation error", typeof(ApiResponse<object>))]
    [SwaggerResponse(423, "Account locked", typeof(ApiResponse<object>))]
    [SwaggerResponse(500, "Internal server error", typeof(ApiResponse<object>))]
    public async Task<ActionResult<ApiResponse<LoginUserResult>>> Login(
        [FromBody] LoginUserCommand request,
        CancellationToken cancellationToken = default)
    {
        // Add client information to the request
        request.ClientIpAddress = HttpContext.Connection.RemoteIpAddress?.ToString();
        request.UserAgent = HttpContext.Request.Headers.UserAgent.ToString();

        var result = await Mediator.Send(request, cancellationToken);

        if (!result.Success)
        {
            if (result.IsAccountLocked)
            {
                return StatusCode(423, Error(result.ErrorMessage ?? "Account is locked"));
            }

            return BadRequest(Error(result.ErrorMessage ?? "Login failed", new { RemainingAttempts = result.RemainingAttempts }));
        }

        return Ok(Success(result, "Login successful"));
    }

    /// <summary>
    /// Refreshes an access token using a refresh token
    /// </summary>
    /// <param name="request">The token refresh request</param>
    /// <param name="cancellationToken">The cancellation token</param>
    /// <returns>New JWT tokens</returns>
    [HttpPost("refresh")]
    [AllowAnonymous]
    [SwaggerOperation(
        Summary = "Refresh access token",
        Description = "Refreshes an expired access token using a valid refresh token"
    )]
    [SwaggerResponse(200, "Token refresh successful", typeof(ApiResponse<RefreshTokenResult>))]
    [SwaggerResponse(400, "Invalid or expired refresh token", typeof(ApiResponse<object>))]
    [SwaggerResponse(401, "Unauthorized", typeof(ApiResponse<object>))]
    [SwaggerResponse(500, "Internal server error", typeof(ApiResponse<object>))]
    public async Task<ActionResult<ApiResponse<RefreshTokenResult>>> RefreshToken(
        [FromBody] RefreshTokenCommand request,
        CancellationToken cancellationToken = default)
    {
        // Add client information to the request
        request.ClientIpAddress = HttpContext.Connection.RemoteIpAddress?.ToString();
        request.UserAgent = HttpContext.Request.Headers.UserAgent.ToString();

        var result = await Mediator.Send(request, cancellationToken);

        if (!result.Success)
        {
            return Unauthorized(Error(result.ErrorMessage ?? "Token refresh failed"));
        }

        return Ok(Success(result, "Token refreshed successfully"));
    }

    /// <summary>
    /// Gets the current user's profile information
    /// </summary>
    /// <param name="cancellationToken">The cancellation token</param>
    /// <returns>User profile information</returns>
    [HttpGet("profile")]
    [Authorize]
    [SwaggerOperation(
        Summary = "Get user profile",
        Description = "Retrieves the authenticated user's profile information"
    )]
    [SwaggerResponse(200, "Profile retrieved successfully", typeof(ApiResponse<GetUserProfileResult>))]
    [SwaggerResponse(401, "Unauthorized", typeof(ApiResponse<object>))]
    [SwaggerResponse(404, "User not found", typeof(ApiResponse<object>))]
    [SwaggerResponse(500, "Internal server error", typeof(ApiResponse<object>))]
    public async Task<ActionResult<ApiResponse<GetUserProfileResult>>> GetProfile(
        CancellationToken cancellationToken = default)
    {
        if (!CurrentUserId.HasValue)
        {
            return Unauthorized(Error("User ID not found in token"));
        }

        var query = new GetUserProfileQuery
        {
            UserId = CurrentUserId.Value,
            RequestingUserId = CurrentUserId.Value,
            IncludeSensitiveInfo = true // User can see their own sensitive info
        };

        var result = await Mediator.Send(query, cancellationToken);

        if (!result.Success)
        {
            return NotFound(Error(result.ErrorMessage ?? "User profile not found"));
        }

        return Ok(Success(result, "Profile retrieved successfully"));
    }

    /// <summary>
    /// Gets another user's profile information (Admin/Manager only)
    /// </summary>
    /// <param name="userId">The user ID to retrieve</param>
    /// <param name="cancellationToken">The cancellation token</param>
    /// <returns>User profile information</returns>
    [HttpGet("profile/{userId:guid}")]
    [Authorize(Roles = "Admin,Manager")]
    [SwaggerOperation(
        Summary = "Get user profile by ID",
        Description = "Retrieves a user's profile information (Admin/Manager only)"
    )]
    [SwaggerResponse(200, "Profile retrieved successfully", typeof(ApiResponse<GetUserProfileResult>))]
    [SwaggerResponse(401, "Unauthorized", typeof(ApiResponse<object>))]
    [SwaggerResponse(403, "Forbidden - insufficient permissions", typeof(ApiResponse<object>))]
    [SwaggerResponse(404, "User not found", typeof(ApiResponse<object>))]
    [SwaggerResponse(500, "Internal server error", typeof(ApiResponse<object>))]
    public async Task<ActionResult<ApiResponse<GetUserProfileResult>>> GetUserProfile(
        [FromRoute] Guid userId,
        CancellationToken cancellationToken = default)
    {
        if (!CurrentUserId.HasValue)
        {
            return Unauthorized(Error("User ID not found in token"));
        }

        var query = new GetUserProfileQuery
        {
            UserId = userId,
            RequestingUserId = CurrentUserId.Value,
            IncludeSensitiveInfo = HasRole("Admin") // Only admins can see sensitive info of other users
        };

        var result = await Mediator.Send(query, cancellationToken);

        if (!result.Success)
        {
            return NotFound(Error(result.ErrorMessage ?? "User profile not found"));
        }

        return Ok(Success(result, "Profile retrieved successfully"));
    }

    /// <summary>
    /// Logs out the current user (placeholder for future implementation)
    /// </summary>
    /// <returns>Logout confirmation</returns>
    [HttpPost("logout")]
    [Authorize]
    [SwaggerOperation(
        Summary = "User logout",
        Description = "Logs out the current user (token invalidation would be implemented here)"
    )]
    [SwaggerResponse(200, "Logout successful", typeof(ApiResponse<object>))]
    [SwaggerResponse(401, "Unauthorized", typeof(ApiResponse<object>))]
    public ActionResult<ApiResponse<object>> Logout()
    {
        // In a full implementation, you would:
        // 1. Add the token to a blacklist
        // 2. Clear any server-side session data
        // 3. Log the logout event
        
        return Ok(Success("Logout successful"));
    }
}
