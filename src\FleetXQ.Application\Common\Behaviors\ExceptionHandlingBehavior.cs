using MediatR;
using Microsoft.Extensions.Logging;

namespace FleetXQ.Application.Common.Behaviors;

/// <summary>
/// Pipeline behavior for centralized exception handling
/// </summary>
/// <typeparam name="TRequest">The request type</typeparam>
/// <typeparam name="TResponse">The response type</typeparam>
public sealed class ExceptionHandlingBehavior<TRequest, TResponse> : IPipelineBehavior<TRequest, TResponse>
    where TRequest : notnull
{
    private readonly ILogger<ExceptionHandlingBehavior<TRequest, TResponse>> _logger;

    /// <summary>
    /// Initializes a new instance of the <see cref="ExceptionHandlingBehavior{TRequest, TResponse}"/> class
    /// </summary>
    /// <param name="logger">The logger</param>
    public ExceptionHandlingBehavior(ILogger<ExceptionHandlingBehavior<TRequest, TResponse>> logger)
    {
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
    }

    /// <summary>
    /// Handles the request with exception handling
    /// </summary>
    /// <param name="request">The request</param>
    /// <param name="next">The next handler in the pipeline</param>
    /// <param name="cancellationToken">The cancellation token</param>
    /// <returns>The response</returns>
    public async Task<TResponse> Handle(TRequest request, RequestHandlerDelegate<TResponse> next, CancellationToken cancellationToken)
    {
        var requestName = typeof(TRequest).Name;

        try
        {
            return await next();
        }
        catch (ValidationException ex)
        {
            _logger.LogWarning("Validation failed for {RequestName}: {ValidationErrors}", 
                requestName, string.Join(", ", ex.Errors.SelectMany(e => e.Value)));
            
            // Re-throw validation exceptions as they should be handled by the API layer
            throw;
        }
        catch (UnauthorizedAccessException ex)
        {
            _logger.LogWarning("Unauthorized access attempt for {RequestName}: {Message}", 
                requestName, ex.Message);
            
            throw;
        }
        catch (ArgumentException ex)
        {
            _logger.LogWarning("Invalid argument for {RequestName}: {Message}", 
                requestName, ex.Message);
            
            throw;
        }
        catch (InvalidOperationException ex)
        {
            _logger.LogWarning("Invalid operation for {RequestName}: {Message}", 
                requestName, ex.Message);
            
            throw;
        }
        catch (OperationCanceledException ex) when (cancellationToken.IsCancellationRequested)
        {
            _logger.LogInformation("Request {RequestName} was cancelled", requestName);
            
            throw;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Unhandled exception occurred while processing {RequestName}", requestName);
            
            // In a production environment, you might want to:
            // 1. Send error notifications to monitoring systems
            // 2. Collect additional diagnostic information
            // 3. Return a generic error response instead of re-throwing
            
            throw new ApplicationException($"An error occurred while processing {requestName}", ex);
        }
    }
}

/// <summary>
/// Custom application exception for business logic errors
/// </summary>
public sealed class ApplicationException : Exception
{
    /// <summary>
    /// Gets the error code
    /// </summary>
    public string? ErrorCode { get; }

    /// <summary>
    /// Gets additional error details
    /// </summary>
    public Dictionary<string, object>? Details { get; }

    /// <summary>
    /// Initializes a new instance of the <see cref="ApplicationException"/> class
    /// </summary>
    /// <param name="message">The error message</param>
    public ApplicationException(string message) : base(message)
    {
    }

    /// <summary>
    /// Initializes a new instance of the <see cref="ApplicationException"/> class
    /// </summary>
    /// <param name="message">The error message</param>
    /// <param name="innerException">The inner exception</param>
    public ApplicationException(string message, Exception innerException) : base(message, innerException)
    {
    }

    /// <summary>
    /// Initializes a new instance of the <see cref="ApplicationException"/> class
    /// </summary>
    /// <param name="message">The error message</param>
    /// <param name="errorCode">The error code</param>
    /// <param name="details">Additional error details</param>
    public ApplicationException(string message, string errorCode, Dictionary<string, object>? details = null) : base(message)
    {
        ErrorCode = errorCode;
        Details = details;
    }

    /// <summary>
    /// Initializes a new instance of the <see cref="ApplicationException"/> class
    /// </summary>
    /// <param name="message">The error message</param>
    /// <param name="errorCode">The error code</param>
    /// <param name="innerException">The inner exception</param>
    /// <param name="details">Additional error details</param>
    public ApplicationException(string message, string errorCode, Exception innerException, Dictionary<string, object>? details = null) 
        : base(message, innerException)
    {
        ErrorCode = errorCode;
        Details = details;
    }
}
